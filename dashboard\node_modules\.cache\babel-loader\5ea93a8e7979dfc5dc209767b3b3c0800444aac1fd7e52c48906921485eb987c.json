{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\shade_cms\\\\dashboard\\\\src\\\\features\\\\Resources\\\\components\\\\websiteComponent\\\\Home.jsx\",\n  _s = $RefreshSig$();\nimport { useEffect, useRef, useState } from \"react\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport Arrow from \"../../../../assets/icons/right-wrrow.svg\";\n// import AboutUs from \"../../../../assets/images/aboutus.png\";\n// import background from \"../../../../assets/images/Hero.png\";\nimport highlightsvg from \"../../../../assets/highlight.svg\";\nimport { recentProjects, markets, safety, testimonials } from \"../../../../assets/index\";\nimport { Swiper, SwiperSlide } from \"swiper/react\";\nimport { Pagination, Navigation, Autoplay, EffectCoverflow } from \"swiper/modules\";\nimport \"swiper/css/navigation\";\nimport \"swiper/css\";\nimport \"swiper/css/pagination\";\nimport blankImage from \"../../../../assets/images/blankImage.webp\";\nimport { TruncateText } from \"../../../../app/capitalizeword\";\nimport dynamicSize, { generatefontSize } from \"../../../../app/fontSizes\";\nimport { differentText } from \"../../../../app/fontSizes\";\n// import contentJSON from './content.json'\nimport { Img_url } from \"../../../../routes/backend\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction defineDevice(screen) {\n  if (screen > 900) {\n    return \"computer\";\n  } else if (screen < 900 && screen > 550) {\n    return 'tablet';\n  } else {\n    return 'phone';\n  }\n}\nconst HomePage = ({\n  language,\n  screen,\n  fullScreen,\n  highlight,\n  content,\n  currentContent,\n  liveContent,\n  width\n}) => {\n  _s();\n  var _differentText$checkD, _content$, _content$$sections, _content$$sections$ac, _content$2, _content$2$content, _content$2$content$im, _content$2$content$im2, _content$3, _content$3$content, _content$3$content$im, _content$3$content$im2, _content$4, _content$4$content, _liveContent$, _liveContent$$content, _content$5, _content$5$content, _content$6, _content$6$content, _liveContent$2, _liveContent$2$conten, _content$7, _content$7$content, _content$7$content$de, _content$8, _content$8$content, _content$8$content$bu, _content$8$content$bu2, _content$8$content$bu3, _liveContent$3, _liveContent$3$conten, _liveContent$3$conten2, _liveContent$3$conten3, _liveContent$3$conten4, _content$9, _content$9$content, _content$9$content$bu, _content$9$content$bu2, _content$9$content$bu3, _content$10, _content$10$content, _content$10$content$i, _content$10$content$i2, _content$11, _content$11$content, _content$11$content$t, _liveContent$4, _liveContent$4$conten, _liveContent$4$conten2, _content$12, _content$12$content, _content$12$content$t, _content$13, _content$13$content, _content$13$content$d, _liveContent$5, _liveContent$5$conten, _liveContent$5$conten2, _content$14, _content$14$content, _content$14$content$d, _content$15, _content$15$content, _content$15$content$d, _liveContent$6, _liveContent$6$conten, _liveContent$6$conten2, _content$16, _content$16$content, _content$16$content$b, _content$16$content$b2, _content$16$content$b3, _content$17, _content$17$content, _liveContent$7, _liveContent$7$conten, _liveContent$7$conten2, _content$18, _content$18$content, _content$18$content$t, _content$19, _liveContent$8, _content$20, _content$20$items, _content$21, _content$21$content, _content$21$content$c, _content$22, _content$22$content, _content$22$content$t, _liveContent$9, _liveContent$9$conten, _liveContent$9$conten2, _content$23, _content$23$content, _content$23$content$t, _content$24, _content$24$content, _content$24$content$d, _liveContent$10, _liveContent$10$conte, _liveContent$10$conte2, _content$25, _content$25$content, _content$25$content$d, _content$26, _content$26$content, _content$26$content$b, _content$26$content$b2, _content$26$content$b3, _liveContent$11, _liveContent$11$conte, _liveContent$11$conte2, _liveContent$11$conte3, _liveContent$11$conte4, _content$27, _content$27$content, _content$27$content$b, _content$27$content$b2, _content$27$content$b3, _currentContent$, _currentContent$$butt, _currentContent$$butt2, _currentContent$$butt3, _content$28, _content$28$sections, _currentContent$2, _currentContent$2$but, _currentContent$2$but2, _currentContent$2$but3, _currentContent$3, _currentContent$3$but, _currentContent$3$but2, _currentContent$3$but3, _content$29, _content$29$content, _content$29$content$t, _liveContent$12, _liveContent$12$conte, _liveContent$12$conte2, _content$30, _content$30$content, _content$30$content$t, _content$31, _content$31$content, _content$31$content$d, _liveContent$13, _liveContent$13$conte, _liveContent$13$conte2, _content$32, _content$32$content, _content$32$content$d, _content$33, _content$33$content, _content$33$content$c, _content$34, _content$34$content, _content$34$content$t, _content$35, _content$35$items, _content$36, _content$36$items, _content$37, _content$37$content, _content$37$content$t, _content$38, _content$38$content, _content$38$content$d, _content$39, _content$39$content, _content$39$content$b, _content$39$content$b2, _content$39$content$b3;\n  // const dispatch = useDispatch()\n  const checkDifference = highlight ? differentText === null || differentText === void 0 ? void 0 : (_differentText$checkD = differentText.checkDifference) === null || _differentText$checkD === void 0 ? void 0 : _differentText$checkD.bind(differentText) : () => \"\";\n  const isComputer = screen > 900;\n  const isTablet = screen < 900 && screen > 730;\n  const isPhone = screen < 450;\n  const ImagesFromRedux = useSelector(state => {\n    var _state$homeContent, _state$homeContent$pr;\n    return state === null || state === void 0 ? void 0 : (_state$homeContent = state.homeContent) === null || _state$homeContent === void 0 ? void 0 : (_state$homeContent$pr = _state$homeContent.present) === null || _state$homeContent$pr === void 0 ? void 0 : _state$homeContent$pr.images;\n  });\n  const fontLight = useSelector(state => state.fontStyle.light);\n  // const platform = useSelector(state => state.platform.platform)\n  const [swiperInstance, setSwiperInstance] = useState(null);\n  let isLeftAlign = language === \"en\";\n  let textAlignment = isLeftAlign ? \"text-left\" : \"text-right\";\n  const titleLan = isLeftAlign ? \"titleEn\" : \"titleAr\";\n  const prevRef = useRef(null);\n  const nextRef = useRef(null);\n  const [activeRecentProjectSection, setActiveRecentProjectSection] = useState(0);\n  let chunkArray = (array, chunkSize) => {\n    const chunks = [];\n    for (let i = 0; i < (array === null || array === void 0 ? void 0 : array.length); i += chunkSize) {\n      chunks === null || chunks === void 0 ? void 0 : chunks.push(array === null || array === void 0 ? void 0 : array.slice(i, i + chunkSize));\n    }\n    return chunks;\n  };\n  const projectsPerSlide = 4;\n  let projectChunks = chunkArray((content === null || content === void 0 ? void 0 : (_content$ = content[\"5\"]) === null || _content$ === void 0 ? void 0 : (_content$$sections = _content$.sections) === null || _content$$sections === void 0 ? void 0 : (_content$$sections$ac = _content$$sections[activeRecentProjectSection]) === null || _content$$sections$ac === void 0 ? void 0 : _content$$sections$ac.items) || [], projectsPerSlide);\n  const ProjectSlider = {\n    ...recentProjects,\n    ...markets,\n    ...safety\n  };\n  const fontSize = generatefontSize(defineDevice(screen), dynamicSize, width);\n  const scrollRef = useRef(null);\n  useEffect(() => {\n    if (swiperInstance) {\n      swiperInstance === null || swiperInstance === void 0 ? void 0 : swiperInstance.update();\n    }\n  }, [language]);\n  useEffect(() => {\n    var _container$firstChild;\n    const container = scrollRef.current;\n    if (!container) return;\n    const children = (_container$firstChild = container.firstChild) === null || _container$firstChild === void 0 ? void 0 : _container$firstChild.children;\n    if (!children || children.length === 0) return;\n    let index = 0;\n    const interval = setInterval(() => {\n      if (index >= children.length) index = 0;\n      const child = children[index];\n      if (child) {\n        container.scrollTo({\n          left: child.offsetLeft - 64,\n          // Adjust for padding (`px-16`)\n          behavior: \"smooth\"\n        });\n      }\n      index++;\n    }, 3000); // every 3 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n  const testimonialPrevRef = useRef(null);\n  const testimonialNextRef = useRef(null);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `w-full relative ${textAlignment} bankgothic-medium-dt bg-[white]`,\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"w-full relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `w-full overflow-y-hidden min-h-[400px] block ${language === \"en\" ? \"scale-x-100\" : \"scale-x-[-1]\"}`,\n        style: {\n          height: dynamicSize(715, width)\n        },\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          dir: isLeftAlign ? \"ltr\" : \"rtl\",\n          src: `${Img_url}${content === null || content === void 0 ? void 0 : (_content$2 = content[\"1\"]) === null || _content$2 === void 0 ? void 0 : (_content$2$content = _content$2.content) === null || _content$2$content === void 0 ? void 0 : (_content$2$content$im = _content$2$content.images) === null || _content$2$content$im === void 0 ? void 0 : (_content$2$content$im2 = _content$2$content$im[0]) === null || _content$2$content$im2 === void 0 ? void 0 : _content$2$content$im2.url}`,\n          alt: content === null || content === void 0 ? void 0 : (_content$3 = content[\"1\"]) === null || _content$3 === void 0 ? void 0 : (_content$3$content = _content$3.content) === null || _content$3$content === void 0 ? void 0 : (_content$3$content$im = _content$3$content.images) === null || _content$3$content$im === void 0 ? void 0 : (_content$3$content$im2 = _content$3$content$im[0]) === null || _content$3$content$im2 === void 0 ? void 0 : _content$3$content$im2.url,\n          className: \"w-full object-cover\",\n          style: {\n            objectPosition: \"center\",\n            transform: \"scaleX(-1)\",\n            height: isTablet ? \"500px\" : isPhone && \"500px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `container mx-auto absolute ${isComputer ? \"top-[20%]\" : \"top-16\"}  left-0 right-0 px-4`,\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `text-left flex flex-col ${language === \"en\" ? \"items-start\" : \"items-end\"} ${textAlignment} ${isPhone ? \"px-[0px] py-10\" : \"px-[80px]\"}`,\n          style: {\n            paddingLeft: isComputer && dynamicSize(140, width)\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: `${checkDifference(content === null || content === void 0 ? void 0 : (_content$4 = content[\"1\"]) === null || _content$4 === void 0 ? void 0 : (_content$4$content = _content$4.content) === null || _content$4$content === void 0 ? void 0 : _content$4$content.title[language], liveContent === null || liveContent === void 0 ? void 0 : (_liveContent$ = liveContent[\"1\"]) === null || _liveContent$ === void 0 ? void 0 : (_liveContent$$content = _liveContent$.content) === null || _liveContent$$content === void 0 ? void 0 : _liveContent$$content.title[language])} text-[#292E3D] text-[45px] tracking-[0px]  leading-[2.5rem] capitalize font-[500] mb-4 ${isPhone ? \"w-full\" : fullScreen ? \"w-3/5\" : \"w-3/5\"}  `,\n            style: {\n              fontSize: fontSize === null || fontSize === void 0 ? void 0 : fontSize.mainHeading,\n              lineHeight: isComputer && `${width / 1526 * 4.5}rem`\n            },\n            children: content === null || content === void 0 ? void 0 : (_content$5 = content[\"1\"]) === null || _content$5 === void 0 ? void 0 : (_content$5$content = _content$5.content) === null || _content$5$content === void 0 ? void 0 : _content$5$content.title[language]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: `${checkDifference(content === null || content === void 0 ? void 0 : (_content$6 = content[\"1\"]) === null || _content$6 === void 0 ? void 0 : (_content$6$content = _content$6.content) === null || _content$6$content === void 0 ? void 0 : _content$6$content.description[language], liveContent === null || liveContent === void 0 ? void 0 : (_liveContent$2 = liveContent[\"1\"]) === null || _liveContent$2 === void 0 ? void 0 : (_liveContent$2$conten = _liveContent$2.content) === null || _liveContent$2$conten === void 0 ? void 0 : _liveContent$2$conten.description[language])} text-[#0e172fb3]  leading-[16px] mb-6 ${isPhone ? \"w-full text-[12px]\" : \"w-1/2 text-[10px]\"} tracking-[0px]`,\n            style: {\n              fontSize: fontSize === null || fontSize === void 0 ? void 0 : fontSize.mainPara,\n              lineHeight: isComputer && `${width / 1526 * 24}px`\n            },\n            children: content === null || content === void 0 ? void 0 : (_content$7 = content[\"1\"]) === null || _content$7 === void 0 ? void 0 : (_content$7$content = _content$7.content) === null || _content$7$content === void 0 ? void 0 : (_content$7$content$de = _content$7$content.description) === null || _content$7$content$de === void 0 ? void 0 : _content$7$content$de[language]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `relative items-center flex ${isLeftAlign ? \"\" : \"flex-row-reverse\"} gap-2 text-[12px] font-medium px-[12px] py-[6px] px-[12px] bg-[#00b9f2] text-white rounded-md`,\n            style: {\n              fontSize: fontSize === null || fontSize === void 0 ? void 0 : fontSize.mainButton\n            },\n            onClick: () => {},\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: `${checkDifference(content === null || content === void 0 ? void 0 : (_content$8 = content[\"1\"]) === null || _content$8 === void 0 ? void 0 : (_content$8$content = _content$8.content) === null || _content$8$content === void 0 ? void 0 : (_content$8$content$bu = _content$8$content.button) === null || _content$8$content$bu === void 0 ? void 0 : (_content$8$content$bu2 = _content$8$content$bu[0]) === null || _content$8$content$bu2 === void 0 ? void 0 : (_content$8$content$bu3 = _content$8$content$bu2.text) === null || _content$8$content$bu3 === void 0 ? void 0 : _content$8$content$bu3[language], liveContent === null || liveContent === void 0 ? void 0 : (_liveContent$3 = liveContent[\"1\"]) === null || _liveContent$3 === void 0 ? void 0 : (_liveContent$3$conten = _liveContent$3.content) === null || _liveContent$3$conten === void 0 ? void 0 : (_liveContent$3$conten2 = _liveContent$3$conten.button) === null || _liveContent$3$conten2 === void 0 ? void 0 : (_liveContent$3$conten3 = _liveContent$3$conten2[0]) === null || _liveContent$3$conten3 === void 0 ? void 0 : (_liveContent$3$conten4 = _liveContent$3$conten3.text) === null || _liveContent$3$conten4 === void 0 ? void 0 : _liveContent$3$conten4[language])}`,\n              children: content === null || content === void 0 ? void 0 : (_content$9 = content[\"1\"]) === null || _content$9 === void 0 ? void 0 : (_content$9$content = _content$9.content) === null || _content$9$content === void 0 ? void 0 : (_content$9$content$bu = _content$9$content.button) === null || _content$9$content$bu === void 0 ? void 0 : (_content$9$content$bu2 = _content$9$content$bu[0]) === null || _content$9$content$bu2 === void 0 ? void 0 : (_content$9$content$bu3 = _content$9$content$bu2.text) === null || _content$9$content$bu3 === void 0 ? void 0 : _content$9$content$bu3[language]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n              src: Arrow,\n              width: \"10\",\n              height: \"11\",\n              alt: \"\",\n              style: {\n                transform: isLeftAlign ? \"rotate(180deg)\" : \"\",\n                width: isComputer && dynamicSize(16, width)\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: ` ${isPhone ? \"px-2 py-[60px]\" : isTablet ? \"px-[80px] py-[120px]\" : \"px-[150px] py-[120px]\"} ${language === \"en\" ? \"\" : \" direction-rtl\"} items-start`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `relative container mx-auto flex ${isPhone ? \"flex-col\" : \"\"} ${isLeftAlign ? \"\" : \"flex-row-reverse\"} items-center`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${isPhone ? \"w-[90%]\" : \"w-[70%]\"} h-[500px] overflow-hidden rounded-sm shadow-lg `,\n          style: {\n            height: isComputer && dynamicSize(629, width),\n            width: isComputer && dynamicSize(877, width)\n          },\n          children: /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `${Img_url}${content === null || content === void 0 ? void 0 : (_content$10 = content[\"2\"]) === null || _content$10 === void 0 ? void 0 : (_content$10$content = _content$10.content) === null || _content$10$content === void 0 ? void 0 : (_content$10$content$i = _content$10$content.images) === null || _content$10$content$i === void 0 ? void 0 : (_content$10$content$i2 = _content$10$content$i[0]) === null || _content$10$content$i2 === void 0 ? void 0 : _content$10$content$i2.url}`,\n            alt: \"about-us\",\n            className: \"w-full h-[500px] object-cover\",\n            style: {\n              width: isComputer && dynamicSize(877, width),\n              height: isComputer && '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 168,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex flex-col items-start ${isPhone ? \" \" : \"absolute \"} ${isLeftAlign ? \"right-0 text-left\" : \"left-0 text-right\"} bg-[#145098] ${isTablet ? \"p-10 py-14\" : \"p-14 py-20\"} rounded-sm w-[23rem]`,\n          style: {\n            gap: isComputer ? dynamicSize(26, width) : \"16px\",\n            width: isComputer && dynamicSize(488, width),\n            padding: isComputer && `${dynamicSize(98, width)} ${dynamicSize(65, width)}`\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: `text-white text-[28px] leading-[1.8rem]  font-normal ${checkDifference(content === null || content === void 0 ? void 0 : (_content$11 = content[\"2\"]) === null || _content$11 === void 0 ? void 0 : (_content$11$content = _content$11.content) === null || _content$11$content === void 0 ? void 0 : (_content$11$content$t = _content$11$content.title) === null || _content$11$content$t === void 0 ? void 0 : _content$11$content$t[language], liveContent === null || liveContent === void 0 ? void 0 : (_liveContent$4 = liveContent[\"2\"]) === null || _liveContent$4 === void 0 ? void 0 : (_liveContent$4$conten = _liveContent$4.content) === null || _liveContent$4$conten === void 0 ? void 0 : (_liveContent$4$conten2 = _liveContent$4$conten.title) === null || _liveContent$4$conten2 === void 0 ? void 0 : _liveContent$4$conten2[language])}`,\n            style: {\n              fontSize: isComputer && dynamicSize(36, width),\n              lineHeight: isComputer && dynamicSize(32, width)\n            },\n            children: content === null || content === void 0 ? void 0 : (_content$12 = content[\"2\"]) === null || _content$12 === void 0 ? void 0 : (_content$12$content = _content$12.content) === null || _content$12$content === void 0 ? void 0 : (_content$12$content$t = _content$12$content.title) === null || _content$12$content$t === void 0 ? void 0 : _content$12$content$t[language]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `text-white font-[100] text-[12px] leading-[16px] ${checkDifference(content === null || content === void 0 ? void 0 : (_content$13 = content[\"2\"]) === null || _content$13 === void 0 ? void 0 : (_content$13$content = _content$13.content) === null || _content$13$content === void 0 ? void 0 : (_content$13$content$d = _content$13$content.description) === null || _content$13$content$d === void 0 ? void 0 : _content$13$content$d[language], liveContent === null || liveContent === void 0 ? void 0 : (_liveContent$5 = liveContent[\"2\"]) === null || _liveContent$5 === void 0 ? void 0 : (_liveContent$5$conten = _liveContent$5.content) === null || _liveContent$5$conten === void 0 ? void 0 : (_liveContent$5$conten2 = _liveContent$5$conten.description) === null || _liveContent$5$conten2 === void 0 ? void 0 : _liveContent$5$conten2[language])}`,\n            style: {\n              fontSize: isComputer && dynamicSize(15, width),\n              lineHeight: isComputer && dynamicSize(26, width)\n            },\n            dangerouslySetInnerHTML: {\n              __html: content === null || content === void 0 ? void 0 : (_content$14 = content[\"2\"]) === null || _content$14 === void 0 ? void 0 : (_content$14$content = _content$14.content) === null || _content$14$content === void 0 ? void 0 : (_content$14$content$d = _content$14$content.description) === null || _content$14$content$d === void 0 ? void 0 : _content$14$content$d[language]\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `px-[6px] py-[2px] bg-[#00B9F2] text-white text-[12px] ${checkDifference(content === null || content === void 0 ? void 0 : (_content$15 = content[\"2\"]) === null || _content$15 === void 0 ? void 0 : (_content$15$content = _content$15.content) === null || _content$15$content === void 0 ? void 0 : (_content$15$content$d = _content$15$content.description) === null || _content$15$content$d === void 0 ? void 0 : _content$15$content$d[language], liveContent === null || liveContent === void 0 ? void 0 : (_liveContent$6 = liveContent[\"2\"]) === null || _liveContent$6 === void 0 ? void 0 : (_liveContent$6$conten = _liveContent$6.content) === null || _liveContent$6$conten === void 0 ? void 0 : (_liveContent$6$conten2 = _liveContent$6$conten.description) === null || _liveContent$6$conten2 === void 0 ? void 0 : _liveContent$6$conten2[language])} rounded-md hover:bg-opacity-90 text-right`,\n            style: {\n              fontSize: isComputer && dynamicSize(18, width)\n            },\n            children: content === null || content === void 0 ? void 0 : (_content$16 = content[\"2\"]) === null || _content$16 === void 0 ? void 0 : (_content$16$content = _content$16.content) === null || _content$16$content === void 0 ? void 0 : (_content$16$content$b = _content$16$content.button) === null || _content$16$content$b === void 0 ? void 0 : (_content$16$content$b2 = _content$16$content$b[0]) === null || _content$16$content$b2 === void 0 ? void 0 : (_content$16$content$b3 = _content$16$content$b2.text) === null || _content$16$content$b3 === void 0 ? void 0 : _content$16$content$b3[language]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 163,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"py-10 bg-gray-100\",\n      style: {\n        wordBreak: \"normal\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto px-6\",\n        style: {\n          padding: isComputer && `${dynamicSize(44, width)} ${dynamicSize(220, width)}`\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: `text-center text-3xl font-light text-[#292E3D] mb-9 ${isPhone ? \"text-[30px]\" : \"text-[40px]\"}\n                    ${checkDifference(content === null || content === void 0 ? void 0 : (_content$17 = content[\"3\"]) === null || _content$17 === void 0 ? void 0 : (_content$17$content = _content$17.content) === null || _content$17$content === void 0 ? void 0 : _content$17$content.title[language], liveContent === null || liveContent === void 0 ? void 0 : (_liveContent$7 = liveContent[\"3\"]) === null || _liveContent$7 === void 0 ? void 0 : (_liveContent$7$conten = _liveContent$7.content) === null || _liveContent$7$conten === void 0 ? void 0 : (_liveContent$7$conten2 = _liveContent$7$conten.title) === null || _liveContent$7$conten2 === void 0 ? void 0 : _liveContent$7$conten2[language])}\n                    `,\n          style: {\n            fontSize: isComputer && dynamicSize(36, width)\n          },\n          children: content === null || content === void 0 ? void 0 : (_content$18 = content[\"3\"]) === null || _content$18 === void 0 ? void 0 : (_content$18$content = _content$18.content) === null || _content$18$content === void 0 ? void 0 : (_content$18$content$t = _content$18$content.title) === null || _content$18$content$t === void 0 ? void 0 : _content$18$content$t[language]\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${isPhone ? \"flex gap-4 flex-col\" : \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:gap-12 sm:gap-6\"}\n                    ${checkDifference(content === null || content === void 0 ? void 0 : (_content$19 = content[\"3\"]) === null || _content$19 === void 0 ? void 0 : _content$19.items, liveContent === null || liveContent === void 0 ? void 0 : (_liveContent$8 = liveContent[\"3\"]) === null || _liveContent$8 === void 0 ? void 0 : _liveContent$8.items)}\n                    `,\n          style: {\n            columnGap: isComputer && dynamicSize(96, width),\n            rowGap: isComputer && dynamicSize(48, width)\n          },\n          children: content === null || content === void 0 ? void 0 : (_content$20 = content['3']) === null || _content$20 === void 0 ? void 0 : (_content$20$items = _content$20.items) === null || _content$20$items === void 0 ? void 0 : _content$20$items.map((card, key) => {\n            var _card$liveModeVersion;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-full h-44 flex items-center justify-center p-6 rounded-md transition-transform duration-300 hover:scale-105 cursor-pointer ${key % 2 !== 0 ? \"bg-blue-900 text-[white]\" : \" bg-stone-200\"} `,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-center gap-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: Img_url + (card === null || card === void 0 ? void 0 : (_card$liveModeVersion = card.liveModeVersionData) === null || _card$liveModeVersion === void 0 ? void 0 : _card$liveModeVersion.icon),\n                  width: 40,\n                  height: 40,\n                  alt: \"Icon\",\n                  className: \"h-10 w-10\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: `relative text-lg font-light text-center `,\n                  style: {\n                    fontSize: isComputer && dynamicSize(20, width)\n                  },\n                  children: [card === null || card === void 0 ? void 0 : card[titleLan], /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"block h-[2px] w-16 bg-gray-300 mt-2 mx-auto\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 216,\n                    columnNumber: 45\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 41\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 37\n              }, this)\n            }, key, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 33\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: `py-[115px] overflow-hidden ${isComputer ? fullScreen ? \"px-20 pt-40 pb-60\" : \"px-20 pb-60\" : !isLeftAlign ? \"px-8\" : \"px-10\"}`,\n      dir: isLeftAlign ? 'ltr' : \"rtl\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `container mx-auto flex ${isPhone ? \"flex-col gap-[350px]\" : \"gap-10\"} `,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `w-[100%]  flex-[4]`,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `relative \n                        ${checkDifference()}\n                        ${isTablet ? !isLeftAlign ? \"left-[-70px]\" : \"left-[15px]\" : isComputer && fullScreen ? \"left-[450px] scale-[1.7]\" : isPhone ? screen < 370 ? \"left-[-10px] scale-[.6]\" : \"left-[0px] scale-[1]\" : \"left-[50px] scale-[1.2]\"} \n                            ${!isLeftAlign && isPhone && \"left-[-310px]\"}`\n            // style={{ width: isComputer && dynamicSize(200, width) }}\n            ,\n            children: content === null || content === void 0 ? void 0 : (_content$21 = content[\"4\"]) === null || _content$21 === void 0 ? void 0 : (_content$21$content = _content$21.content) === null || _content$21$content === void 0 ? void 0 : (_content$21$content$c = _content$21$content.cards) === null || _content$21$content$c === void 0 ? void 0 : _content$21$content$c.map((item, key) => {\n              // Set top position based on whether key is odd or even\n              const topValue = Math.floor(key / 2) * 140 + (key % 2 !== 0 ? -35 : 25); // Odd = move up, Even = move down\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  top: `${topValue}px`,\n                  zIndex: key + 1\n                },\n                className: `w-[180px] absolute rounded-md bg-white shadow-lg p-4 ${key % 2 !== 0 ? !isLeftAlign ? \"left-[170px]\" : \"xl:left-[150px]\" : \"left-0\"}`,\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    className: `absolute ${key % 2 === 1 ? \"top-[-22px] right-[-32px]\" : \"left-[-36px] top-[-27px]\"}`,\n                    src: Img_url + (item === null || item === void 0 ? void 0 : item.icon),\n                    width: 40,\n                    height: key === 1 ? 47 : 60,\n                    alt: \"\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 45\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-[#292E3D] text-2xl font-semibold pl-2 font-sans\",\n                  style: {\n                    fontSize: isComputer && dynamicSize(40, width)\n                  },\n                  children: item === null || item === void 0 ? void 0 : item.count\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 41\n                }, this), /*#__PURE__*/_jsxDEV(\"h5\", {\n                  className: `text-[#292E3D] text-xs font-light relative before:absolute ${isLeftAlign ? \"before:left-[-10px]\" : \"before:right-[-10px]\"} before:top-0 before:w-[3px] before:h-[18px] before:bg-[#F9995D]`,\n                  style: {\n                    fontSize: isComputer && dynamicSize(12, width)\n                  },\n                  children: item === null || item === void 0 ? void 0 : item.title[language]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 41\n                }, this)]\n              }, key, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 37\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `max-w-[450xp] ${isTablet ? !isLeftAlign ? \"pr-[64px]\" : \"pl-[40px]\" : \"pl-[40px]\"}  ${fullScreen ? \"flex-[2]\" : \"flex-[3]\"}`,\n          style: {\n            // maxWidth: isComputer && dynamicSize(420, width)\n            // width: isComputer && dynamicSize(420, width),\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: `text-[#00B9F2] text-4xl font-bold leading-[50px] mb-6 ${checkDifference(content === null || content === void 0 ? void 0 : (_content$22 = content['4']) === null || _content$22 === void 0 ? void 0 : (_content$22$content = _content$22.content) === null || _content$22$content === void 0 ? void 0 : (_content$22$content$t = _content$22$content.title) === null || _content$22$content$t === void 0 ? void 0 : _content$22$content$t[language]), liveContent === null || liveContent === void 0 ? void 0 : (_liveContent$9 = liveContent['4']) === null || _liveContent$9 === void 0 ? void 0 : (_liveContent$9$conten = _liveContent$9.content) === null || _liveContent$9$conten === void 0 ? void 0 : (_liveContent$9$conten2 = _liveContent$9$conten.title) === null || _liveContent$9$conten2 === void 0 ? void 0 : _liveContent$9$conten2[language]}`,\n            style: {\n              fontSize: isComputer && dynamicSize(60, width),\n              lineHeight: isComputer && dynamicSize(70, width)\n            },\n            children: content === null || content === void 0 ? void 0 : (_content$23 = content['4']) === null || _content$23 === void 0 ? void 0 : (_content$23$content = _content$23.content) === null || _content$23$content === void 0 ? void 0 : (_content$23$content$t = _content$23$content.title) === null || _content$23$content$t === void 0 ? void 0 : _content$23$content$t[language]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 281,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: `text-[#292E3D] text-sm ${fontLight} leading-4 mb-8 ${content !== null && content !== void 0 && (_content$24 = content['4']) !== null && _content$24 !== void 0 && (_content$24$content = _content$24.content) !== null && _content$24$content !== void 0 && (_content$24$content$d = _content$24$content.description) !== null && _content$24$content$d !== void 0 && _content$24$content$d[language], liveContent === null || liveContent === void 0 ? void 0 : (_liveContent$10 = liveContent['4']) === null || _liveContent$10 === void 0 ? void 0 : (_liveContent$10$conte = _liveContent$10.content) === null || _liveContent$10$conte === void 0 ? void 0 : (_liveContent$10$conte2 = _liveContent$10$conte.description) === null || _liveContent$10$conte2 === void 0 ? void 0 : _liveContent$10$conte2[language]}`,\n            style: {\n              fontWeight: \"100\",\n              fontSize: isComputer && dynamicSize(16, width)\n            },\n            children: content === null || content === void 0 ? void 0 : (_content$25 = content['4']) === null || _content$25 === void 0 ? void 0 : (_content$25$content = _content$25.content) === null || _content$25$content === void 0 ? void 0 : (_content$25$content$d = _content$25$content.description) === null || _content$25$content$d === void 0 ? void 0 : _content$25$content$d[language]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `text-white bg-[#00B9F2] px-[12px] py-1 text-sm text-lg rounded-md ${!isLeftAlign ? '!px-4' : ''}`,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `${checkDifference(content === null || content === void 0 ? void 0 : (_content$26 = content['4']) === null || _content$26 === void 0 ? void 0 : (_content$26$content = _content$26.content) === null || _content$26$content === void 0 ? void 0 : (_content$26$content$b = _content$26$content.button) === null || _content$26$content$b === void 0 ? void 0 : (_content$26$content$b2 = _content$26$content$b[0]) === null || _content$26$content$b2 === void 0 ? void 0 : (_content$26$content$b3 = _content$26$content$b2.text) === null || _content$26$content$b3 === void 0 ? void 0 : _content$26$content$b3[language], liveContent === null || liveContent === void 0 ? void 0 : (_liveContent$11 = liveContent['4']) === null || _liveContent$11 === void 0 ? void 0 : (_liveContent$11$conte = _liveContent$11.content) === null || _liveContent$11$conte === void 0 ? void 0 : (_liveContent$11$conte2 = _liveContent$11$conte.button) === null || _liveContent$11$conte2 === void 0 ? void 0 : (_liveContent$11$conte3 = _liveContent$11$conte2[0]) === null || _liveContent$11$conte3 === void 0 ? void 0 : (_liveContent$11$conte4 = _liveContent$11$conte3.text) === null || _liveContent$11$conte4 === void 0 ? void 0 : _liveContent$11$conte4[language])}`,\n              children: content === null || content === void 0 ? void 0 : (_content$27 = content['4']) === null || _content$27 === void 0 ? void 0 : (_content$27$content = _content$27.content) === null || _content$27$content === void 0 ? void 0 : (_content$27$content$b = _content$27$content.button) === null || _content$27$content$b === void 0 ? void 0 : (_content$27$content$b2 = _content$27$content$b[0]) === null || _content$27$content$b2 === void 0 ? void 0 : (_content$27$content$b3 = _content$27$content$b2.text) === null || _content$27$content$b3 === void 0 ? void 0 : _content$27$content$b3[language]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 29\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 226,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: `py-[58px] ${isPhone ? \"px-2\" : \"px-8\"}  relative`,\n      dir: isLeftAlign ? 'ltr' : 'rtl',\n      style: {\n        padding: isComputer && `50px ${dynamicSize(150, width)}`\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `container mx-auto flex relative  ${!isLeftAlign && 'flex-row-reverse'} ${!isLeftAlign && isTablet && \"pl-[200px]\"}`,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex justify-end absolute top-[-30px] ${isLeftAlign ? \"right-1\" : \"left-1\"}`,\n          children: activeRecentProjectSection === 2 ? \"\" : /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: `relative bg-transparent border-none text-[#667085] text-right text-[16px] leading-[24px] cursor-pointer flex gap-2 items-center `,\n            style: {\n              fontSize: isComputer && dynamicSize(16, width)\n            },\n            onClick: () => {},\n            children: [currentContent === null || currentContent === void 0 ? void 0 : (_currentContent$ = currentContent[\"5\"]) === null || _currentContent$ === void 0 ? void 0 : (_currentContent$$butt = _currentContent$.button) === null || _currentContent$$butt === void 0 ? void 0 : (_currentContent$$butt2 = _currentContent$$butt[0]) === null || _currentContent$$butt2 === void 0 ? void 0 : (_currentContent$$butt3 = _currentContent$$butt2.text) === null || _currentContent$$butt3 === void 0 ? void 0 : _currentContent$$butt3[language], /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://frequencyimage.s3.ap-south-1.amazonaws.com/5d82e78b-cb95-4768-abfe-247369079ce6-bi_arrow-up.svg\",\n              width: \"18\",\n              height: \"17\",\n              alt: \"\",\n              className: `w-[18px] h-[17px] ${isLeftAlign ? 'transform scale-x-[-1]' : ''}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 29\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `flex ${isTablet ? isPhone ? \"gap-[20px]\" : \"gap-[30px]\" : \"gap-[30px]\"} ${isLeftAlign && !isComputer && \"pr-20\"}`,\n          style: {\n            gap: isComputer && dynamicSize(70, width),\n            width: isComputer || fullScreen ? dynamicSize(1230, width) : \"100%\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `leftDetails min-w-[150px] ${isTablet ? isPhone ? \"w-[150px]\" : \"w-[240px]\" : \"\"}`,\n            style: {\n              width: isComputer || fullScreen ? dynamicSize(424, width) : \"\"\n            },\n            children: content === null || content === void 0 ? void 0 : (_content$28 = content[\"5\"]) === null || _content$28 === void 0 ? void 0 : (_content$28$sections = _content$28.sections) === null || _content$28$sections === void 0 ? void 0 : _content$28$sections.map((section, index) => {\n              var _section$content, _section$content$titl, _section$content2, _section$content2$des;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `relative `,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: activeRecentProjectSection === index ? 'font-bold leading-[36px] mb-[16px] cursor-pointer relative' : 'font-bold leading-[36px] mb-[16px] cursor-pointer',\n                  onClick: () => setActiveRecentProjectSection(index),\n                  children: /*#__PURE__*/_jsxDEV(\"h2\", {\n                    className: `${activeRecentProjectSection === index ? 'text-[#292e3d]' : 'text-[#292e3d]'} text-md cursor-pointer`,\n                    onClick: () => setActiveRecentProjectSection(index),\n                    style: {\n                      fontSize: isComputer && dynamicSize(32, width)\n                    },\n                    children: section === null || section === void 0 ? void 0 : (_section$content = section.content) === null || _section$content === void 0 ? void 0 : (_section$content$titl = _section$content.title) === null || _section$content$titl === void 0 ? void 0 : _section$content$titl[language]\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 344,\n                    columnNumber: 41\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 37\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: `${fontLight} ${activeRecentProjectSection === index ? 'text-[#292e3d] text-xs leading-[25px] mb-[24px] opacity-100 transform translate-y-0 transition-opacity duration-300' : 'text-[#292e3d] text-xs leading-[25px] mb-[24px] opacity-0 h-0 transform translate-y-[-20px] transition-opacity duration-300'}`,\n                  style: {\n                    fontSize: isComputer && dynamicSize(16, width)\n                  },\n                  children: section === null || section === void 0 ? void 0 : (_section$content2 = section.content) === null || _section$content2 === void 0 ? void 0 : (_section$content2$des = _section$content2.description) === null || _section$content2$des === void 0 ? void 0 : _section$content2$des[language]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 352,\n                  columnNumber: 37\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 33\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `${isPhone ? \"w-[220px]\" : isTablet ? \"w-[500px]\" : \"\"}`,\n            style: {\n              width: isComputer || fullScreen ? dynamicSize(800, width) : \"\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(Swiper, {\n              modules: [Pagination, Navigation],\n              className: `mySwiper pl-1`,\n              style: {\n                width: '100%'\n              },\n              navigation: {\n                prevEl: prevRef.current,\n                nextEl: nextRef.current\n              },\n              onSwiper: swiper => {\n                setSwiperInstance(swiper);\n                swiper.params.navigation.prevEl = prevRef.current;\n                swiper.params.navigation.nextEl = nextRef.current;\n                swiper.navigation.init();\n                swiper.navigation.update();\n              },\n              children: projectChunks === null || projectChunks === void 0 ? void 0 : projectChunks.map((chunk, slideIndex) => {\n                return /*#__PURE__*/_jsxDEV(SwiperSlide, {\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `${isPhone ? \"flex flex-col\" : `grid grid-cols-2 gap-[12px] auto-rows-auto ${isTablet ? \"w-[350px]\" : \"w-[600px]\"}`}`,\n                    style: {\n                      width: isComputer ? dynamicSize(798, width) : isPhone ? `${600 / 1180 * screen}px` : `${750 / 1180 * screen}px`,\n                      gap: isComputer ? \"\" : `${40 / 1180 * screen}px`,\n                      placeItems: \"\"\n                    },\n                    children: chunk === null || chunk === void 0 ? void 0 : chunk.map((project, cardIndex) => {\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex flex-col rounded-[4px]\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `w-full aspect-[1.4/1] `,\n                          children: /*#__PURE__*/_jsxDEV(\"img\", {\n                            className: `w-full aspect-[1.4/1] object-cover object-center`,\n                            alt: project === null || project === void 0 ? void 0 : project[language],\n                            src: ImagesFromRedux !== null && ImagesFromRedux !== void 0 && ImagesFromRedux[project === null || project === void 0 ? void 0 : project.image] ? ImagesFromRedux === null || ImagesFromRedux === void 0 ? void 0 : ImagesFromRedux[project === null || project === void 0 ? void 0 : project.image] : project !== null && project !== void 0 && project.image ? ProjectSlider === null || ProjectSlider === void 0 ? void 0 : ProjectSlider[project === null || project === void 0 ? void 0 : project.image] : recentProjects.itLab\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 398,\n                            columnNumber: 65\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 397,\n                          columnNumber: 61\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"p-[18px_12px_12px_12px] flex flex-col justify-center items-start gap-[16px] bg-[#00B9F2] flex-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                            title: project === null || project === void 0 ? void 0 : project[titleLan],\n                            className: `text-white text-[20px] font-semibold  h-[40px] ${!isComputer && \"mb-2\"}`,\n                            style: {\n                              fontSize: isComputer && dynamicSize(20, width)\n                            },\n                            children: TruncateText(project === null || project === void 0 ? void 0 : project[titleLan], !isComputer ? 20 : 35)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 408,\n                            columnNumber: 65\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            title: project === null || project === void 0 ? void 0 : project[titleLan],\n                            className: \"text-white text-[16px] font-light leading-[normal]\",\n                            style: {\n                              fontSize: isComputer && dynamicSize(16, width)\n                            },\n                            children: TruncateText(project === null || project === void 0 ? void 0 : project[titleLan], isTablet ? 16 : 25)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 415,\n                            columnNumber: 65\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 406,\n                          columnNumber: 61\n                        }, this)]\n                      }, cardIndex, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 396,\n                        columnNumber: 57\n                      }, this);\n                    })\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 387,\n                    columnNumber: 45\n                  }, this)\n                }, slideIndex, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 386,\n                  columnNumber: 41\n                }, this);\n              })\n            }, language, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `flex items-center justify-between relative mt-8 font-sans`,\n              style: {\n                width: isComputer ? \"\" : isPhone ? \"220px\" : `${400 / 1180 * screen}px`\n              }\n              // ${projectChunks?.length <= 1 ? 'hidden' : ''}\n              ,\n              children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                ref: prevRef,\n                className: `py-[12px] px-[20px] text-[#00B9F2] text-md font-medium border-[1px] border-[#00B9F2] rounded-[6px] flex gap-2 items-center ${isPhone ? \"w-[120px]\" : \"min-w-[246px]\"} justify-center  bg-white transition-all duration-200`,\n                style: {\n                  fontSize: isComputer && dynamicSize(18, width)\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"https://frequencyimage.s3.ap-south-1.amazonaws.com/b2872383-e9d5-4dd7-ae00-8ae00cc4e87e-Vector%20%286%29.svg\",\n                  width: \"18\",\n                  height: \"17\",\n                  alt: \"\",\n                  className: `w-[18px] h-[17px] ${language === \"en\" && 'transform scale-x-[-1]'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 37\n                }, this), !isPhone && (currentContent === null || currentContent === void 0 ? void 0 : (_currentContent$2 = currentContent[\"5\"]) === null || _currentContent$2 === void 0 ? void 0 : (_currentContent$2$but = _currentContent$2.buttons) === null || _currentContent$2$but === void 0 ? void 0 : (_currentContent$2$but2 = _currentContent$2$but[1]) === null || _currentContent$2$but2 === void 0 ? void 0 : (_currentContent$2$but3 = _currentContent$2$but2.text) === null || _currentContent$2$but3 === void 0 ? void 0 : _currentContent$2$but3[language])]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                ref: nextRef,\n                className: `py-[12px] px-[20px] text-[#00B9F2] text-md font-medium border-[1px] border-[#00B9F2] rounded-[6px] flex gap-2 items-center ${isPhone ? \"w-[120px]\" : \"min-w-[246px]\"} justify-center bg-white transition-all duration-200`,\n                style: {\n                  fontSize: isComputer && dynamicSize(18, width)\n                },\n                children: [!isPhone && (currentContent === null || currentContent === void 0 ? void 0 : (_currentContent$3 = currentContent[\"5\"]) === null || _currentContent$3 === void 0 ? void 0 : (_currentContent$3$but = _currentContent$3.buttons) === null || _currentContent$3$but === void 0 ? void 0 : (_currentContent$3$but2 = _currentContent$3$but[2]) === null || _currentContent$3$but2 === void 0 ? void 0 : (_currentContent$3$but3 = _currentContent$3$but2.text) === null || _currentContent$3$but3 === void 0 ? void 0 : _currentContent$3$but3[language]), /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: \"https://frequencyimage.s3.ap-south-1.amazonaws.com/de8581fe-4796-404c-a956-8e951ccb355a-Vector%20%287%29.svg\",\n                  width: \"18\",\n                  height: \"17\",\n                  alt: \"\",\n                  className: `w-[18px] h-[17px] ${isLeftAlign && 'transform scale-x-[-1]'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 37\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 434,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 364,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 308,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: \"bg-[#00B9F2] py-12 relative\",\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"https://frequencyimage.s3.ap-south-1.amazonaws.com/98d10161-fc9a-464f-86cb-7f69a0bebbd5-Group%2061%20%281%29.svg\",\n        width: \"143\",\n        height: \"144\",\n        alt: \"about-us\",\n        className: \"absolute top-0 left-0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"https://frequencyimage.s3.ap-south-1.amazonaws.com/216c2752-9d74-4567-a5fc-b5df034eba6e-Group%2062%20%281%29.svg\",\n        width: \"180\",\n        height: \"181\",\n        alt: \"about-us\",\n        className: \"absolute bottom-0 right-0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 481,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-8 px-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: `text-white text-3xl font-bold mb-4 \n                        ${checkDifference(content === null || content === void 0 ? void 0 : (_content$29 = content[\"6\"]) === null || _content$29 === void 0 ? void 0 : (_content$29$content = _content$29.content) === null || _content$29$content === void 0 ? void 0 : (_content$29$content$t = _content$29$content.title) === null || _content$29$content$t === void 0 ? void 0 : _content$29$content$t[language], liveContent === null || liveContent === void 0 ? void 0 : (_liveContent$12 = liveContent[\"6\"]) === null || _liveContent$12 === void 0 ? void 0 : (_liveContent$12$conte = _liveContent$12.content) === null || _liveContent$12$conte === void 0 ? void 0 : (_liveContent$12$conte2 = _liveContent$12$conte.title) === null || _liveContent$12$conte2 === void 0 ? void 0 : _liveContent$12$conte2[language])}\n                        `,\n            style: {\n              fontSize: isComputer && dynamicSize(36, width)\n            },\n            children: content === null || content === void 0 ? void 0 : (_content$30 = content[\"6\"]) === null || _content$30 === void 0 ? void 0 : (_content$30$content = _content$30.content) === null || _content$30$content === void 0 ? void 0 : (_content$30$content$t = _content$30$content.title) === null || _content$30$content$t === void 0 ? void 0 : _content$30$content$t[language]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: `text-white text-base font-light leading-6\n                        ${checkDifference(content === null || content === void 0 ? void 0 : (_content$31 = content[\"6\"]) === null || _content$31 === void 0 ? void 0 : (_content$31$content = _content$31.content) === null || _content$31$content === void 0 ? void 0 : (_content$31$content$d = _content$31$content.description) === null || _content$31$content$d === void 0 ? void 0 : _content$31$content$d[language], liveContent === null || liveContent === void 0 ? void 0 : (_liveContent$13 = liveContent[\"6\"]) === null || _liveContent$13 === void 0 ? void 0 : (_liveContent$13$conte = _liveContent$13.content) === null || _liveContent$13$conte === void 0 ? void 0 : (_liveContent$13$conte2 = _liveContent$13$conte.description) === null || _liveContent$13$conte2 === void 0 ? void 0 : _liveContent$13$conte2[language])}\n                        `,\n            style: {\n              fontSize: isComputer && dynamicSize(16, width)\n            },\n            children: content === null || content === void 0 ? void 0 : (_content$32 = content[\"6\"]) === null || _content$32 === void 0 ? void 0 : (_content$32$content = _content$32.content) === null || _content$32$content === void 0 ? void 0 : (_content$32$content$d = _content$32$content.description) === null || _content$32$content$d === void 0 ? void 0 : _content$32$content$d[language]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 489,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: scrollRef,\n          className: `w-full overflow-x-auto rm-scroll px-16 pb-4`,\n          style: {\n            padding: isComputer ? `${dynamicSize(40, width)} ${dynamicSize(68, width)}` : \"\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex min-w-100% items-center ${isPhone ? \"flex-col gap-4 justify-center\" : \"w-[fit-content]  justify-between\"}`,\n            style: {\n              gap: !isPhone ? isTablet ? dynamicSize(264, width) : dynamicSize(194, width) : dynamicSize(354, width)\n            },\n            children: content === null || content === void 0 ? void 0 : (_content$33 = content[\"6\"]) === null || _content$33 === void 0 ? void 0 : (_content$33$content = _content$33.content) === null || _content$33$content === void 0 ? void 0 : (_content$33$content$c = _content$33$content.clientsImages) === null || _content$33$content$c === void 0 ? void 0 : _content$33$content$c.map((client, key) => {\n              var _liveContent$14, _liveContent$14$conte, _liveContent$14$conte2, _liveContent$14$conte3;\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-[120px] h-[120px] bg-white rounded-full flex items-center justify-center p-5\n                                        ${checkDifference(client.url, liveContent === null || liveContent === void 0 ? void 0 : (_liveContent$14 = liveContent[\"6\"]) === null || _liveContent$14 === void 0 ? void 0 : (_liveContent$14$conte = _liveContent$14.content) === null || _liveContent$14$conte === void 0 ? void 0 : (_liveContent$14$conte2 = _liveContent$14$conte.clientsImages) === null || _liveContent$14$conte2 === void 0 ? void 0 : (_liveContent$14$conte3 = _liveContent$14$conte2[key]) === null || _liveContent$14$conte3 === void 0 ? void 0 : _liveContent$14$conte3.url)}\n                                        `,\n                style: {\n                  width: isComputer && dynamicSize(200, width),\n                  height: isComputer && dynamicSize(200, width)\n                },\n                children: /*#__PURE__*/_jsxDEV(\"img\", {\n                  src: Img_url + (client === null || client === void 0 ? void 0 : client.url),\n                  width: key === 3 ? 100 : 66,\n                  height: key === 3 ? 30 : 66,\n                  alt: \"about-us\",\n                  className: \"object-contain\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 522,\n                  columnNumber: 37\n                }, this)\n              }, key, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 33\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 508,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 488,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: `py-[40px] pb-[40px] ${!isLeftAlign && 'rtl'} mx-auto relative overflow-hidden`,\n      style: {\n        width: isComputer ? \"800px\" : `${screen - 10}px`\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center mb-16\",\n          children: /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-black text-3xl font-medium\",\n            style: {\n              fontSize: isComputer && dynamicSize(36, width)\n            },\n            children: content === null || content === void 0 ? void 0 : (_content$34 = content[\"7\"]) === null || _content$34 === void 0 ? void 0 : (_content$34$content = _content$34.content) === null || _content$34$content === void 0 ? void 0 : (_content$34$content$t = _content$34$content.title) === null || _content$34$content$t === void 0 ? void 0 : _content$34$content$t[language]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative w-full\",\n          children: [!isPhone && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 left-0 h-full w-[20%] bg-gradient-to-r from-white to-transparent pointer-events-none z-10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 29\n          }, this), !isPhone && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-0 right-0 h-full w-[20%] bg-gradient-to-l from-white to-transparent pointer-events-none z-10\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 29\n          }, this), (content === null || content === void 0 ? void 0 : (_content$35 = content[\"7\"]) === null || _content$35 === void 0 ? void 0 : (_content$35$items = _content$35.items) === null || _content$35$items === void 0 ? void 0 : _content$35$items.length) > 1 && /*#__PURE__*/_jsxDEV(Swiper, {\n            modules: [Navigation, Autoplay, EffectCoverflow],\n            grabCursor: true,\n            centeredSlides: true,\n            slidesPerView: isPhone ? 1 : 2,\n            loop: true,\n            spaceBetween: 10,\n            effect: \"coverflow\",\n            navigation: {\n              prevEl: testimonialPrevRef.current,\n              nextEl: testimonialNextRef.current\n            },\n            onSwiper: swiper => {\n              swiper.params.navigation.prevEl = testimonialPrevRef.current;\n              swiper.params.navigation.nextEl = testimonialNextRef.current;\n              swiper.navigation.init();\n              swiper.navigation.update();\n            },\n            coverflowEffect: {\n              rotate: 0,\n              stretch: 0,\n              depth: 250,\n              modifier: 2,\n              slideShadows: false\n            },\n            autoplay: {\n              delay: 2500\n            },\n            breakpoints: {\n              724: {\n                slidesPerView: isPhone ? 1 : 1.5\n              },\n              500: {\n                slidesPerView: 1\n              }\n            },\n            children: content === null || content === void 0 ? void 0 : (_content$36 = content[\"7\"]) === null || _content$36 === void 0 ? void 0 : (_content$36$items = _content$36.items) === null || _content$36$items === void 0 ? void 0 : _content$36$items.map((testimonial, index) => {\n              var _ref, _testimonial$liveMode, _testimonial$liveMode2, _testimonial$liveMode3, _testimonial$liveMode4, _testimonial$liveMode5, _testimonial$liveMode6, _testimonial$liveMode7, _testimonial$liveMode8, _testimonial$liveMode9, _testimonial$liveMode10, _testimonial$liveMode11, _testimonial$liveMode12, _testimonial$liveMode13, _testimonial$liveMode14, _testimonial$liveMode15, _testimonial$liveMode16;\n              return /*#__PURE__*/_jsxDEV(SwiperSlide, {\n                dir: isLeftAlign ? \"ltr\" : \"rtl\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `border bg-white p-3 rounded-xl flex justify-center  shadow-md`,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex 1\",\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: (_ref = [\"7\"]) === null || _ref === void 0 ? void 0 : _ref[testimonial === null || testimonial === void 0 ? void 0 : (_testimonial$liveMode = testimonial.liveModeVersionData) === null || _testimonial$liveMode === void 0 ? void 0 : _testimonial$liveMode.image],\n                      height: 70,\n                      width: 70,\n                      alt: testimonial === null || testimonial === void 0 ? void 0 : testimonial.name,\n                      className: \"rounded-full h-[70px] w-[75px] object-cover border border-gray-200\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 602,\n                      columnNumber: 53\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 601,\n                    columnNumber: 49\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-5 w-full\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-gray-900 text-md font-bold\",\n                      style: {\n                        fontSize: isComputer && dynamicSize(20, width)\n                      },\n                      children: testimonial === null || testimonial === void 0 ? void 0 : testimonial[titleLan]\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 612,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-500 text-xs font-light mb-4\",\n                      style: {\n                        fontSize: isComputer && dynamicSize(12, width)\n                      },\n                      children: testimonial === null || testimonial === void 0 ? void 0 : (_testimonial$liveMode2 = testimonial.liveModeVersionData) === null || _testimonial$liveMode2 === void 0 ? void 0 : (_testimonial$liveMode3 = _testimonial$liveMode2.sections) === null || _testimonial$liveMode3 === void 0 ? void 0 : (_testimonial$liveMode4 = _testimonial$liveMode3[0]) === null || _testimonial$liveMode4 === void 0 ? void 0 : (_testimonial$liveMode5 = _testimonial$liveMode4.content) === null || _testimonial$liveMode5 === void 0 ? void 0 : (_testimonial$liveMode6 = _testimonial$liveMode5.position) === null || _testimonial$liveMode6 === void 0 ? void 0 : _testimonial$liveMode6[language]\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 617,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-900 text-xs font-light mb-6 leading-5\",\n                      style: {\n                        fontSize: isComputer && dynamicSize(14, width)\n                      },\n                      children: testimonial === null || testimonial === void 0 ? void 0 : (_testimonial$liveMode7 = testimonial.liveModeVersionData) === null || _testimonial$liveMode7 === void 0 ? void 0 : (_testimonial$liveMode8 = _testimonial$liveMode7.sections) === null || _testimonial$liveMode8 === void 0 ? void 0 : (_testimonial$liveMode9 = _testimonial$liveMode8[0]) === null || _testimonial$liveMode9 === void 0 ? void 0 : (_testimonial$liveMode10 = _testimonial$liveMode9.content) === null || _testimonial$liveMode10 === void 0 ? void 0 : (_testimonial$liveMode11 = _testimonial$liveMode10.quote) === null || _testimonial$liveMode11 === void 0 ? void 0 : _testimonial$liveMode11[language]\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 622,\n                      columnNumber: 53\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `flex items-center justify- gap-2`,\n                      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                        src: \"https://frequencyimage.s3.ap-south-1.amazonaws.com/a813959c-7b67-400b-a0b7-f806e63339e5-ph_building%20%281%29.svg\",\n                        height: 18,\n                        width: 18,\n                        alt: testimonial === null || testimonial === void 0 ? void 0 : testimonial.name,\n                        className: \"h-[18px] w-[18px]\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 628,\n                        columnNumber: 57\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: `text-gray-500 text-base font-bold ${isLeftAlign ? \"text-left\" : \"text-right\"}`,\n                        style: {\n                          fontSize: isComputer && dynamicSize(16, width)\n                        },\n                        children: testimonial === null || testimonial === void 0 ? void 0 : (_testimonial$liveMode12 = testimonial.liveModeVersionData) === null || _testimonial$liveMode12 === void 0 ? void 0 : (_testimonial$liveMode13 = _testimonial$liveMode12.sections) === null || _testimonial$liveMode13 === void 0 ? void 0 : (_testimonial$liveMode14 = _testimonial$liveMode13[0]) === null || _testimonial$liveMode14 === void 0 ? void 0 : (_testimonial$liveMode15 = _testimonial$liveMode14.content) === null || _testimonial$liveMode15 === void 0 ? void 0 : (_testimonial$liveMode16 = _testimonial$liveMode15.company) === null || _testimonial$liveMode16 === void 0 ? void 0 : _testimonial$liveMode16[language]\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 635,\n                        columnNumber: 57\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 627,\n                      columnNumber: 53\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 611,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 45\n                }, this)\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 41\n              }, this);\n            })\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 563,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex justify-center items-center gap-7 mt-5 ${!isLeftAlign && \"flex-row-reverse\"}`,\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              ref: testimonialPrevRef,\n              className: \"w-[42px] h-[42px] rounded-full border border-[#00B9F2] flex justify-center items-center cursor-pointer\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://frequencyimage.s3.ap-south-1.amazonaws.com/b2872383-e9d5-4dd7-ae00-8ae00cc4e87e-Vector%20%286%29.svg\",\n                width: \"22\",\n                height: \"17\",\n                alt: \"\",\n                className: `${isLeftAlign && 'scale-x-[-1]'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 656,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 652,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              ref: testimonialNextRef,\n              className: \"w-[42px] h-[42px] rounded-full border border-[#00B9F2] flex justify-center items-center cursor-pointer\",\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"https://frequencyimage.s3.ap-south-1.amazonaws.com/de8581fe-4796-404c-a956-8e951ccb355a-Vector%20%287%29.svg\",\n                width: \"22\",\n                height: \"17\",\n                alt: \"\",\n                className: `${isLeftAlign && 'scale-x-[-1]'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 668,\n                columnNumber: 33\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 543,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 537,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: `py-16 w-[100%] ${isPhone ? \"px-[0px] text-justify\" : \"px-[80px]\"} bg-transparent`,\n      style: {\n        padding: `64px ${isComputer ? dynamicSize(143, width) : \"35px\"}`\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"container mx-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center bg-transparent\",\n          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-3xl font-medium text-black mb-5\",\n            style: {\n              fontSize: isComputer && dynamicSize(36, width)\n            },\n            children: content === null || content === void 0 ? void 0 : (_content$37 = content['8']) === null || _content$37 === void 0 ? void 0 : (_content$37$content = _content$37.content) === null || _content$37$content === void 0 ? void 0 : (_content$37$content$t = _content$37$content.title) === null || _content$37$content$t === void 0 ? void 0 : _content$37$content$t[language]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `font-light text-black leading-7 mb-2 relative bg-transparent`,\n              style: {\n                fontSize: isComputer && dynamicSize(16, width)\n              },\n              dangerouslySetInnerHTML: {\n                __html: content === null || content === void 0 ? void 0 : (_content$38 = content['8']) === null || _content$38 === void 0 ? void 0 : (_content$38$content = _content$38.content) === null || _content$38$content === void 0 ? void 0 : (_content$38$content$d = _content$38$content.description) === null || _content$38$content$d === void 0 ? void 0 : _content$38$content$d[language]\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"i\", {\n              className: `absolute ${isLeftAlign ? isPhone ? \"right-[130px] top-[55px]\" : \"right-[250px]\" : \"right-[152px]\"} top-0  opacity-70 z-10 \n                                ${language === 'ar' ? 'right-48' : ''}`,\n              style: {\n                backgroundImage: `url(${highlightsvg})`,\n                backgroundRepeat: 'no-repeat',\n                backgroundSize: 'contain',\n                width: '120px',\n                height: '100%',\n                mixBlendMode: 'multiply'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"bg-[#00B9F2] text-xs text-white px-4 py-2 text-lg mt-11 mx-auto block rounded\",\n            style: {\n              fontSize: isComputer && dynamicSize(18, width)\n            },\n            children: content === null || content === void 0 ? void 0 : (_content$39 = content['8']) === null || _content$39 === void 0 ? void 0 : (_content$39$content = _content$39.content) === null || _content$39$content === void 0 ? void 0 : (_content$39$content$b = _content$39$content.button) === null || _content$39$content$b === void 0 ? void 0 : (_content$39$content$b2 = _content$39$content$b[0]) === null || _content$39$content$b2 === void 0 ? void 0 : (_content$39$content$b3 = _content$39$content$b2.text) === null || _content$39$content$b3 === void 0 ? void 0 : _content$39$content$b3[language]\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 710,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 686,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 685,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 682,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 9\n  }, this);\n};\n_s(HomePage, \"vNgeAdK1WbC4Qr8J8+yJbhxxqWg=\", false, function () {\n  return [useSelector, useSelector];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "useDispatch", "useSelector", "Arrow", "highlightsvg", "recentProjects", "markets", "safety", "testimonials", "Swiper", "SwiperSlide", "Pagination", "Navigation", "Autoplay", "EffectCoverflow", "blankImage", "TruncateText", "dynamicSize", "generatefontSize", "differentText", "Img_url", "jsxDEV", "_jsxDEV", "defineDevice", "screen", "HomePage", "language", "fullScreen", "highlight", "content", "currentC<PERSON>nt", "liveContent", "width", "_s", "_differentText$checkD", "_content$", "_content$$sections", "_content$$sections$ac", "_content$2", "_content$2$content", "_content$2$content$im", "_content$2$content$im2", "_content$3", "_content$3$content", "_content$3$content$im", "_content$3$content$im2", "_content$4", "_content$4$content", "_liveContent$", "_liveContent$$content", "_content$5", "_content$5$content", "_content$6", "_content$6$content", "_liveContent$2", "_liveContent$2$conten", "_content$7", "_content$7$content", "_content$7$content$de", "_content$8", "_content$8$content", "_content$8$content$bu", "_content$8$content$bu2", "_content$8$content$bu3", "_liveContent$3", "_liveContent$3$conten", "_liveContent$3$conten2", "_liveContent$3$conten3", "_liveContent$3$conten4", "_content$9", "_content$9$content", "_content$9$content$bu", "_content$9$content$bu2", "_content$9$content$bu3", "_content$10", "_content$10$content", "_content$10$content$i", "_content$10$content$i2", "_content$11", "_content$11$content", "_content$11$content$t", "_liveContent$4", "_liveContent$4$conten", "_liveContent$4$conten2", "_content$12", "_content$12$content", "_content$12$content$t", "_content$13", "_content$13$content", "_content$13$content$d", "_liveContent$5", "_liveContent$5$conten", "_liveContent$5$conten2", "_content$14", "_content$14$content", "_content$14$content$d", "_content$15", "_content$15$content", "_content$15$content$d", "_liveContent$6", "_liveContent$6$conten", "_liveContent$6$conten2", "_content$16", "_content$16$content", "_content$16$content$b", "_content$16$content$b2", "_content$16$content$b3", "_content$17", "_content$17$content", "_liveContent$7", "_liveContent$7$conten", "_liveContent$7$conten2", "_content$18", "_content$18$content", "_content$18$content$t", "_content$19", "_liveContent$8", "_content$20", "_content$20$items", "_content$21", "_content$21$content", "_content$21$content$c", "_content$22", "_content$22$content", "_content$22$content$t", "_liveContent$9", "_liveContent$9$conten", "_liveContent$9$conten2", "_content$23", "_content$23$content", "_content$23$content$t", "_content$24", "_content$24$content", "_content$24$content$d", "_liveContent$10", "_liveContent$10$conte", "_liveContent$10$conte2", "_content$25", "_content$25$content", "_content$25$content$d", "_content$26", "_content$26$content", "_content$26$content$b", "_content$26$content$b2", "_content$26$content$b3", "_liveContent$11", "_liveContent$11$conte", "_liveContent$11$conte2", "_liveContent$11$conte3", "_liveContent$11$conte4", "_content$27", "_content$27$content", "_content$27$content$b", "_content$27$content$b2", "_content$27$content$b3", "_currentContent$", "_currentContent$$butt", "_currentContent$$butt2", "_currentContent$$butt3", "_content$28", "_content$28$sections", "_currentContent$2", "_currentContent$2$but", "_currentContent$2$but2", "_currentContent$2$but3", "_currentContent$3", "_currentContent$3$but", "_currentContent$3$but2", "_currentContent$3$but3", "_content$29", "_content$29$content", "_content$29$content$t", "_liveContent$12", "_liveContent$12$conte", "_liveContent$12$conte2", "_content$30", "_content$30$content", "_content$30$content$t", "_content$31", "_content$31$content", "_content$31$content$d", "_liveContent$13", "_liveContent$13$conte", "_liveContent$13$conte2", "_content$32", "_content$32$content", "_content$32$content$d", "_content$33", "_content$33$content", "_content$33$content$c", "_content$34", "_content$34$content", "_content$34$content$t", "_content$35", "_content$35$items", "_content$36", "_content$36$items", "_content$37", "_content$37$content", "_content$37$content$t", "_content$38", "_content$38$content", "_content$38$content$d", "_content$39", "_content$39$content", "_content$39$content$b", "_content$39$content$b2", "_content$39$content$b3", "checkDifference", "bind", "isComputer", "isTablet", "isPhone", "ImagesFromRedux", "state", "_state$homeContent", "_state$homeContent$pr", "homeContent", "present", "images", "fontLight", "fontStyle", "light", "swiperInstance", "setSwiperInstance", "isLeftAlign", "textAlignment", "<PERSON><PERSON><PERSON>", "prevRef", "nextRef", "activeRecentProjectSection", "setActiveRecentProjectSection", "chunkArray", "array", "chunkSize", "chunks", "i", "length", "push", "slice", "projectsPerSlide", "projectChunks", "sections", "items", "ProjectSlider", "fontSize", "scrollRef", "update", "_container$firstChild", "container", "current", "children", "<PERSON><PERSON><PERSON><PERSON>", "index", "interval", "setInterval", "child", "scrollTo", "left", "offsetLeft", "behavior", "clearInterval", "testimonialPrevRef", "testimonialNextRef", "className", "style", "height", "dir", "src", "url", "alt", "objectPosition", "transform", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "paddingLeft", "title", "mainHeading", "lineHeight", "description", "mainPara", "mainButton", "onClick", "button", "text", "gap", "padding", "dangerouslySetInnerHTML", "__html", "wordBreak", "columnGap", "rowGap", "map", "card", "key", "_card$liveModeVersion", "liveModeVersionData", "icon", "cards", "item", "topValue", "Math", "floor", "top", "zIndex", "count", "fontWeight", "type", "section", "_section$content", "_section$content$titl", "_section$content2", "_section$content2$des", "modules", "navigation", "prevEl", "nextEl", "onSwiper", "swiper", "params", "init", "chunk", "slideIndex", "placeItems", "project", "cardIndex", "image", "itLab", "ref", "buttons", "clientsImages", "client", "_liveContent$14", "_liveContent$14$conte", "_liveContent$14$conte2", "_liveContent$14$conte3", "grabCursor", "centeredSlides", "<PERSON><PERSON><PERSON><PERSON>iew", "loop", "spaceBetween", "effect", "coverflowEffect", "rotate", "stretch", "depth", "modifier", "slideShadows", "autoplay", "delay", "breakpoints", "testimonial", "_ref", "_testimonial$liveMode", "_testimonial$liveMode2", "_testimonial$liveMode3", "_testimonial$liveMode4", "_testimonial$liveMode5", "_testimonial$liveMode6", "_testimonial$liveMode7", "_testimonial$liveMode8", "_testimonial$liveMode9", "_testimonial$liveMode10", "_testimonial$liveMode11", "_testimonial$liveMode12", "_testimonial$liveMode13", "_testimonial$liveMode14", "_testimonial$liveMode15", "_testimonial$liveMode16", "name", "position", "quote", "company", "backgroundImage", "backgroundRepeat", "backgroundSize", "mixBlendMode", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/shade_cms/dashboard/src/features/Resources/components/websiteComponent/Home.jsx"], "sourcesContent": ["import { useEffect, useRef, useState } from \"react\";\r\nimport {\r\n    useDispatch,\r\n    useSelector,\r\n} from \"react-redux\";\r\nimport Arrow from \"../../../../assets/icons/right-wrrow.svg\";\r\n// import AboutUs from \"../../../../assets/images/aboutus.png\";\r\n// import background from \"../../../../assets/images/Hero.png\";\r\nimport highlightsvg from \"../../../../assets/highlight.svg\"\r\nimport {\r\n    recentProjects,\r\n    markets,\r\n    safety,\r\n    testimonials,\r\n} from \"../../../../assets/index\";\r\nimport { Swiper, SwiperSlide } from \"swiper/react\";\r\nimport {\r\n    Pagination,\r\n    Navigation,\r\n    Autoplay,\r\n    EffectCoverflow,\r\n} from \"swiper/modules\";\r\nimport \"swiper/css/navigation\";\r\nimport \"swiper/css\";\r\nimport \"swiper/css/pagination\";\r\nimport blankImage from \"../../../../assets/images/blankImage.webp\";\r\nimport { TruncateText } from \"../../../../app/capitalizeword\";\r\nimport dynamicSize, { generatefontSize } from \"../../../../app/fontSizes\";\r\nimport { differentText } from \"../../../../app/fontSizes\";\r\n// import contentJSON from './content.json'\r\nimport { Img_url } from \"../../../../routes/backend\";\r\n\r\nfunction defineDevice(screen) {\r\n    if (screen > 900) {\r\n        return \"computer\"\r\n    } else if (screen < 900 && screen > 550) {\r\n        return 'tablet'\r\n    } else {\r\n        return 'phone'\r\n    }\r\n}\r\n\r\nconst HomePage = ({ language, screen, fullScreen, highlight, content, currentContent, liveContent, width }) => {\r\n    // const dispatch = useDispatch()\r\n    const checkDifference = highlight ? differentText?.checkDifference?.bind(differentText) : () => \"\"\r\n    const isComputer = screen > 900;\r\n    const isTablet = screen < 900 && screen > 730;\r\n    const isPhone = screen < 450;\r\n    const ImagesFromRedux = useSelector((state) => state?.homeContent?.present?.images)\r\n    const fontLight = useSelector(state => state.fontStyle.light)\r\n    // const platform = useSelector(state => state.platform.platform)\r\n    const [swiperInstance, setSwiperInstance] = useState(null);\r\n    let isLeftAlign = language === \"en\";\r\n    let textAlignment = isLeftAlign ? \"text-left\" : \"text-right\"\r\n    const titleLan = isLeftAlign ? \"titleEn\" : \"titleAr\"\r\n    const prevRef = useRef(null);\r\n    const nextRef = useRef(null);\r\n    const [activeRecentProjectSection, setActiveRecentProjectSection] = useState(0);\r\n    let chunkArray = (array, chunkSize) => {\r\n        const chunks = [];\r\n        for (let i = 0; i < array?.length; i += chunkSize) {\r\n            chunks?.push(array?.slice(i, i + chunkSize));\r\n        }\r\n        return chunks;\r\n    };\r\n    const projectsPerSlide = 4;\r\n\r\n    let projectChunks = chunkArray(\r\n        content?.[\"5\"]?.sections?.[activeRecentProjectSection]?.items || [],\r\n        projectsPerSlide\r\n    );\r\n    const ProjectSlider = { ...recentProjects, ...markets, ...safety };\r\n\r\n\r\n    const fontSize = generatefontSize(defineDevice(screen), dynamicSize, width)\r\n\r\n    const scrollRef = useRef(null);\r\n\r\n    useEffect(() => {\r\n        if (swiperInstance) {\r\n            swiperInstance?.update();\r\n        }\r\n    }, [language]);\r\n\r\n    useEffect(() => {\r\n        const container = scrollRef.current;\r\n        if (!container) return;\r\n\r\n        const children = container.firstChild?.children;\r\n        if (!children || children.length === 0) return;\r\n\r\n        let index = 0;\r\n        const interval = setInterval(() => {\r\n            if (index >= children.length) index = 0;\r\n\r\n            const child = children[index];\r\n            if (child) {\r\n                container.scrollTo({\r\n                    left: child.offsetLeft - 64, // Adjust for padding (`px-16`)\r\n                    behavior: \"smooth\"\r\n                });\r\n            }\r\n\r\n            index++;\r\n        }, 3000); // every 3 seconds\r\n\r\n        return () => clearInterval(interval);\r\n    }, []);\r\n\r\n\r\n\r\n    const testimonialPrevRef = useRef(null);\r\n    const testimonialNextRef = useRef(null);\r\n    return (\r\n        <div className={`w-full relative ${textAlignment} bankgothic-medium-dt bg-[white]`} >\r\n            {/* banner 1  */}\r\n            <section className=\"w-full relative\">\r\n                <div\r\n                    className={`w-full overflow-y-hidden min-h-[400px] block ${language === \"en\" ? \"scale-x-100\" : \"scale-x-[-1]\"\r\n                        }`}\r\n                    style={{ height: dynamicSize(715, width) }}\r\n                >\r\n                    <img\r\n                        dir={isLeftAlign ? \"ltr\" : \"rtl\"}\r\n                        src={`${Img_url}${content?.[\"1\"]?.content?.images?.[0]?.url}`}\r\n                        alt={content?.[\"1\"]?.content?.images?.[0]?.url}\r\n                        className=\"w-full object-cover\"\r\n                        style={{ objectPosition: \"center\", transform: \"scaleX(-1)\", height: isTablet ? \"500px\" : isPhone && \"500px\" }} />\r\n                </div>\r\n                <div\r\n                    className={`container mx-auto absolute ${isComputer ? \"top-[20%]\" : \"top-16\"}  left-0 right-0 px-4`}>\r\n                    <div className={`text-left flex flex-col ${language === \"en\" ? \"items-start\" : \"items-end\"} ${textAlignment} ${isPhone ? \"px-[0px] py-10\" : \"px-[80px]\"}`}\r\n                        style={{ paddingLeft: isComputer && dynamicSize(140, width) }}>\r\n                        <h1 className={`${(checkDifference(content?.[\"1\"]?.content?.title[language], liveContent?.[\"1\"]?.content?.title[language]))} text-[#292E3D] text-[45px] tracking-[0px]  leading-[2.5rem] capitalize font-[500] mb-4 ${isPhone ? \"w-full\" : fullScreen ? \"w-3/5\" : \"w-3/5\"}  `}\r\n                            style={{ fontSize: fontSize?.mainHeading, lineHeight: isComputer && `${(width / 1526) * 4.5}rem`, }}\r\n                        >\r\n                            {content?.[\"1\"]?.content?.title[language]}\r\n                        </h1>\r\n                        <p className={`${(checkDifference(content?.[\"1\"]?.content?.description[language], liveContent?.[\"1\"]?.content?.description[language]))} text-[#0e172fb3]  leading-[16px] mb-6 ${isPhone ? \"w-full text-[12px]\" : \"w-1/2 text-[10px]\"} tracking-[0px]`}\r\n                            style={{ fontSize: fontSize?.mainPara, lineHeight: isComputer && `${width / 1526 * 24}px` }}\r\n                        >\r\n                            {content?.[\"1\"]?.content?.description?.[language]}\r\n                        </p>\r\n                        <button\r\n                            className={`relative items-center flex ${isLeftAlign ? \"\" : \"flex-row-reverse\"} gap-2 text-[12px] font-medium px-[12px] py-[6px] px-[12px] bg-[#00b9f2] text-white rounded-md`}\r\n                            style={{ fontSize: fontSize?.mainButton }}\r\n                            onClick={() => { }}\r\n                        >\r\n                            <span className={`${(checkDifference(content?.[\"1\"]?.content?.button?.[0]?.text?.[language], liveContent?.[\"1\"]?.content?.button?.[0]?.text?.[language]))}`}>\r\n                                {content?.[\"1\"]?.content?.button?.[0]?.text?.[language]}</span>\r\n                            <img\r\n                                src={Arrow}\r\n                                width=\"10\"\r\n                                height=\"11\"\r\n                                alt=\"\"\r\n                                style={{ transform: isLeftAlign ? \"rotate(180deg)\" : \"\", width: isComputer && dynamicSize(16, width) }}\r\n                            />\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </section>\r\n            {/* about us section 2 */}\r\n            <section className={` ${isPhone ? \"px-2 py-[60px]\" : isTablet ? \"px-[80px] py-[120px]\" : \"px-[150px] py-[120px]\"} ${language === \"en\" ? \"\" : \" direction-rtl\"} items-start`}>\r\n                <div className={`relative container mx-auto flex ${isPhone ? \"flex-col\" : \"\"} ${isLeftAlign ? \"\" : \"flex-row-reverse\"} items-center`}>\r\n                    {/* Image section */}\r\n                    <div className={`${isPhone ? \"w-[90%]\" : \"w-[70%]\"} h-[500px] overflow-hidden rounded-sm shadow-lg `}\r\n                        style={{ height: isComputer && dynamicSize(629, width), width: isComputer && dynamicSize(877, width) }}>\r\n                        <img src={`${Img_url}${content?.[\"2\"]?.content?.images?.[0]?.url}`} alt=\"about-us\" className=\"w-full h-[500px] object-cover\"\r\n                            style={{ width: isComputer && dynamicSize(877, width), height: isComputer && '100%' }}\r\n                        />\r\n                    </div>\r\n                    {/* About content */}\r\n                    <div className={`flex flex-col items-start ${isPhone ? \" \" : \"absolute \"} ${isLeftAlign ? \"right-0 text-left\" : \"left-0 text-right\"} bg-[#145098] ${isTablet ? \"p-10 py-14\" : \"p-14 py-20\"} rounded-sm w-[23rem]`}\r\n                        style={{ gap: isComputer ? dynamicSize(26, width) : \"16px\", width: isComputer && dynamicSize(488, width), padding: isComputer && `${dynamicSize(98, width)} ${dynamicSize(65, width)}` }}\r\n                    >\r\n                        <h2 className={`text-white text-[28px] leading-[1.8rem]  font-normal ${checkDifference(content?.[\"2\"]?.content?.title?.[language], liveContent?.[\"2\"]?.content?.title?.[language])}`}\r\n                            style={{ fontSize: isComputer && dynamicSize(36, width), lineHeight: isComputer && dynamicSize(32, width) }}>\r\n                            {content?.[\"2\"]?.content?.title?.[language]}\r\n                        </h2>\r\n                        <div className={`text-white font-[100] text-[12px] leading-[16px] ${checkDifference(content?.[\"2\"]?.content?.description?.[language], liveContent?.[\"2\"]?.content?.description?.[language])}`}\r\n                            style={{ fontSize: isComputer && dynamicSize(15, width), lineHeight: isComputer && dynamicSize(26, width) }}\r\n                            dangerouslySetInnerHTML={{ __html: content?.[\"2\"]?.content?.description?.[language] }}\r\n                        />\r\n                        <button className={`px-[6px] py-[2px] bg-[#00B9F2] text-white text-[12px] ${checkDifference(content?.[\"2\"]?.content?.description?.[language], liveContent?.[\"2\"]?.content?.description?.[language])} rounded-md hover:bg-opacity-90 text-right`}\r\n                            style={{ fontSize: isComputer && dynamicSize(18, width) }}\r\n                        >\r\n                            {content?.[\"2\"]?.content?.button?.[0]?.text?.[language]}\r\n                        </button>\r\n                    </div>\r\n\r\n                </div>\r\n            </section >\r\n            {/* service section 3 */}\r\n            < section className=\"py-10 bg-gray-100\" style={{ wordBreak: \"normal\" }}>\r\n                <div className=\"container mx-auto px-6\"\r\n                    style={{ padding: isComputer && `${dynamicSize(44, width)} ${dynamicSize(220, width)}` }}>\r\n                    <h2 className={`text-center text-3xl font-light text-[#292E3D] mb-9 ${isPhone ? \"text-[30px]\" : \"text-[40px]\"}\r\n                    ${checkDifference(content?.[\"3\"]?.content?.title[language], liveContent?.[\"3\"]?.content?.title?.[language])}\r\n                    `}\r\n                        style={{ fontSize: isComputer && dynamicSize(36, width) }}>\r\n                        {content?.[\"3\"]?.content?.title?.[language]}\r\n                    </h2>\r\n\r\n                    <div className={`${isPhone ? \"flex gap-4 flex-col\" : \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:gap-12 sm:gap-6\"}\r\n                    ${checkDifference(content?.[\"3\"]?.items, liveContent?.[\"3\"]?.items)}\r\n                    `}\r\n                        style={{ columnGap: isComputer && dynamicSize(96, width), rowGap: isComputer && dynamicSize(48, width) }}>\r\n                        {content?.['3']?.items?.map((card, key) => {\r\n                            return (\r\n                                <div key={key} className={`w-full h-44 flex items-center justify-center p-6 rounded-md transition-transform duration-300 hover:scale-105 cursor-pointer ${key % 2 !== 0 ? \"bg-blue-900 text-[white]\" : \" bg-stone-200\"} `}>\r\n                                    <div className=\"flex flex-col items-center gap-4\">\r\n                                        <img src={Img_url + card?.liveModeVersionData?.icon} width={40} height={40} alt=\"Icon\" className=\"h-10 w-10\" />\r\n                                        <h5 className={`relative text-lg font-light text-center `}\r\n                                            style={{ fontSize: isComputer && dynamicSize(20, width) }}>\r\n                                            {card?.[titleLan]}\r\n                                            <span className=\"block h-[2px] w-16 bg-gray-300 mt-2 mx-auto\"></span>\r\n                                        </h5>\r\n                                    </div>\r\n                                </div>)\r\n                        })}\r\n                    </div>\r\n                </div>\r\n            </section >\r\n            {/* experience section 4 */}\r\n            < section className={`py-[115px] overflow-hidden ${isComputer ? fullScreen ? \"px-20 pt-40 pb-60\" : \"px-20 pb-60\" : !isLeftAlign ? \"px-8\" : \"px-10\"}`} dir={isLeftAlign ? 'ltr' : \"rtl\"} >\r\n                <div\r\n                    className={`container mx-auto flex ${isPhone ? \"flex-col gap-[350px]\" : \"gap-10\"} `}>\r\n                    <div className={`w-[100%]  flex-[4]`}\r\n                    >\r\n                        <div className={`relative \r\n                        ${checkDifference()}\r\n                        ${isTablet ?\r\n                                (!isLeftAlign ? \"left-[-70px]\" : \"left-[15px]\") :\r\n                                isComputer && fullScreen ? \"left-[450px] scale-[1.7]\" :\r\n                                    isPhone ? screen < 370 ? \"left-[-10px] scale-[.6]\" :\r\n                                        \"left-[0px] scale-[1]\" : \"left-[50px] scale-[1.2]\"} \r\n                            ${!isLeftAlign && isPhone && \"left-[-310px]\"}`}\r\n                        // style={{ width: isComputer && dynamicSize(200, width) }}\r\n                        >\r\n                            {content?.[\"4\"]?.content?.cards?.map((item, key) => {\r\n                                // Set top position based on whether key is odd or even\r\n                                const topValue = Math.floor(key / 2) * 140 + (key % 2 !== 0 ? -35 : 25); // Odd = move up, Even = move down\r\n                                return (\r\n                                    <div\r\n                                        key={key}\r\n                                        style={{ top: `${topValue}px`, zIndex: key + 1 }}\r\n                                        className={`w-[180px] absolute rounded-md bg-white shadow-lg p-4 ${key % 2 !== 0 ? !isLeftAlign ? \"left-[170px]\" : \"xl:left-[150px]\" : \"left-0\"}`}\r\n                                    >\r\n                                        <div className=\"relative\">\r\n                                            <img\r\n                                                className={`absolute ${key % 2 === 1 ? \"top-[-22px] right-[-32px]\" : \"left-[-36px] top-[-27px]\"}`}\r\n                                                src={Img_url + item?.icon}\r\n                                                width={40}\r\n                                                height={key === 1 ? 47 : 60}\r\n                                                alt=\"\"\r\n                                            />\r\n                                        </div>\r\n                                        <h3 className=\"text-[#292E3D] text-2xl font-semibold pl-2 font-sans\"\r\n                                            style={{\r\n                                                fontSize: isComputer && dynamicSize(40, width)\r\n                                            }}\r\n                                        >{item?.count}</h3>\r\n                                        <h5 className={`text-[#292E3D] text-xs font-light relative before:absolute ${isLeftAlign ? \"before:left-[-10px]\" : \"before:right-[-10px]\"} before:top-0 before:w-[3px] before:h-[18px] before:bg-[#F9995D]`}\r\n                                            style={{\r\n                                                fontSize: isComputer && dynamicSize(12, width)\r\n                                            }}\r\n                                        >\r\n                                            {item?.title[language]}\r\n                                        </h5>\r\n                                    </div>\r\n                                );\r\n                            })}\r\n                        </div>\r\n                    </div>\r\n                    <div className={`max-w-[450xp] ${isTablet ? !isLeftAlign ? \"pr-[64px]\" : \"pl-[40px]\" : \"pl-[40px]\"}  ${fullScreen ? \"flex-[2]\" : \"flex-[3]\"}`}\r\n                        style={{\r\n                            // maxWidth: isComputer && dynamicSize(420, width)\r\n                            // width: isComputer && dynamicSize(420, width),\r\n                        }}\r\n                    >\r\n                        <h2 className={`text-[#00B9F2] text-4xl font-bold leading-[50px] mb-6 ${checkDifference(content?.['4']?.content?.title?.[language]), liveContent?.['4']?.content?.title?.[language]}`}\r\n                            style={{\r\n                                fontSize: isComputer && dynamicSize(60, width),\r\n                                lineHeight: isComputer && dynamicSize(70, width)\r\n                            }}>\r\n                            {content?.['4']?.content?.title?.[language]}\r\n                        </h2>\r\n                        <p className={`text-[#292E3D] text-sm ${fontLight} leading-4 mb-8 ${content?.['4']?.content?.description?.[language], liveContent?.['4']?.content?.description?.[language]}`}\r\n                            style={{\r\n                                fontWeight: \"100\",\r\n                                fontSize: isComputer && dynamicSize(16, width)\r\n                            }}\r\n                        >\r\n                            {content?.['4']?.content?.description?.[language]}\r\n                        </p>\r\n                        <button\r\n                            className={`text-white bg-[#00B9F2] px-[12px] py-1 text-sm text-lg rounded-md ${!isLeftAlign ? '!px-4' : ''}`}\r\n                        >\r\n                            <span className={`${checkDifference(content?.['4']?.content?.button?.[0]?.text?.[language], liveContent?.['4']?.content?.button?.[0]?.text?.[language])}`}>\r\n                                {content?.['4']?.content?.button?.[0]?.text?.[language]}\r\n                            </span>\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </section >\r\n\r\n            {/* subProjects 5 */}\r\n            < section className={`py-[58px] ${isPhone ? \"px-2\" : \"px-8\"}  relative`} dir={isLeftAlign ? 'ltr' : 'rtl'}\r\n                style={{ padding: isComputer && `50px ${dynamicSize(150, width)}`, }}>\r\n                <div className={`container mx-auto flex relative  ${!isLeftAlign && 'flex-row-reverse'} ${!isLeftAlign && isTablet && \"pl-[200px]\"}`}>\r\n                    <div className={`flex justify-end absolute top-[-30px] ${isLeftAlign ? \"right-1\" : \"left-1\"}`}>\r\n                        {activeRecentProjectSection === 2 ? (\"\") : (\r\n                            <button\r\n                                type=\"button\"\r\n                                className={`relative bg-transparent border-none text-[#667085] text-right text-[16px] leading-[24px] cursor-pointer flex gap-2 items-center `}\r\n                                style={{ fontSize: isComputer && dynamicSize(16, width) }}\r\n                                onClick={() => { }}>\r\n                                {currentContent?.[\"5\"]?.button?.[0]?.text?.[language]}\r\n                                <img\r\n                                    src=\"https://frequencyimage.s3.ap-south-1.amazonaws.com/5d82e78b-cb95-4768-abfe-247369079ce6-bi_arrow-up.svg\"\r\n                                    width=\"18\"\r\n                                    height=\"17\"\r\n                                    alt=\"\"\r\n                                    className={`w-[18px] h-[17px] ${isLeftAlign ? 'transform scale-x-[-1]' : ''}`}\r\n                                />\r\n                            </button>\r\n                        )}\r\n                    </div>\r\n\r\n                    <div className={`flex ${isTablet ? isPhone ? \"gap-[20px]\" : \"gap-[30px]\" : \"gap-[30px]\"} ${isLeftAlign && !isComputer && \"pr-20\"}`}\r\n                        style={{ gap: isComputer && dynamicSize(70, width), width: isComputer || fullScreen ? dynamicSize(1230, width) : \"100%\" }}>\r\n                        <div className={`leftDetails min-w-[150px] ${isTablet ? isPhone ? \"w-[150px]\" : \"w-[240px]\" : \"\"}`}\r\n                            style={{ width: isComputer || fullScreen ? dynamicSize(424, width) : \"\" }}>\r\n                            {content?.[\"5\"]?.sections?.map((section, index) => (\r\n                                <div\r\n                                    key={index}\r\n                                    className={`relative `}\r\n                                >\r\n                                    <span className={activeRecentProjectSection === index\r\n                                        ? 'font-bold leading-[36px] mb-[16px] cursor-pointer relative'\r\n                                        : 'font-bold leading-[36px] mb-[16px] cursor-pointer'}\r\n                                        onClick={() => setActiveRecentProjectSection(index)}\r\n                                    >\r\n                                        <h2 className={`${activeRecentProjectSection === index ? 'text-[#292e3d]' : 'text-[#292e3d]'} text-md cursor-pointer`}\r\n                                            onClick={() => setActiveRecentProjectSection(index)}\r\n                                            style={{ fontSize: isComputer && dynamicSize(32, width) }}\r\n                                        >\r\n                                            {section?.content?.title?.[language]}\r\n                                        </h2>\r\n                                    </span>\r\n\r\n                                    <p className={`${fontLight} ${activeRecentProjectSection === index\r\n                                        ? 'text-[#292e3d] text-xs leading-[25px] mb-[24px] opacity-100 transform translate-y-0 transition-opacity duration-300'\r\n                                        : 'text-[#292e3d] text-xs leading-[25px] mb-[24px] opacity-0 h-0 transform translate-y-[-20px] transition-opacity duration-300'\r\n                                        }`}\r\n                                        style={{ fontSize: isComputer && dynamicSize(16, width) }}\r\n                                    >\r\n                                        {section?.content?.description?.[language]}\r\n                                    </p>\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n\r\n                        <div className={`${isPhone ? \"w-[220px]\" : isTablet ? \"w-[500px]\" : \"\"}`}\r\n                            style={{ width: isComputer || fullScreen ? dynamicSize(800, width) : \"\" }}\r\n                        >\r\n                            <Swiper\r\n                                key={language}\r\n                                modules={[Pagination, Navigation]}\r\n                                className={`mySwiper pl-1`}\r\n                                style={{ width: '100%' }}\r\n                                navigation={{\r\n                                    prevEl: prevRef.current,\r\n                                    nextEl: nextRef.current,\r\n                                }}\r\n                                onSwiper={(swiper) => {\r\n                                    setSwiperInstance(swiper);\r\n                                    swiper.params.navigation.prevEl = prevRef.current;\r\n                                    swiper.params.navigation.nextEl = nextRef.current;\r\n                                    swiper.navigation.init();\r\n                                    swiper.navigation.update();\r\n                                }}\r\n                            >\r\n                                {projectChunks?.map((chunk, slideIndex) => {\r\n                                    return (\r\n                                        <SwiperSlide key={slideIndex}>\r\n                                            <div className={`${isPhone ? \"flex flex-col\" : `grid grid-cols-2 gap-[12px] auto-rows-auto ${isTablet ? \"w-[350px]\" : \"w-[600px]\"}`}`}\r\n                                                style={{\r\n                                                    width: isComputer ? dynamicSize(798, width) : isPhone ? `${(600 / 1180) * screen}px` : `${(750 / 1180) * screen}px`,\r\n                                                    gap: isComputer ? \"\" : `${(40 / 1180) * screen}px`,\r\n                                                    placeItems: \"\"\r\n                                                }}\r\n                                            >\r\n                                                {chunk?.map((project, cardIndex) => {\r\n                                                    return (\r\n                                                        <div className=\"flex flex-col rounded-[4px]\" key={cardIndex}>\r\n                                                            <div className={`w-full aspect-[1.4/1] `} >\r\n                                                                <img\r\n                                                                    className={`w-full aspect-[1.4/1] object-cover object-center`}\r\n                                                                    alt={project?.[language]}\r\n                                                                    src={ImagesFromRedux?.[project?.image] ? ImagesFromRedux?.[project?.image] : project?.image\r\n                                                                        ? ProjectSlider?.[project?.image]\r\n                                                                        : recentProjects.itLab}\r\n                                                                />\r\n                                                            </div>\r\n                                                            <div className=\"p-[18px_12px_12px_12px] flex flex-col justify-center items-start gap-[16px] bg-[#00B9F2] flex-1\">\r\n\r\n                                                                <h5\r\n                                                                    title={project?.[titleLan]}\r\n                                                                    className={`text-white text-[20px] font-semibold  h-[40px] ${!isComputer && \"mb-2\"}`}\r\n                                                                    style={{ fontSize: isComputer && dynamicSize(20, width) }}\r\n                                                                >\r\n                                                                    {TruncateText(project?.[titleLan], !isComputer ? 20 : 35)}\r\n                                                                </h5>\r\n                                                                <p\r\n                                                                    title={project?.[titleLan]}\r\n                                                                    className=\"text-white text-[16px] font-light leading-[normal]\"\r\n                                                                    style={{ fontSize: isComputer && dynamicSize(16, width) }}\r\n                                                                >\r\n                                                                    {TruncateText(project?.[titleLan], (isTablet ? 16 : 25))}\r\n                                                                </p>\r\n                                                            </div>\r\n                                                        </div>\r\n                                                    )\r\n                                                }\r\n                                                )}\r\n                                            </div>\r\n                                        </SwiperSlide>\r\n                                    )\r\n                                })}\r\n                            </Swiper>\r\n\r\n                            {/* Custom buttons */}\r\n                            <div\r\n                                className={`flex items-center justify-between relative mt-8 font-sans`}\r\n                                style={{ width: isComputer ? \"\" : isPhone ? \"220px\" : `${(400 / 1180) * screen}px` }}\r\n                            // ${projectChunks?.length <= 1 ? 'hidden' : ''}\r\n                            >\r\n                                <button ref={prevRef} className={`py-[12px] px-[20px] text-[#00B9F2] text-md font-medium border-[1px] border-[#00B9F2] rounded-[6px] flex gap-2 items-center ${isPhone ? \"w-[120px]\" : \"min-w-[246px]\"} justify-center  bg-white transition-all duration-200`}\r\n                                    style={{ fontSize: isComputer && dynamicSize(18, width) }}>\r\n                                    <img\r\n                                        src=\"https://frequencyimage.s3.ap-south-1.amazonaws.com/b2872383-e9d5-4dd7-ae00-8ae00cc4e87e-Vector%20%286%29.svg\"\r\n                                        width=\"18\"\r\n                                        height=\"17\"\r\n                                        alt=\"\"\r\n                                        className={`w-[18px] h-[17px] ${language === \"en\" && 'transform scale-x-[-1]'}`}\r\n                                    />\r\n\r\n                                    {!isPhone &&\r\n                                        currentContent?.[\"5\"]?.buttons?.[1]?.text?.[language]\r\n                                    }\r\n                                </button>\r\n                                <button ref={nextRef} className={`py-[12px] px-[20px] text-[#00B9F2] text-md font-medium border-[1px] border-[#00B9F2] rounded-[6px] flex gap-2 items-center ${isPhone ? \"w-[120px]\" : \"min-w-[246px]\"} justify-center bg-white transition-all duration-200`}\r\n                                    style={{ fontSize: isComputer && dynamicSize(18, width) }}>\r\n                                    {!isPhone &&\r\n                                        currentContent?.[\"5\"]?.buttons?.[2]?.text?.[language]\r\n                                    }\r\n                                    <img\r\n                                        src=\"https://frequencyimage.s3.ap-south-1.amazonaws.com/de8581fe-4796-404c-a956-8e951ccb355a-Vector%20%287%29.svg\"\r\n                                        width=\"18\"\r\n                                        height=\"17\"\r\n                                        alt=\"\"\r\n                                        className={`w-[18px] h-[17px] ${isLeftAlign && 'transform scale-x-[-1]'}`}\r\n                                    />\r\n                                </button>\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </section >\r\n\r\n            {/* client section 6 */}\r\n            <section className=\"bg-[#00B9F2] py-12 relative\" >\r\n                <img\r\n                    src=\"https://frequencyimage.s3.ap-south-1.amazonaws.com/98d10161-fc9a-464f-86cb-7f69a0bebbd5-Group%2061%20%281%29.svg\"\r\n                    width=\"143\"\r\n                    height=\"144\"\r\n                    alt=\"about-us\"\r\n                    className=\"absolute top-0 left-0\"\r\n                />\r\n                <img\r\n                    src=\"https://frequencyimage.s3.ap-south-1.amazonaws.com/216c2752-9d74-4567-a5fc-b5df034eba6e-Group%2062%20%281%29.svg\"\r\n                    width=\"180\"\r\n                    height=\"181\"\r\n                    alt=\"about-us\"\r\n                    className=\"absolute bottom-0 right-0\"\r\n                />\r\n                <div className=\"container mx-auto\">\r\n                    <div className=\"text-center mb-8 px-4\">\r\n                        <h2 className={`text-white text-3xl font-bold mb-4 \r\n                        ${checkDifference(content?.[\"6\"]?.content?.title?.[language], liveContent?.[\"6\"]?.content?.title?.[language])}\r\n                        `}\r\n                            style={{ fontSize: isComputer && dynamicSize(36, width) }}\r\n                        >\r\n                            {content?.[\"6\"]?.content?.title?.[language]}\r\n                        </h2>\r\n                        <p className={`text-white text-base font-light leading-6\r\n                        ${checkDifference(content?.[\"6\"]?.content?.description?.[language], liveContent?.[\"6\"]?.content?.description?.[language])}\r\n                        `}\r\n                            style={{ fontSize: isComputer && dynamicSize(16, width) }}\r\n                        >\r\n                            {content?.[\"6\"]?.content?.description?.[language]}\r\n                        </p>\r\n                    </div>\r\n                    <div ref={scrollRef} className={`w-full overflow-x-auto rm-scroll px-16 pb-4`}\r\n                        style={{ padding: isComputer ? `${dynamicSize(40, width)} ${dynamicSize(68, width)}` : \"\" }}\r\n                    >\r\n                        <div className={`flex min-w-100% items-center ${isPhone ? \"flex-col gap-4 justify-center\" : \"w-[fit-content]  justify-between\"}`}\r\n                            style={{ gap: !isPhone ? (isTablet ? dynamicSize(264, width) : dynamicSize(194, width)) : dynamicSize(354, width) }}\r\n                        >\r\n                            {content?.[\"6\"]?.content?.clientsImages?.map((client, key) => (\r\n                                <div\r\n                                    key={key}\r\n                                    className={`w-[120px] h-[120px] bg-white rounded-full flex items-center justify-center p-5\r\n                                        ${checkDifference(client.url, liveContent?.[\"6\"]?.content?.clientsImages?.[key]?.url)}\r\n                                        `}\r\n                                    style={{\r\n                                        width: isComputer && dynamicSize(200, width),\r\n                                        height: isComputer && dynamicSize(200, width)\r\n                                    }}\r\n                                >\r\n                                    <img\r\n                                        src={Img_url + client?.url}\r\n                                        width={key === 3 ? 100 : 66}\r\n                                        height={key === 3 ? 30 : 66}\r\n                                        alt=\"about-us\"\r\n                                        className=\"object-contain\"\r\n                                    />\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </section>\r\n\r\n            {/* testomonials section 7 */}\r\n            < section\r\n                className={`py-[40px] pb-[40px] ${!isLeftAlign && 'rtl'} mx-auto relative overflow-hidden`}\r\n                style={{\r\n                    width: isComputer ? \"800px\" : `${screen - 10}px`,\r\n                }}\r\n            >\r\n                <div className=\"container mx-auto\" >\r\n                    <div className=\"text-center mb-16\">\r\n                        <h2 className=\"text-black text-3xl font-medium\"\r\n                            style={{ fontSize: isComputer && dynamicSize(36, width) }}\r\n                        >\r\n                            {content?.[\"7\"]?.content?.title?.[language]}\r\n                        </h2>\r\n                    </div>\r\n\r\n                    <div className=\"relative w-full\" >\r\n                        {/* Blur effect container */}\r\n                        {\r\n                            !isPhone &&\r\n                            <div className=\"absolute top-0 left-0 h-full w-[20%] bg-gradient-to-r from-white to-transparent pointer-events-none z-10\"></div>\r\n                        }\r\n                        {\r\n                            !isPhone &&\r\n                            <div className=\"absolute top-0 right-0 h-full w-[20%] bg-gradient-to-l from-white to-transparent pointer-events-none z-10\"></div>\r\n                        }\r\n                        {content?.[\"7\"]?.items?.length > 1 &&\r\n                            < Swiper\r\n                                modules={[Navigation, Autoplay, EffectCoverflow]}\r\n                                grabCursor={true}\r\n                                centeredSlides={true}\r\n                                slidesPerView={isPhone ? 1 : 2}\r\n                                loop={true}\r\n                                spaceBetween={10}\r\n                                effect=\"coverflow\"\r\n                                navigation={{\r\n                                    prevEl: testimonialPrevRef.current,\r\n                                    nextEl: testimonialNextRef.current,\r\n                                }}\r\n                                onSwiper={(swiper) => {\r\n                                    swiper.params.navigation.prevEl = testimonialPrevRef.current;\r\n                                    swiper.params.navigation.nextEl = testimonialNextRef.current;\r\n                                    swiper.navigation.init();\r\n                                    swiper.navigation.update();\r\n                                }}\r\n                                coverflowEffect={{\r\n                                    rotate: 0,\r\n                                    stretch: 0,\r\n                                    depth: 250,\r\n                                    modifier: 2,\r\n                                    slideShadows: false,\r\n                                }}\r\n                                autoplay={{ delay: 2500 }}\r\n                                breakpoints={{\r\n                                    724: { slidesPerView: isPhone ? 1 : 1.5 },\r\n                                    500: { slidesPerView: 1 },\r\n                                }}\r\n                            >\r\n                                {content?.[\"7\"]?.items?.map(\r\n                                    (testimonial, index) => (\r\n                                        <SwiperSlide key={index}\r\n                                            dir={isLeftAlign ? \"ltr\" : \"rtl\"}\r\n                                        >\r\n                                            <div className={`border bg-white p-3 rounded-xl flex justify-center  shadow-md`}>\r\n\r\n                                                <div className=\"flex 1\">\r\n                                                    <img\r\n                                                        src={[\"7\"]?.[testimonial?.liveModeVersionData?.image]}\r\n                                                        height={70}\r\n                                                        width={70}\r\n                                                        alt={testimonial?.name}\r\n                                                        className=\"rounded-full h-[70px] w-[75px] object-cover border border-gray-200\"\r\n                                                    />\r\n                                                </div>\r\n\r\n                                                <div className=\"p-5 w-full\">\r\n                                                    <h3 className=\"text-gray-900 text-md font-bold\"\r\n                                                        style={{ fontSize: isComputer && dynamicSize(20, width) }}\r\n                                                    >\r\n                                                        {testimonial?.[titleLan]}\r\n                                                    </h3>\r\n                                                    <p className=\"text-gray-500 text-xs font-light mb-4\"\r\n                                                        style={{ fontSize: isComputer && dynamicSize(12, width) }}\r\n                                                    >\r\n                                                        {testimonial?.liveModeVersionData?.sections?.[0]?.content?.position?.[language]}\r\n                                                    </p>\r\n                                                    <p className=\"text-gray-900 text-xs font-light mb-6 leading-5\"\r\n                                                        style={{ fontSize: isComputer && dynamicSize(14, width) }}\r\n                                                    >\r\n                                                        {testimonial?.liveModeVersionData?.sections?.[0]?.content?.quote?.[language]}\r\n                                                    </p>\r\n                                                    <div className={`flex items-center justify- gap-2`}>\r\n                                                        <img\r\n                                                            src=\"https://frequencyimage.s3.ap-south-1.amazonaws.com/a813959c-7b67-400b-a0b7-f806e63339e5-ph_building%20%281%29.svg\"\r\n                                                            height={18}\r\n                                                            width={18}\r\n                                                            alt={testimonial?.name}\r\n                                                            className=\"h-[18px] w-[18px]\"\r\n                                                        />\r\n                                                        <p className={`text-gray-500 text-base font-bold ${isLeftAlign ? \"text-left\" : \"text-right\"}`}\r\n                                                            style={{ fontSize: isComputer && dynamicSize(16, width) }}\r\n                                                        >\r\n                                                            {testimonial?.liveModeVersionData?.sections?.[0]?.content?.company?.[language]}\r\n                                                        </p>\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                            </div>\r\n                                        </SwiperSlide>\r\n                                    )\r\n                                )}\r\n                            </Swiper>\r\n                        }\r\n\r\n\r\n                        <div className={`flex justify-center items-center gap-7 mt-5 ${!isLeftAlign && \"flex-row-reverse\"}`}>\r\n                            <button\r\n                                ref={testimonialPrevRef}\r\n                                className=\"w-[42px] h-[42px] rounded-full border border-[#00B9F2] flex justify-center items-center cursor-pointer\"\r\n                            >\r\n                                <img\r\n                                    src=\"https://frequencyimage.s3.ap-south-1.amazonaws.com/b2872383-e9d5-4dd7-ae00-8ae00cc4e87e-Vector%20%286%29.svg\"\r\n                                    width=\"22\"\r\n                                    height=\"17\"\r\n                                    alt=\"\"\r\n                                    className={`${isLeftAlign && 'scale-x-[-1]'}`}\r\n                                />\r\n                            </button>\r\n                            <button\r\n                                ref={testimonialNextRef}\r\n                                className=\"w-[42px] h-[42px] rounded-full border border-[#00B9F2] flex justify-center items-center cursor-pointer\"\r\n                            >\r\n                                <img\r\n                                    src=\"https://frequencyimage.s3.ap-south-1.amazonaws.com/de8581fe-4796-404c-a956-8e951ccb355a-Vector%20%287%29.svg\"\r\n                                    width=\"22\"\r\n                                    height=\"17\"\r\n                                    alt=\"\"\r\n                                    className={`${isLeftAlign && 'scale-x-[-1]'}`}\r\n                                />\r\n                            </button>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </section >\r\n\r\n            {/* new project section 8 */}\r\n            < section className={`py-16 w-[100%] ${isPhone ? \"px-[0px] text-justify\" : \"px-[80px]\"} bg-transparent`}\r\n                style={{ padding: `64px ${isComputer ? dynamicSize(143, width) : \"35px\"}` }}\r\n            >\r\n                <div className=\"container mx-auto\">\r\n                    <div className=\"text-center bg-transparent\">\r\n                        <h2 className=\"text-3xl font-medium text-black mb-5\"\r\n                            style={{ fontSize: isComputer && dynamicSize(36, width) }}\r\n                        >\r\n                            {content?.['8']?.content?.title?.[language]}\r\n                        </h2>\r\n                        <div className=\"relative\">\r\n                            <div className={`font-light text-black leading-7 mb-2 relative bg-transparent`}\r\n                                style={{ fontSize: isComputer && dynamicSize(16, width) }}\r\n                                dangerouslySetInnerHTML={{ __html: content?.['8']?.content?.description?.[language] }}\r\n                            />\r\n                            <i\r\n                                className={`absolute ${isLeftAlign ? isPhone ? \"right-[130px] top-[55px]\" : \"right-[250px]\" : \"right-[152px]\"} top-0  opacity-70 z-10 \r\n                                ${language === 'ar' ? 'right-48' : ''}`}\r\n                                style={{\r\n                                    backgroundImage: `url(${highlightsvg})`,\r\n                                    backgroundRepeat: 'no-repeat',\r\n                                    backgroundSize: 'contain',\r\n                                    width: '120px',\r\n                                    height: '100%',\r\n                                    mixBlendMode: 'multiply',\r\n                                }}\r\n                            />\r\n                        </div>\r\n                        <button\r\n                            className=\"bg-[#00B9F2] text-xs text-white px-4 py-2 text-lg mt-11 mx-auto block rounded\"\r\n                            style={{ fontSize: isComputer && dynamicSize(18, width) }}\r\n                        >\r\n                            {content?.['8']?.content?.button?.[0]?.text?.[language]}\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n            </section >\r\n        </div >)\r\n};\r\n\r\nexport default HomePage;"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SACIC,WAAW,EACXC,WAAW,QACR,aAAa;AACpB,OAAOC,KAAK,MAAM,0CAA0C;AAC5D;AACA;AACA,OAAOC,YAAY,MAAM,kCAAkC;AAC3D,SACIC,cAAc,EACdC,OAAO,EACPC,MAAM,EACNC,YAAY,QACT,0BAA0B;AACjC,SAASC,MAAM,EAAEC,WAAW,QAAQ,cAAc;AAClD,SACIC,UAAU,EACVC,UAAU,EACVC,QAAQ,EACRC,eAAe,QACZ,gBAAgB;AACvB,OAAO,uBAAuB;AAC9B,OAAO,YAAY;AACnB,OAAO,uBAAuB;AAC9B,OAAOC,UAAU,MAAM,2CAA2C;AAClE,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,OAAOC,WAAW,IAAIC,gBAAgB,QAAQ,2BAA2B;AACzE,SAASC,aAAa,QAAQ,2BAA2B;AACzD;AACA,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,SAASC,YAAYA,CAACC,MAAM,EAAE;EAC1B,IAAIA,MAAM,GAAG,GAAG,EAAE;IACd,OAAO,UAAU;EACrB,CAAC,MAAM,IAAIA,MAAM,GAAG,GAAG,IAAIA,MAAM,GAAG,GAAG,EAAE;IACrC,OAAO,QAAQ;EACnB,CAAC,MAAM;IACH,OAAO,OAAO;EAClB;AACJ;AAEA,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,QAAQ;EAAEF,MAAM;EAAEG,UAAU;EAAEC,SAAS;EAAEC,OAAO;EAAEC,cAAc;EAAEC,WAAW;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,SAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,aAAA,EAAAC,qBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,cAAA,EAAAC,WAAA,EAAAC,iBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,WAAA,EAAAC,oBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,iBAAA,EAAAC,WAAA,EAAAC,iBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC3G;EACA,MAAMC,eAAe,GAAGpL,SAAS,GAAGT,aAAa,aAAbA,aAAa,wBAAAe,qBAAA,GAAbf,aAAa,CAAE6L,eAAe,cAAA9K,qBAAA,uBAA9BA,qBAAA,CAAgC+K,IAAI,CAAC9L,aAAa,CAAC,GAAG,MAAM,EAAE;EAClG,MAAM+L,UAAU,GAAG1L,MAAM,GAAG,GAAG;EAC/B,MAAM2L,QAAQ,GAAG3L,MAAM,GAAG,GAAG,IAAIA,MAAM,GAAG,GAAG;EAC7C,MAAM4L,OAAO,GAAG5L,MAAM,GAAG,GAAG;EAC5B,MAAM6L,eAAe,GAAGnN,WAAW,CAAEoN,KAAK;IAAA,IAAAC,kBAAA,EAAAC,qBAAA;IAAA,OAAKF,KAAK,aAALA,KAAK,wBAAAC,kBAAA,GAALD,KAAK,CAAEG,WAAW,cAAAF,kBAAA,wBAAAC,qBAAA,GAAlBD,kBAAA,CAAoBG,OAAO,cAAAF,qBAAA,uBAA3BA,qBAAA,CAA6BG,MAAM;EAAA,EAAC;EACnF,MAAMC,SAAS,GAAG1N,WAAW,CAACoN,KAAK,IAAIA,KAAK,CAACO,SAAS,CAACC,KAAK,CAAC;EAC7D;EACA,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGhO,QAAQ,CAAC,IAAI,CAAC;EAC1D,IAAIiO,WAAW,GAAGvM,QAAQ,KAAK,IAAI;EACnC,IAAIwM,aAAa,GAAGD,WAAW,GAAG,WAAW,GAAG,YAAY;EAC5D,MAAME,QAAQ,GAAGF,WAAW,GAAG,SAAS,GAAG,SAAS;EACpD,MAAMG,OAAO,GAAGrO,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMsO,OAAO,GAAGtO,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAM,CAACuO,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGvO,QAAQ,CAAC,CAAC,CAAC;EAC/E,IAAIwO,UAAU,GAAGA,CAACC,KAAK,EAAEC,SAAS,KAAK;IACnC,MAAMC,MAAM,GAAG,EAAE;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAGH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEI,MAAM,GAAED,CAAC,IAAIF,SAAS,EAAE;MAC/CC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEG,IAAI,CAACL,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEM,KAAK,CAACH,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAAC,CAAC;IAChD;IACA,OAAOC,MAAM;EACjB,CAAC;EACD,MAAMK,gBAAgB,GAAG,CAAC;EAE1B,IAAIC,aAAa,GAAGT,UAAU,CAC1B,CAAA3M,OAAO,aAAPA,OAAO,wBAAAM,SAAA,GAAPN,OAAO,CAAG,GAAG,CAAC,cAAAM,SAAA,wBAAAC,kBAAA,GAAdD,SAAA,CAAgB+M,QAAQ,cAAA9M,kBAAA,wBAAAC,qBAAA,GAAxBD,kBAAA,CAA2BkM,0BAA0B,CAAC,cAAAjM,qBAAA,uBAAtDA,qBAAA,CAAwD8M,KAAK,KAAI,EAAE,EACnEH,gBACJ,CAAC;EACD,MAAMI,aAAa,GAAG;IAAE,GAAG/O,cAAc;IAAE,GAAGC,OAAO;IAAE,GAAGC;EAAO,CAAC;EAGlE,MAAM8O,QAAQ,GAAGnO,gBAAgB,CAACK,YAAY,CAACC,MAAM,CAAC,EAAEP,WAAW,EAAEe,KAAK,CAAC;EAE3E,MAAMsN,SAAS,GAAGvP,MAAM,CAAC,IAAI,CAAC;EAE9BD,SAAS,CAAC,MAAM;IACZ,IAAIiO,cAAc,EAAE;MAChBA,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEwB,MAAM,CAAC,CAAC;IAC5B;EACJ,CAAC,EAAE,CAAC7N,QAAQ,CAAC,CAAC;EAEd5B,SAAS,CAAC,MAAM;IAAA,IAAA0P,qBAAA;IACZ,MAAMC,SAAS,GAAGH,SAAS,CAACI,OAAO;IACnC,IAAI,CAACD,SAAS,EAAE;IAEhB,MAAME,QAAQ,IAAAH,qBAAA,GAAGC,SAAS,CAACG,UAAU,cAAAJ,qBAAA,uBAApBA,qBAAA,CAAsBG,QAAQ;IAC/C,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACd,MAAM,KAAK,CAAC,EAAE;IAExC,IAAIgB,KAAK,GAAG,CAAC;IACb,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MAC/B,IAAIF,KAAK,IAAIF,QAAQ,CAACd,MAAM,EAAEgB,KAAK,GAAG,CAAC;MAEvC,MAAMG,KAAK,GAAGL,QAAQ,CAACE,KAAK,CAAC;MAC7B,IAAIG,KAAK,EAAE;QACPP,SAAS,CAACQ,QAAQ,CAAC;UACfC,IAAI,EAAEF,KAAK,CAACG,UAAU,GAAG,EAAE;UAAE;UAC7BC,QAAQ,EAAE;QACd,CAAC,CAAC;MACN;MAEAP,KAAK,EAAE;IACX,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMQ,aAAa,CAACP,QAAQ,CAAC;EACxC,CAAC,EAAE,EAAE,CAAC;EAIN,MAAMQ,kBAAkB,GAAGvQ,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMwQ,kBAAkB,GAAGxQ,MAAM,CAAC,IAAI,CAAC;EACvC,oBACIuB,OAAA;IAAKkP,SAAS,EAAE,mBAAmBtC,aAAa,kCAAmC;IAAAyB,QAAA,gBAE/ErO,OAAA;MAASkP,SAAS,EAAC,iBAAiB;MAAAb,QAAA,gBAChCrO,OAAA;QACIkP,SAAS,EAAE,gDAAgD9O,QAAQ,KAAK,IAAI,GAAG,aAAa,GAAG,cAAc,EACtG;QACP+O,KAAK,EAAE;UAAEC,MAAM,EAAEzP,WAAW,CAAC,GAAG,EAAEe,KAAK;QAAE,CAAE;QAAA2N,QAAA,eAE3CrO,OAAA;UACIqP,GAAG,EAAE1C,WAAW,GAAG,KAAK,GAAG,KAAM;UACjC2C,GAAG,EAAE,GAAGxP,OAAO,GAAGS,OAAO,aAAPA,OAAO,wBAAAS,UAAA,GAAPT,OAAO,CAAG,GAAG,CAAC,cAAAS,UAAA,wBAAAC,kBAAA,GAAdD,UAAA,CAAgBT,OAAO,cAAAU,kBAAA,wBAAAC,qBAAA,GAAvBD,kBAAA,CAAyBoL,MAAM,cAAAnL,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,uBAApCA,sBAAA,CAAsCoO,GAAG,EAAG;UAC9DC,GAAG,EAAEjP,OAAO,aAAPA,OAAO,wBAAAa,UAAA,GAAPb,OAAO,CAAG,GAAG,CAAC,cAAAa,UAAA,wBAAAC,kBAAA,GAAdD,UAAA,CAAgBb,OAAO,cAAAc,kBAAA,wBAAAC,qBAAA,GAAvBD,kBAAA,CAAyBgL,MAAM,cAAA/K,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,uBAApCA,sBAAA,CAAsCgO,GAAI;UAC/CL,SAAS,EAAC,qBAAqB;UAC/BC,KAAK,EAAE;YAAEM,cAAc,EAAE,QAAQ;YAAEC,SAAS,EAAE,YAAY;YAAEN,MAAM,EAAEvD,QAAQ,GAAG,OAAO,GAAGC,OAAO,IAAI;UAAQ;QAAE;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpH,CAAC,eACN9P,OAAA;QACIkP,SAAS,EAAE,8BAA8BtD,UAAU,GAAG,WAAW,GAAG,QAAQ,uBAAwB;QAAAyC,QAAA,eACpGrO,OAAA;UAAKkP,SAAS,EAAE,2BAA2B9O,QAAQ,KAAK,IAAI,GAAG,aAAa,GAAG,WAAW,IAAIwM,aAAa,IAAId,OAAO,GAAG,gBAAgB,GAAG,WAAW,EAAG;UACtJqD,KAAK,EAAE;YAAEY,WAAW,EAAEnE,UAAU,IAAIjM,WAAW,CAAC,GAAG,EAAEe,KAAK;UAAE,CAAE;UAAA2N,QAAA,gBAC9DrO,OAAA;YAAIkP,SAAS,EAAE,GAAIxD,eAAe,CAACnL,OAAO,aAAPA,OAAO,wBAAAiB,UAAA,GAAPjB,OAAO,CAAG,GAAG,CAAC,cAAAiB,UAAA,wBAAAC,kBAAA,GAAdD,UAAA,CAAgBjB,OAAO,cAAAkB,kBAAA,uBAAvBA,kBAAA,CAAyBuO,KAAK,CAAC5P,QAAQ,CAAC,EAAEK,WAAW,aAAXA,WAAW,wBAAAiB,aAAA,GAAXjB,WAAW,CAAG,GAAG,CAAC,cAAAiB,aAAA,wBAAAC,qBAAA,GAAlBD,aAAA,CAAoBnB,OAAO,cAAAoB,qBAAA,uBAA3BA,qBAAA,CAA6BqO,KAAK,CAAC5P,QAAQ,CAAC,CAAC,2FAA4F0L,OAAO,GAAG,QAAQ,GAAGzL,UAAU,GAAG,OAAO,GAAG,OAAO,IAAK;YAC1Q8O,KAAK,EAAE;cAAEpB,QAAQ,EAAEA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEkC,WAAW;cAAEC,UAAU,EAAEtE,UAAU,IAAI,GAAIlL,KAAK,GAAG,IAAI,GAAI,GAAG;YAAO,CAAE;YAAA2N,QAAA,EAEnG9N,OAAO,aAAPA,OAAO,wBAAAqB,UAAA,GAAPrB,OAAO,CAAG,GAAG,CAAC,cAAAqB,UAAA,wBAAAC,kBAAA,GAAdD,UAAA,CAAgBrB,OAAO,cAAAsB,kBAAA,uBAAvBA,kBAAA,CAAyBmO,KAAK,CAAC5P,QAAQ;UAAC;YAAAuP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC,eACL9P,OAAA;YAAGkP,SAAS,EAAE,GAAIxD,eAAe,CAACnL,OAAO,aAAPA,OAAO,wBAAAuB,UAAA,GAAPvB,OAAO,CAAG,GAAG,CAAC,cAAAuB,UAAA,wBAAAC,kBAAA,GAAdD,UAAA,CAAgBvB,OAAO,cAAAwB,kBAAA,uBAAvBA,kBAAA,CAAyBoO,WAAW,CAAC/P,QAAQ,CAAC,EAAEK,WAAW,aAAXA,WAAW,wBAAAuB,cAAA,GAAXvB,WAAW,CAAG,GAAG,CAAC,cAAAuB,cAAA,wBAAAC,qBAAA,GAAlBD,cAAA,CAAoBzB,OAAO,cAAA0B,qBAAA,uBAA3BA,qBAAA,CAA6BkO,WAAW,CAAC/P,QAAQ,CAAC,CAAC,0CAA2C0L,OAAO,GAAG,oBAAoB,GAAG,mBAAmB,iBAAkB;YAClPqD,KAAK,EAAE;cAAEpB,QAAQ,EAAEA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqC,QAAQ;cAAEF,UAAU,EAAEtE,UAAU,IAAI,GAAGlL,KAAK,GAAG,IAAI,GAAG,EAAE;YAAK,CAAE;YAAA2N,QAAA,EAE3F9N,OAAO,aAAPA,OAAO,wBAAA2B,UAAA,GAAP3B,OAAO,CAAG,GAAG,CAAC,cAAA2B,UAAA,wBAAAC,kBAAA,GAAdD,UAAA,CAAgB3B,OAAO,cAAA4B,kBAAA,wBAAAC,qBAAA,GAAvBD,kBAAA,CAAyBgO,WAAW,cAAA/N,qBAAA,uBAApCA,qBAAA,CAAuChC,QAAQ;UAAC;YAAAuP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACJ9P,OAAA;YACIkP,SAAS,EAAE,8BAA8BvC,WAAW,GAAG,EAAE,GAAG,kBAAkB,gGAAiG;YAC/KwC,KAAK,EAAE;cAAEpB,QAAQ,EAAEA,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsC;YAAW,CAAE;YAC1CC,OAAO,EAAEA,CAAA,KAAM,CAAE,CAAE;YAAAjC,QAAA,gBAEnBrO,OAAA;cAAMkP,SAAS,EAAE,GAAIxD,eAAe,CAACnL,OAAO,aAAPA,OAAO,wBAAA8B,UAAA,GAAP9B,OAAO,CAAG,GAAG,CAAC,cAAA8B,UAAA,wBAAAC,kBAAA,GAAdD,UAAA,CAAgB9B,OAAO,cAAA+B,kBAAA,wBAAAC,qBAAA,GAAvBD,kBAAA,CAAyBiO,MAAM,cAAAhO,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAApCD,sBAAA,CAAsCgO,IAAI,cAAA/N,sBAAA,uBAA1CA,sBAAA,CAA6CrC,QAAQ,CAAC,EAAEK,WAAW,aAAXA,WAAW,wBAAAiC,cAAA,GAAXjC,WAAW,CAAG,GAAG,CAAC,cAAAiC,cAAA,wBAAAC,qBAAA,GAAlBD,cAAA,CAAoBnC,OAAO,cAAAoC,qBAAA,wBAAAC,sBAAA,GAA3BD,qBAAA,CAA6B4N,MAAM,cAAA3N,sBAAA,wBAAAC,sBAAA,GAAnCD,sBAAA,CAAsC,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAAxCD,sBAAA,CAA0C2N,IAAI,cAAA1N,sBAAA,uBAA9CA,sBAAA,CAAiD1C,QAAQ,CAAC,CAAC,EAAI;cAAAiO,QAAA,EACvJ9N,OAAO,aAAPA,OAAO,wBAAAwC,UAAA,GAAPxC,OAAO,CAAG,GAAG,CAAC,cAAAwC,UAAA,wBAAAC,kBAAA,GAAdD,UAAA,CAAgBxC,OAAO,cAAAyC,kBAAA,wBAAAC,qBAAA,GAAvBD,kBAAA,CAAyBuN,MAAM,cAAAtN,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAApCD,sBAAA,CAAsCsN,IAAI,cAAArN,sBAAA,uBAA1CA,sBAAA,CAA6C/C,QAAQ;YAAC;cAAAuP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACnE9P,OAAA;cACIsP,GAAG,EAAEzQ,KAAM;cACX6B,KAAK,EAAC,IAAI;cACV0O,MAAM,EAAC,IAAI;cACXI,GAAG,EAAC,EAAE;cACNL,KAAK,EAAE;gBAAEO,SAAS,EAAE/C,WAAW,GAAG,gBAAgB,GAAG,EAAE;gBAAEjM,KAAK,EAAEkL,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;cAAE;YAAE;cAAAiP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1G,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAEV9P,OAAA;MAASkP,SAAS,EAAE,IAAIpD,OAAO,GAAG,gBAAgB,GAAGD,QAAQ,GAAG,sBAAsB,GAAG,uBAAuB,IAAIzL,QAAQ,KAAK,IAAI,GAAG,EAAE,GAAG,gBAAgB,cAAe;MAAAiO,QAAA,eACxKrO,OAAA;QAAKkP,SAAS,EAAE,mCAAmCpD,OAAO,GAAG,UAAU,GAAG,EAAE,IAAIa,WAAW,GAAG,EAAE,GAAG,kBAAkB,eAAgB;QAAA0B,QAAA,gBAEjIrO,OAAA;UAAKkP,SAAS,EAAE,GAAGpD,OAAO,GAAG,SAAS,GAAG,SAAS,kDAAmD;UACjGqD,KAAK,EAAE;YAAEC,MAAM,EAAExD,UAAU,IAAIjM,WAAW,CAAC,GAAG,EAAEe,KAAK,CAAC;YAAEA,KAAK,EAAEkL,UAAU,IAAIjM,WAAW,CAAC,GAAG,EAAEe,KAAK;UAAE,CAAE;UAAA2N,QAAA,eACvGrO,OAAA;YAAKsP,GAAG,EAAE,GAAGxP,OAAO,GAAGS,OAAO,aAAPA,OAAO,wBAAA6C,WAAA,GAAP7C,OAAO,CAAG,GAAG,CAAC,cAAA6C,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgB7C,OAAO,cAAA8C,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBgJ,MAAM,cAAA/I,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,uBAApCA,sBAAA,CAAsCgM,GAAG,EAAG;YAACC,GAAG,EAAC,UAAU;YAACN,SAAS,EAAC,+BAA+B;YACxHC,KAAK,EAAE;cAAEzO,KAAK,EAAEkL,UAAU,IAAIjM,WAAW,CAAC,GAAG,EAAEe,KAAK,CAAC;cAAE0O,MAAM,EAAExD,UAAU,IAAI;YAAO;UAAE;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN9P,OAAA;UAAKkP,SAAS,EAAE,6BAA6BpD,OAAO,GAAG,GAAG,GAAG,WAAW,IAAIa,WAAW,GAAG,mBAAmB,GAAG,mBAAmB,iBAAiBd,QAAQ,GAAG,YAAY,GAAG,YAAY,uBAAwB;UAC9MsD,KAAK,EAAE;YAAEsB,GAAG,EAAE7E,UAAU,GAAGjM,WAAW,CAAC,EAAE,EAAEe,KAAK,CAAC,GAAG,MAAM;YAAEA,KAAK,EAAEkL,UAAU,IAAIjM,WAAW,CAAC,GAAG,EAAEe,KAAK,CAAC;YAAEgQ,OAAO,EAAE9E,UAAU,IAAI,GAAGjM,WAAW,CAAC,EAAE,EAAEe,KAAK,CAAC,IAAIf,WAAW,CAAC,EAAE,EAAEe,KAAK,CAAC;UAAG,CAAE;UAAA2N,QAAA,gBAEzLrO,OAAA;YAAIkP,SAAS,EAAE,wDAAwDxD,eAAe,CAACnL,OAAO,aAAPA,OAAO,wBAAAiD,WAAA,GAAPjD,OAAO,CAAG,GAAG,CAAC,cAAAiD,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBjD,OAAO,cAAAkD,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBuM,KAAK,cAAAtM,qBAAA,uBAA9BA,qBAAA,CAAiCtD,QAAQ,CAAC,EAAEK,WAAW,aAAXA,WAAW,wBAAAkD,cAAA,GAAXlD,WAAW,CAAG,GAAG,CAAC,cAAAkD,cAAA,wBAAAC,qBAAA,GAAlBD,cAAA,CAAoBpD,OAAO,cAAAqD,qBAAA,wBAAAC,sBAAA,GAA3BD,qBAAA,CAA6BoM,KAAK,cAAAnM,sBAAA,uBAAlCA,sBAAA,CAAqCzD,QAAQ,CAAC,CAAC,EAAG;YACjL+O,KAAK,EAAE;cAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK,CAAC;cAAEwP,UAAU,EAAEtE,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;YAAE,CAAE;YAAA2N,QAAA,EAC3G9N,OAAO,aAAPA,OAAO,wBAAAuD,WAAA,GAAPvD,OAAO,CAAG,GAAG,CAAC,cAAAuD,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBvD,OAAO,cAAAwD,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBiM,KAAK,cAAAhM,qBAAA,uBAA9BA,qBAAA,CAAiC5D,QAAQ;UAAC;YAAAuP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACL9P,OAAA;YAAKkP,SAAS,EAAE,oDAAoDxD,eAAe,CAACnL,OAAO,aAAPA,OAAO,wBAAA0D,WAAA,GAAP1D,OAAO,CAAG,GAAG,CAAC,cAAA0D,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgB1D,OAAO,cAAA2D,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBiM,WAAW,cAAAhM,qBAAA,uBAApCA,qBAAA,CAAuC/D,QAAQ,CAAC,EAAEK,WAAW,aAAXA,WAAW,wBAAA2D,cAAA,GAAX3D,WAAW,CAAG,GAAG,CAAC,cAAA2D,cAAA,wBAAAC,qBAAA,GAAlBD,cAAA,CAAoB7D,OAAO,cAAA8D,qBAAA,wBAAAC,sBAAA,GAA3BD,qBAAA,CAA6B8L,WAAW,cAAA7L,sBAAA,uBAAxCA,sBAAA,CAA2ClE,QAAQ,CAAC,CAAC,EAAG;YAC1L+O,KAAK,EAAE;cAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK,CAAC;cAAEwP,UAAU,EAAEtE,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;YAAE,CAAE;YAC5GiQ,uBAAuB,EAAE;cAAEC,MAAM,EAAErQ,OAAO,aAAPA,OAAO,wBAAAgE,WAAA,GAAPhE,OAAO,CAAG,GAAG,CAAC,cAAAgE,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBhE,OAAO,cAAAiE,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyB2L,WAAW,cAAA1L,qBAAA,uBAApCA,qBAAA,CAAuCrE,QAAQ;YAAE;UAAE;YAAAuP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzF,CAAC,eACF9P,OAAA;YAAQkP,SAAS,EAAE,yDAAyDxD,eAAe,CAACnL,OAAO,aAAPA,OAAO,wBAAAmE,WAAA,GAAPnE,OAAO,CAAG,GAAG,CAAC,cAAAmE,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBnE,OAAO,cAAAoE,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBwL,WAAW,cAAAvL,qBAAA,uBAApCA,qBAAA,CAAuCxE,QAAQ,CAAC,EAAEK,WAAW,aAAXA,WAAW,wBAAAoE,cAAA,GAAXpE,WAAW,CAAG,GAAG,CAAC,cAAAoE,cAAA,wBAAAC,qBAAA,GAAlBD,cAAA,CAAoBtE,OAAO,cAAAuE,qBAAA,wBAAAC,sBAAA,GAA3BD,qBAAA,CAA6BqL,WAAW,cAAApL,sBAAA,uBAAxCA,sBAAA,CAA2C3E,QAAQ,CAAC,CAAC,4CAA6C;YAC5O+O,KAAK,EAAE;cAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;YAAE,CAAE;YAAA2N,QAAA,EAEzD9N,OAAO,aAAPA,OAAO,wBAAAyE,WAAA,GAAPzE,OAAO,CAAG,GAAG,CAAC,cAAAyE,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBzE,OAAO,cAAA0E,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBsL,MAAM,cAAArL,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAApCD,sBAAA,CAAsCqL,IAAI,cAAApL,sBAAA,uBAA1CA,sBAAA,CAA6ChF,QAAQ;UAAC;YAAAuP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEX9P,OAAA;MAAUkP,SAAS,EAAC,mBAAmB;MAACC,KAAK,EAAE;QAAE0B,SAAS,EAAE;MAAS,CAAE;MAAAxC,QAAA,eACnErO,OAAA;QAAKkP,SAAS,EAAC,wBAAwB;QACnCC,KAAK,EAAE;UAAEuB,OAAO,EAAE9E,UAAU,IAAI,GAAGjM,WAAW,CAAC,EAAE,EAAEe,KAAK,CAAC,IAAIf,WAAW,CAAC,GAAG,EAAEe,KAAK,CAAC;QAAG,CAAE;QAAA2N,QAAA,gBACzFrO,OAAA;UAAIkP,SAAS,EAAE,uDAAuDpD,OAAO,GAAG,aAAa,GAAG,aAAa;AACjI,sBAAsBJ,eAAe,CAACnL,OAAO,aAAPA,OAAO,wBAAA8E,WAAA,GAAP9E,OAAO,CAAG,GAAG,CAAC,cAAA8E,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgB9E,OAAO,cAAA+E,mBAAA,uBAAvBA,mBAAA,CAAyB0K,KAAK,CAAC5P,QAAQ,CAAC,EAAEK,WAAW,aAAXA,WAAW,wBAAA8E,cAAA,GAAX9E,WAAW,CAAG,GAAG,CAAC,cAAA8E,cAAA,wBAAAC,qBAAA,GAAlBD,cAAA,CAAoBhF,OAAO,cAAAiF,qBAAA,wBAAAC,sBAAA,GAA3BD,qBAAA,CAA6BwK,KAAK,cAAAvK,sBAAA,uBAAlCA,sBAAA,CAAqCrF,QAAQ,CAAC,CAAC;AAC/H,qBAAsB;UACE+O,KAAK,EAAE;YAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;UAAE,CAAE;UAAA2N,QAAA,EACzD9N,OAAO,aAAPA,OAAO,wBAAAmF,WAAA,GAAPnF,OAAO,CAAG,GAAG,CAAC,cAAAmF,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBnF,OAAO,cAAAoF,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBqK,KAAK,cAAApK,qBAAA,uBAA9BA,qBAAA,CAAiCxF,QAAQ;QAAC;UAAAuP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAEL9P,OAAA;UAAKkP,SAAS,EAAE,GAAGpD,OAAO,GAAG,qBAAqB,GAAG,mEAAmE;AAC5I,sBAAsBJ,eAAe,CAACnL,OAAO,aAAPA,OAAO,wBAAAsF,WAAA,GAAPtF,OAAO,CAAG,GAAG,CAAC,cAAAsF,WAAA,uBAAdA,WAAA,CAAgBgI,KAAK,EAAEpN,WAAW,aAAXA,WAAW,wBAAAqF,cAAA,GAAXrF,WAAW,CAAG,GAAG,CAAC,cAAAqF,cAAA,uBAAlBA,cAAA,CAAoB+H,KAAK,CAAC;AACvF,qBAAsB;UACEsB,KAAK,EAAE;YAAE2B,SAAS,EAAElF,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK,CAAC;YAAEqQ,MAAM,EAAEnF,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;UAAE,CAAE;UAAA2N,QAAA,EACxG9N,OAAO,aAAPA,OAAO,wBAAAwF,WAAA,GAAPxF,OAAO,CAAG,GAAG,CAAC,cAAAwF,WAAA,wBAAAC,iBAAA,GAAdD,WAAA,CAAgB8H,KAAK,cAAA7H,iBAAA,uBAArBA,iBAAA,CAAuBgL,GAAG,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK;YAAA,IAAAC,qBAAA;YACvC,oBACInR,OAAA;cAAekP,SAAS,EAAE,gIAAgIgC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,0BAA0B,GAAG,eAAe,GAAI;cAAA7C,QAAA,eACtNrO,OAAA;gBAAKkP,SAAS,EAAC,kCAAkC;gBAAAb,QAAA,gBAC7CrO,OAAA;kBAAKsP,GAAG,EAAExP,OAAO,IAAGmR,IAAI,aAAJA,IAAI,wBAAAE,qBAAA,GAAJF,IAAI,CAAEG,mBAAmB,cAAAD,qBAAA,uBAAzBA,qBAAA,CAA2BE,IAAI,CAAC;kBAAC3Q,KAAK,EAAE,EAAG;kBAAC0O,MAAM,EAAE,EAAG;kBAACI,GAAG,EAAC,MAAM;kBAACN,SAAS,EAAC;gBAAW;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/G9P,OAAA;kBAAIkP,SAAS,EAAE,0CAA2C;kBACtDC,KAAK,EAAE;oBAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;kBAAE,CAAE;kBAAA2N,QAAA,GACzD4C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAGpE,QAAQ,CAAC,eACjB7M,OAAA;oBAAMkP,SAAS,EAAC;kBAA6C;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC,GARAoB,GAAG;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASR,CAAC;UACd,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEX9P,OAAA;MAAUkP,SAAS,EAAE,8BAA8BtD,UAAU,GAAGvL,UAAU,GAAG,mBAAmB,GAAG,aAAa,GAAG,CAACsM,WAAW,GAAG,MAAM,GAAG,OAAO,EAAG;MAAC0C,GAAG,EAAE1C,WAAW,GAAG,KAAK,GAAG,KAAM;MAAA0B,QAAA,eACnLrO,OAAA;QACIkP,SAAS,EAAE,0BAA0BpD,OAAO,GAAG,sBAAsB,GAAG,QAAQ,GAAI;QAAAuC,QAAA,gBACpFrO,OAAA;UAAKkP,SAAS,EAAE,oBAAqB;UAAAb,QAAA,eAEjCrO,OAAA;YAAKkP,SAAS,EAAE;AACxC,0BAA0BxD,eAAe,CAAC,CAAC;AAC3C,0BAA0BG,QAAQ,GACD,CAACc,WAAW,GAAG,cAAc,GAAG,aAAa,GAC9Cf,UAAU,IAAIvL,UAAU,GAAG,0BAA0B,GACjDyL,OAAO,GAAG5L,MAAM,GAAG,GAAG,GAAG,yBAAyB,GAC9C,sBAAsB,GAAG,yBAAyB;AAC1F,8BAA8B,CAACyM,WAAW,IAAIb,OAAO,IAAI,eAAe;YAChD;YAAA;YAAAuC,QAAA,EAEK9N,OAAO,aAAPA,OAAO,wBAAA0F,WAAA,GAAP1F,OAAO,CAAG,GAAG,CAAC,cAAA0F,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgB1F,OAAO,cAAA2F,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBoL,KAAK,cAAAnL,qBAAA,uBAA9BA,qBAAA,CAAgC6K,GAAG,CAAC,CAACO,IAAI,EAAEL,GAAG,KAAK;cAChD;cACA,MAAMM,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACR,GAAG,GAAG,CAAC,CAAC,GAAG,GAAG,IAAIA,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;cACzE,oBACIlR,OAAA;gBAEImP,KAAK,EAAE;kBAAEwC,GAAG,EAAE,GAAGH,QAAQ,IAAI;kBAAEI,MAAM,EAAEV,GAAG,GAAG;gBAAE,CAAE;gBACjDhC,SAAS,EAAE,wDAAwDgC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAACvE,WAAW,GAAG,cAAc,GAAG,iBAAiB,GAAG,QAAQ,EAAG;gBAAA0B,QAAA,gBAElJrO,OAAA;kBAAKkP,SAAS,EAAC,UAAU;kBAAAb,QAAA,eACrBrO,OAAA;oBACIkP,SAAS,EAAE,YAAYgC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,2BAA2B,GAAG,0BAA0B,EAAG;oBAClG5B,GAAG,EAAExP,OAAO,IAAGyR,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEF,IAAI,CAAC;oBAC1B3Q,KAAK,EAAE,EAAG;oBACV0O,MAAM,EAAE8B,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,EAAG;oBAC5B1B,GAAG,EAAC;kBAAE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,eACN9P,OAAA;kBAAIkP,SAAS,EAAC,sDAAsD;kBAChEC,KAAK,EAAE;oBACHpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;kBACjD,CAAE;kBAAA2N,QAAA,EACJkD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEM;gBAAK;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACnB9P,OAAA;kBAAIkP,SAAS,EAAE,8DAA8DvC,WAAW,GAAG,qBAAqB,GAAG,sBAAsB,kEAAmE;kBACxMwC,KAAK,EAAE;oBACHpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;kBACjD,CAAE;kBAAA2N,QAAA,EAEDkD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEvB,KAAK,CAAC5P,QAAQ;gBAAC;kBAAAuP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA,GAxBAoB,GAAG;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyBP,CAAC;YAEd,CAAC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN9P,OAAA;UAAKkP,SAAS,EAAE,iBAAiBrD,QAAQ,GAAG,CAACc,WAAW,GAAG,WAAW,GAAG,WAAW,GAAG,WAAW,KAAKtM,UAAU,GAAG,UAAU,GAAG,UAAU,EAAG;UAC1I8O,KAAK,EAAE;YACH;YACA;UAAA,CACF;UAAAd,QAAA,gBAEFrO,OAAA;YAAIkP,SAAS,EAAE,yDAAyDxD,eAAe,CAACnL,OAAO,aAAPA,OAAO,wBAAA6F,WAAA,GAAP7F,OAAO,CAAG,GAAG,CAAC,cAAA6F,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgB7F,OAAO,cAAA8F,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyB2J,KAAK,cAAA1J,qBAAA,uBAA9BA,qBAAA,CAAiClG,QAAQ,CAAC,CAAC,EAAEK,WAAW,aAAXA,WAAW,wBAAA8F,cAAA,GAAX9F,WAAW,CAAG,GAAG,CAAC,cAAA8F,cAAA,wBAAAC,qBAAA,GAAlBD,cAAA,CAAoBhG,OAAO,cAAAiG,qBAAA,wBAAAC,sBAAA,GAA3BD,qBAAA,CAA6BwJ,KAAK,cAAAvJ,sBAAA,uBAAlCA,sBAAA,CAAqCrG,QAAQ,CAAC,EAAG;YAClL+O,KAAK,EAAE;cACHpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK,CAAC;cAC9CwP,UAAU,EAAEtE,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;YACnD,CAAE;YAAA2N,QAAA,EACD9N,OAAO,aAAPA,OAAO,wBAAAmG,WAAA,GAAPnG,OAAO,CAAG,GAAG,CAAC,cAAAmG,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBnG,OAAO,cAAAoG,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBqJ,KAAK,cAAApJ,qBAAA,uBAA9BA,qBAAA,CAAiCxG,QAAQ;UAAC;YAAAuP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACL9P,OAAA;YAAGkP,SAAS,EAAE,0BAA0B5C,SAAS,mBAAmB/L,OAAO,aAAPA,OAAO,gBAAAsG,WAAA,GAAPtG,OAAO,CAAG,GAAG,CAAC,cAAAsG,WAAA,gBAAAC,mBAAA,GAAdD,WAAA,CAAgBtG,OAAO,cAAAuG,mBAAA,gBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBqJ,WAAW,cAAApJ,qBAAA,eAApCA,qBAAA,CAAuC3G,QAAQ,CAAC,EAAEK,WAAW,aAAXA,WAAW,wBAAAuG,eAAA,GAAXvG,WAAW,CAAG,GAAG,CAAC,cAAAuG,eAAA,wBAAAC,qBAAA,GAAlBD,eAAA,CAAoBzG,OAAO,cAAA0G,qBAAA,wBAAAC,sBAAA,GAA3BD,qBAAA,CAA6BkJ,WAAW,cAAAjJ,sBAAA,uBAAxCA,sBAAA,CAA2C9G,QAAQ,CAAC,EAAG;YACzK+O,KAAK,EAAE;cACH2C,UAAU,EAAE,KAAK;cACjB/D,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;YACjD,CAAE;YAAA2N,QAAA,EAED9N,OAAO,aAAPA,OAAO,wBAAA4G,WAAA,GAAP5G,OAAO,CAAG,GAAG,CAAC,cAAA4G,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgB5G,OAAO,cAAA6G,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyB+I,WAAW,cAAA9I,qBAAA,uBAApCA,qBAAA,CAAuCjH,QAAQ;UAAC;YAAAuP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eACJ9P,OAAA;YACIkP,SAAS,EAAE,qEAAqE,CAACvC,WAAW,GAAG,OAAO,GAAG,EAAE,EAAG;YAAA0B,QAAA,eAE9GrO,OAAA;cAAMkP,SAAS,EAAE,GAAGxD,eAAe,CAACnL,OAAO,aAAPA,OAAO,wBAAA+G,WAAA,GAAP/G,OAAO,CAAG,GAAG,CAAC,cAAA+G,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgB/G,OAAO,cAAAgH,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBgJ,MAAM,cAAA/I,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAApCD,sBAAA,CAAsC+I,IAAI,cAAA9I,sBAAA,uBAA1CA,sBAAA,CAA6CtH,QAAQ,CAAC,EAAEK,WAAW,aAAXA,WAAW,wBAAAkH,eAAA,GAAXlH,WAAW,CAAG,GAAG,CAAC,cAAAkH,eAAA,wBAAAC,qBAAA,GAAlBD,eAAA,CAAoBpH,OAAO,cAAAqH,qBAAA,wBAAAC,sBAAA,GAA3BD,qBAAA,CAA6B2I,MAAM,cAAA1I,sBAAA,wBAAAC,sBAAA,GAAnCD,sBAAA,CAAsC,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAAxCD,sBAAA,CAA0C0I,IAAI,cAAAzI,sBAAA,uBAA9CA,sBAAA,CAAiD3H,QAAQ,CAAC,CAAC,EAAG;cAAAiO,QAAA,EACrJ9N,OAAO,aAAPA,OAAO,wBAAAyH,WAAA,GAAPzH,OAAO,CAAG,GAAG,CAAC,cAAAyH,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBzH,OAAO,cAAA0H,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBsI,MAAM,cAAArI,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAApCD,sBAAA,CAAsCqI,IAAI,cAAApI,sBAAA,uBAA1CA,sBAAA,CAA6ChI,QAAQ;YAAC;cAAAuP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGX9P,OAAA;MAAUkP,SAAS,EAAE,aAAapD,OAAO,GAAG,MAAM,GAAG,MAAM,YAAa;MAACuD,GAAG,EAAE1C,WAAW,GAAG,KAAK,GAAG,KAAM;MACtGwC,KAAK,EAAE;QAAEuB,OAAO,EAAE9E,UAAU,IAAI,QAAQjM,WAAW,CAAC,GAAG,EAAEe,KAAK,CAAC;MAAI,CAAE;MAAA2N,QAAA,eACrErO,OAAA;QAAKkP,SAAS,EAAE,oCAAoC,CAACvC,WAAW,IAAI,kBAAkB,IAAI,CAACA,WAAW,IAAId,QAAQ,IAAI,YAAY,EAAG;QAAAwC,QAAA,gBACjIrO,OAAA;UAAKkP,SAAS,EAAE,yCAAyCvC,WAAW,GAAG,SAAS,GAAG,QAAQ,EAAG;UAAA0B,QAAA,EACzFrB,0BAA0B,KAAK,CAAC,GAAI,EAAE,gBACnChN,OAAA;YACI+R,IAAI,EAAC,QAAQ;YACb7C,SAAS,EAAE,kIAAmI;YAC9IC,KAAK,EAAE;cAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;YAAE,CAAE;YAC1D4P,OAAO,EAAEA,CAAA,KAAM,CAAE,CAAE;YAAAjC,QAAA,GAClB7N,cAAc,aAAdA,cAAc,wBAAA6H,gBAAA,GAAd7H,cAAc,CAAG,GAAG,CAAC,cAAA6H,gBAAA,wBAAAC,qBAAA,GAArBD,gBAAA,CAAuBkI,MAAM,cAAAjI,qBAAA,wBAAAC,sBAAA,GAA7BD,qBAAA,CAAgC,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAAlCD,sBAAA,CAAoCiI,IAAI,cAAAhI,sBAAA,uBAAxCA,sBAAA,CAA2CpI,QAAQ,CAAC,eACrDJ,OAAA;cACIsP,GAAG,EAAC,yGAAyG;cAC7G5O,KAAK,EAAC,IAAI;cACV0O,MAAM,EAAC,IAAI;cACXI,GAAG,EAAC,EAAE;cACNN,SAAS,EAAE,qBAAqBvC,WAAW,GAAG,wBAAwB,GAAG,EAAE;YAAG;cAAAgD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QACX;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEN9P,OAAA;UAAKkP,SAAS,EAAE,QAAQrD,QAAQ,GAAGC,OAAO,GAAG,YAAY,GAAG,YAAY,GAAG,YAAY,IAAIa,WAAW,IAAI,CAACf,UAAU,IAAI,OAAO,EAAG;UAC/HuD,KAAK,EAAE;YAAEsB,GAAG,EAAE7E,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK,CAAC;YAAEA,KAAK,EAAEkL,UAAU,IAAIvL,UAAU,GAAGV,WAAW,CAAC,IAAI,EAAEe,KAAK,CAAC,GAAG;UAAO,CAAE;UAAA2N,QAAA,gBAC1HrO,OAAA;YAAKkP,SAAS,EAAE,6BAA6BrD,QAAQ,GAAGC,OAAO,GAAG,WAAW,GAAG,WAAW,GAAG,EAAE,EAAG;YAC/FqD,KAAK,EAAE;cAAEzO,KAAK,EAAEkL,UAAU,IAAIvL,UAAU,GAAGV,WAAW,CAAC,GAAG,EAAEe,KAAK,CAAC,GAAG;YAAG,CAAE;YAAA2N,QAAA,EACzE9N,OAAO,aAAPA,OAAO,wBAAAkI,WAAA,GAAPlI,OAAO,CAAG,GAAG,CAAC,cAAAkI,WAAA,wBAAAC,oBAAA,GAAdD,WAAA,CAAgBmF,QAAQ,cAAAlF,oBAAA,uBAAxBA,oBAAA,CAA0BsI,GAAG,CAAC,CAACgB,OAAO,EAAEzD,KAAK;cAAA,IAAA0D,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;cAAA,oBAC1CpS,OAAA;gBAEIkP,SAAS,EAAE,WAAY;gBAAAb,QAAA,gBAEvBrO,OAAA;kBAAMkP,SAAS,EAAElC,0BAA0B,KAAKuB,KAAK,GAC/C,4DAA4D,GAC5D,mDAAoD;kBACtD+B,OAAO,EAAEA,CAAA,KAAMrD,6BAA6B,CAACsB,KAAK,CAAE;kBAAAF,QAAA,eAEpDrO,OAAA;oBAAIkP,SAAS,EAAE,GAAGlC,0BAA0B,KAAKuB,KAAK,GAAG,gBAAgB,GAAG,gBAAgB,yBAA0B;oBAClH+B,OAAO,EAAEA,CAAA,KAAMrD,6BAA6B,CAACsB,KAAK,CAAE;oBACpDY,KAAK,EAAE;sBAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;oBAAE,CAAE;oBAAA2N,QAAA,EAEzD2D,OAAO,aAAPA,OAAO,wBAAAC,gBAAA,GAAPD,OAAO,CAAEzR,OAAO,cAAA0R,gBAAA,wBAAAC,qBAAA,GAAhBD,gBAAA,CAAkBjC,KAAK,cAAAkC,qBAAA,uBAAvBA,qBAAA,CAA0B9R,QAAQ;kBAAC;oBAAAuP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEP9P,OAAA;kBAAGkP,SAAS,EAAE,GAAG5C,SAAS,IAAIU,0BAA0B,KAAKuB,KAAK,GAC5D,qHAAqH,GACrH,6HAA6H,EAC5H;kBACHY,KAAK,EAAE;oBAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;kBAAE,CAAE;kBAAA2N,QAAA,EAEzD2D,OAAO,aAAPA,OAAO,wBAAAG,iBAAA,GAAPH,OAAO,CAAEzR,OAAO,cAAA4R,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkBhC,WAAW,cAAAiC,qBAAA,uBAA7BA,qBAAA,CAAgChS,QAAQ;gBAAC;kBAAAuP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC;cAAA,GAvBCvB,KAAK;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwBT,CAAC;YAAA,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEN9P,OAAA;YAAKkP,SAAS,EAAE,GAAGpD,OAAO,GAAG,WAAW,GAAGD,QAAQ,GAAG,WAAW,GAAG,EAAE,EAAG;YACrEsD,KAAK,EAAE;cAAEzO,KAAK,EAAEkL,UAAU,IAAIvL,UAAU,GAAGV,WAAW,CAAC,GAAG,EAAEe,KAAK,CAAC,GAAG;YAAG,CAAE;YAAA2N,QAAA,gBAE1ErO,OAAA,CAACb,MAAM;cAEHkT,OAAO,EAAE,CAAChT,UAAU,EAAEC,UAAU,CAAE;cAClC4P,SAAS,EAAE,eAAgB;cAC3BC,KAAK,EAAE;gBAAEzO,KAAK,EAAE;cAAO,CAAE;cACzB4R,UAAU,EAAE;gBACRC,MAAM,EAAEzF,OAAO,CAACsB,OAAO;gBACvBoE,MAAM,EAAEzF,OAAO,CAACqB;cACpB,CAAE;cACFqE,QAAQ,EAAGC,MAAM,IAAK;gBAClBhG,iBAAiB,CAACgG,MAAM,CAAC;gBACzBA,MAAM,CAACC,MAAM,CAACL,UAAU,CAACC,MAAM,GAAGzF,OAAO,CAACsB,OAAO;gBACjDsE,MAAM,CAACC,MAAM,CAACL,UAAU,CAACE,MAAM,GAAGzF,OAAO,CAACqB,OAAO;gBACjDsE,MAAM,CAACJ,UAAU,CAACM,IAAI,CAAC,CAAC;gBACxBF,MAAM,CAACJ,UAAU,CAACrE,MAAM,CAAC,CAAC;cAC9B,CAAE;cAAAI,QAAA,EAEDV,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEqD,GAAG,CAAC,CAAC6B,KAAK,EAAEC,UAAU,KAAK;gBACvC,oBACI9S,OAAA,CAACZ,WAAW;kBAAAiP,QAAA,eACRrO,OAAA;oBAAKkP,SAAS,EAAE,GAAGpD,OAAO,GAAG,eAAe,GAAG,8CAA8CD,QAAQ,GAAG,WAAW,GAAG,WAAW,EAAE,EAAG;oBAClIsD,KAAK,EAAE;sBACHzO,KAAK,EAAEkL,UAAU,GAAGjM,WAAW,CAAC,GAAG,EAAEe,KAAK,CAAC,GAAGoL,OAAO,GAAG,GAAI,GAAG,GAAG,IAAI,GAAI5L,MAAM,IAAI,GAAG,GAAI,GAAG,GAAG,IAAI,GAAIA,MAAM,IAAI;sBACnHuQ,GAAG,EAAE7E,UAAU,GAAG,EAAE,GAAG,GAAI,EAAE,GAAG,IAAI,GAAI1L,MAAM,IAAI;sBAClD6S,UAAU,EAAE;oBAChB,CAAE;oBAAA1E,QAAA,EAEDwE,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE7B,GAAG,CAAC,CAACgC,OAAO,EAAEC,SAAS,KAAK;sBAChC,oBACIjT,OAAA;wBAAKkP,SAAS,EAAC,6BAA6B;wBAAAb,QAAA,gBACxCrO,OAAA;0BAAKkP,SAAS,EAAE,wBAAyB;0BAAAb,QAAA,eACrCrO,OAAA;4BACIkP,SAAS,EAAE,kDAAmD;4BAC9DM,GAAG,EAAEwD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG5S,QAAQ,CAAE;4BACzBkP,GAAG,EAAEvD,eAAe,aAAfA,eAAe,eAAfA,eAAe,CAAGiH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,KAAK,CAAC,GAAGnH,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAGiH,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,KAAK,CAAC,GAAGF,OAAO,aAAPA,OAAO,eAAPA,OAAO,CAAEE,KAAK,GACrFpF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAGkF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEE,KAAK,CAAC,GAC/BnU,cAAc,CAACoU;0BAAM;4BAAAxD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC9B;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC,eACN9P,OAAA;0BAAKkP,SAAS,EAAC,iGAAiG;0BAAAb,QAAA,gBAE5GrO,OAAA;4BACIgQ,KAAK,EAAEgD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGnG,QAAQ,CAAE;4BAC3BqC,SAAS,EAAE,kDAAkD,CAACtD,UAAU,IAAI,MAAM,EAAG;4BACrFuD,KAAK,EAAE;8BAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;4BAAE,CAAE;4BAAA2N,QAAA,EAEzD3O,YAAY,CAACsT,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGnG,QAAQ,CAAC,EAAE,CAACjB,UAAU,GAAG,EAAE,GAAG,EAAE;0BAAC;4BAAA+D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzD,CAAC,eACL9P,OAAA;4BACIgQ,KAAK,EAAEgD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGnG,QAAQ,CAAE;4BAC3BqC,SAAS,EAAC,oDAAoD;4BAC9DC,KAAK,EAAE;8BAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;4BAAE,CAAE;4BAAA2N,QAAA,EAEzD3O,YAAY,CAACsT,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAGnG,QAAQ,CAAC,EAAGhB,QAAQ,GAAG,EAAE,GAAG,EAAG;0BAAC;4BAAA8D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACzD,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA,GA1BwCmD,SAAS;wBAAAtD,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA2BtD,CAAC;oBAEd,CACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACA;gBAAC,GAzCQgD,UAAU;kBAAAnD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA0Cf,CAAC;cAEtB,CAAC;YAAC,GA9DG1P,QAAQ;cAAAuP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+DT,CAAC,eAGT9P,OAAA;cACIkP,SAAS,EAAE,2DAA4D;cACvEC,KAAK,EAAE;gBAAEzO,KAAK,EAAEkL,UAAU,GAAG,EAAE,GAAGE,OAAO,GAAG,OAAO,GAAG,GAAI,GAAG,GAAG,IAAI,GAAI5L,MAAM;cAAK;cACvF;cAAA;cAAAmO,QAAA,gBAEIrO,OAAA;gBAAQoT,GAAG,EAAEtG,OAAQ;gBAACoC,SAAS,EAAE,8HAA8HpD,OAAO,GAAG,WAAW,GAAG,eAAe,uDAAwD;gBAC1PqD,KAAK,EAAE;kBAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;gBAAE,CAAE;gBAAA2N,QAAA,gBAC1DrO,OAAA;kBACIsP,GAAG,EAAC,8GAA8G;kBAClH5O,KAAK,EAAC,IAAI;kBACV0O,MAAM,EAAC,IAAI;kBACXI,GAAG,EAAC,EAAE;kBACNN,SAAS,EAAE,qBAAqB9O,QAAQ,KAAK,IAAI,IAAI,wBAAwB;gBAAG;kBAAAuP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC,EAED,CAAChE,OAAO,KACLtL,cAAc,aAAdA,cAAc,wBAAAmI,iBAAA,GAAdnI,cAAc,CAAG,GAAG,CAAC,cAAAmI,iBAAA,wBAAAC,qBAAA,GAArBD,iBAAA,CAAuB0K,OAAO,cAAAzK,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAiC,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAAnCD,sBAAA,CAAqC2H,IAAI,cAAA1H,sBAAA,uBAAzCA,sBAAA,CAA4C1I,QAAQ,CAAC;cAAA;gBAAAuP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAErD,CAAC,eACT9P,OAAA;gBAAQoT,GAAG,EAAErG,OAAQ;gBAACmC,SAAS,EAAE,8HAA8HpD,OAAO,GAAG,WAAW,GAAG,eAAe,sDAAuD;gBACzPqD,KAAK,EAAE;kBAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;gBAAE,CAAE;gBAAA2N,QAAA,GACzD,CAACvC,OAAO,KACLtL,cAAc,aAAdA,cAAc,wBAAAuI,iBAAA,GAAdvI,cAAc,CAAG,GAAG,CAAC,cAAAuI,iBAAA,wBAAAC,qBAAA,GAArBD,iBAAA,CAAuBsK,OAAO,cAAArK,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAiC,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAAnCD,sBAAA,CAAqCuH,IAAI,cAAAtH,sBAAA,uBAAzCA,sBAAA,CAA4C9I,QAAQ,CAAC,gBAEzDJ,OAAA;kBACIsP,GAAG,EAAC,8GAA8G;kBAClH5O,KAAK,EAAC,IAAI;kBACV0O,MAAM,EAAC,IAAI;kBACXI,GAAG,EAAC,EAAE;kBACNN,SAAS,EAAE,qBAAqBvC,WAAW,IAAI,wBAAwB;gBAAG;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7E,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGX9P,OAAA;MAASkP,SAAS,EAAC,6BAA6B;MAAAb,QAAA,gBAC5CrO,OAAA;QACIsP,GAAG,EAAC,kHAAkH;QACtH5O,KAAK,EAAC,KAAK;QACX0O,MAAM,EAAC,KAAK;QACZI,GAAG,EAAC,UAAU;QACdN,SAAS,EAAC;MAAuB;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eACF9P,OAAA;QACIsP,GAAG,EAAC,kHAAkH;QACtH5O,KAAK,EAAC,KAAK;QACX0O,MAAM,EAAC,KAAK;QACZI,GAAG,EAAC,UAAU;QACdN,SAAS,EAAC;MAA2B;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,eACF9P,OAAA;QAAKkP,SAAS,EAAC,mBAAmB;QAAAb,QAAA,gBAC9BrO,OAAA;UAAKkP,SAAS,EAAC,uBAAuB;UAAAb,QAAA,gBAClCrO,OAAA;YAAIkP,SAAS,EAAE;AACvC,0BAA0BxD,eAAe,CAACnL,OAAO,aAAPA,OAAO,wBAAA4I,WAAA,GAAP5I,OAAO,CAAG,GAAG,CAAC,cAAA4I,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgB5I,OAAO,cAAA6I,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyB4G,KAAK,cAAA3G,qBAAA,uBAA9BA,qBAAA,CAAiCjJ,QAAQ,CAAC,EAAEK,WAAW,aAAXA,WAAW,wBAAA6I,eAAA,GAAX7I,WAAW,CAAG,GAAG,CAAC,cAAA6I,eAAA,wBAAAC,qBAAA,GAAlBD,eAAA,CAAoB/I,OAAO,cAAAgJ,qBAAA,wBAAAC,sBAAA,GAA3BD,qBAAA,CAA6ByG,KAAK,cAAAxG,sBAAA,uBAAlCA,sBAAA,CAAqCpJ,QAAQ,CAAC,CAAC;AACrI,yBAA0B;YACE+O,KAAK,EAAE;cAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;YAAE,CAAE;YAAA2N,QAAA,EAEzD9N,OAAO,aAAPA,OAAO,wBAAAkJ,WAAA,GAAPlJ,OAAO,CAAG,GAAG,CAAC,cAAAkJ,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBlJ,OAAO,cAAAmJ,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBsG,KAAK,cAAArG,qBAAA,uBAA9BA,qBAAA,CAAiCvJ,QAAQ;UAAC;YAAAuP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACL9P,OAAA;YAAGkP,SAAS,EAAE;AACtC,0BAA0BxD,eAAe,CAACnL,OAAO,aAAPA,OAAO,wBAAAqJ,WAAA,GAAPrJ,OAAO,CAAG,GAAG,CAAC,cAAAqJ,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBrJ,OAAO,cAAAsJ,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBsG,WAAW,cAAArG,qBAAA,uBAApCA,qBAAA,CAAuC1J,QAAQ,CAAC,EAAEK,WAAW,aAAXA,WAAW,wBAAAsJ,eAAA,GAAXtJ,WAAW,CAAG,GAAG,CAAC,cAAAsJ,eAAA,wBAAAC,qBAAA,GAAlBD,eAAA,CAAoBxJ,OAAO,cAAAyJ,qBAAA,wBAAAC,sBAAA,GAA3BD,qBAAA,CAA6BmG,WAAW,cAAAlG,sBAAA,uBAAxCA,sBAAA,CAA2C7J,QAAQ,CAAC,CAAC;AACjJ,yBAA0B;YACE+O,KAAK,EAAE;cAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;YAAE,CAAE;YAAA2N,QAAA,EAEzD9N,OAAO,aAAPA,OAAO,wBAAA2J,WAAA,GAAP3J,OAAO,CAAG,GAAG,CAAC,cAAA2J,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgB3J,OAAO,cAAA4J,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBgG,WAAW,cAAA/F,qBAAA,uBAApCA,qBAAA,CAAuChK,QAAQ;UAAC;YAAAuP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN9P,OAAA;UAAKoT,GAAG,EAAEpF,SAAU;UAACkB,SAAS,EAAE,6CAA8C;UAC1EC,KAAK,EAAE;YAAEuB,OAAO,EAAE9E,UAAU,GAAG,GAAGjM,WAAW,CAAC,EAAE,EAAEe,KAAK,CAAC,IAAIf,WAAW,CAAC,EAAE,EAAEe,KAAK,CAAC,EAAE,GAAG;UAAG,CAAE;UAAA2N,QAAA,eAE5FrO,OAAA;YAAKkP,SAAS,EAAE,gCAAgCpD,OAAO,GAAG,+BAA+B,GAAG,kCAAkC,EAAG;YAC7HqD,KAAK,EAAE;cAAEsB,GAAG,EAAE,CAAC3E,OAAO,GAAID,QAAQ,GAAGlM,WAAW,CAAC,GAAG,EAAEe,KAAK,CAAC,GAAGf,WAAW,CAAC,GAAG,EAAEe,KAAK,CAAC,GAAIf,WAAW,CAAC,GAAG,EAAEe,KAAK;YAAE,CAAE;YAAA2N,QAAA,EAEnH9N,OAAO,aAAPA,OAAO,wBAAA8J,WAAA,GAAP9J,OAAO,CAAG,GAAG,CAAC,cAAA8J,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgB9J,OAAO,cAAA+J,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBgJ,aAAa,cAAA/I,qBAAA,uBAAtCA,qBAAA,CAAwCyG,GAAG,CAAC,CAACuC,MAAM,EAAErC,GAAG;cAAA,IAAAsC,eAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;cAAA,oBACrD3T,OAAA;gBAEIkP,SAAS,EAAE;AAC/C,0CAA0CxD,eAAe,CAAC6H,MAAM,CAAChE,GAAG,EAAE9O,WAAW,aAAXA,WAAW,wBAAA+S,eAAA,GAAX/S,WAAW,CAAG,GAAG,CAAC,cAAA+S,eAAA,wBAAAC,qBAAA,GAAlBD,eAAA,CAAoBjT,OAAO,cAAAkT,qBAAA,wBAAAC,sBAAA,GAA3BD,qBAAA,CAA6BH,aAAa,cAAAI,sBAAA,wBAAAC,sBAAA,GAA1CD,sBAAA,CAA6CxC,GAAG,CAAC,cAAAyC,sBAAA,uBAAjDA,sBAAA,CAAmDpE,GAAG,CAAC;AAC7H,yCAA0C;gBACNJ,KAAK,EAAE;kBACHzO,KAAK,EAAEkL,UAAU,IAAIjM,WAAW,CAAC,GAAG,EAAEe,KAAK,CAAC;kBAC5C0O,MAAM,EAAExD,UAAU,IAAIjM,WAAW,CAAC,GAAG,EAAEe,KAAK;gBAChD,CAAE;gBAAA2N,QAAA,eAEFrO,OAAA;kBACIsP,GAAG,EAAExP,OAAO,IAAGyT,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEhE,GAAG,CAAC;kBAC3B7O,KAAK,EAAEwQ,GAAG,KAAK,CAAC,GAAG,GAAG,GAAG,EAAG;kBAC5B9B,MAAM,EAAE8B,GAAG,KAAK,CAAC,GAAG,EAAE,GAAG,EAAG;kBAC5B1B,GAAG,EAAC,UAAU;kBACdN,SAAS,EAAC;gBAAgB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B;cAAC,GAfGoB,GAAG;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBP,CAAC;YAAA,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGV9P,OAAA;MACIkP,SAAS,EAAE,uBAAuB,CAACvC,WAAW,IAAI,KAAK,mCAAoC;MAC3FwC,KAAK,EAAE;QACHzO,KAAK,EAAEkL,UAAU,GAAG,OAAO,GAAG,GAAG1L,MAAM,GAAG,EAAE;MAChD,CAAE;MAAAmO,QAAA,eAEFrO,OAAA;QAAKkP,SAAS,EAAC,mBAAmB;QAAAb,QAAA,gBAC9BrO,OAAA;UAAKkP,SAAS,EAAC,mBAAmB;UAAAb,QAAA,eAC9BrO,OAAA;YAAIkP,SAAS,EAAC,iCAAiC;YAC3CC,KAAK,EAAE;cAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;YAAE,CAAE;YAAA2N,QAAA,EAEzD9N,OAAO,aAAPA,OAAO,wBAAAiK,WAAA,GAAPjK,OAAO,CAAG,GAAG,CAAC,cAAAiK,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBjK,OAAO,cAAAkK,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBuF,KAAK,cAAAtF,qBAAA,uBAA9BA,qBAAA,CAAiCtK,QAAQ;UAAC;YAAAuP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eAEN9P,OAAA;UAAKkP,SAAS,EAAC,iBAAiB;UAAAb,QAAA,GAGxB,CAACvC,OAAO,iBACR9L,OAAA;YAAKkP,SAAS,EAAC;UAA0G;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAGhI,CAAChE,OAAO,iBACR9L,OAAA;YAAKkP,SAAS,EAAC;UAA2G;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,EAEpI,CAAAvP,OAAO,aAAPA,OAAO,wBAAAoK,WAAA,GAAPpK,OAAO,CAAG,GAAG,CAAC,cAAAoK,WAAA,wBAAAC,iBAAA,GAAdD,WAAA,CAAgBkD,KAAK,cAAAjD,iBAAA,uBAArBA,iBAAA,CAAuB2C,MAAM,IAAG,CAAC,iBAC9BvN,OAAA,CAAEb,MAAM;YACJkT,OAAO,EAAE,CAAC/S,UAAU,EAAEC,QAAQ,EAAEC,eAAe,CAAE;YACjDoU,UAAU,EAAE,IAAK;YACjBC,cAAc,EAAE,IAAK;YACrBC,aAAa,EAAEhI,OAAO,GAAG,CAAC,GAAG,CAAE;YAC/BiI,IAAI,EAAE,IAAK;YACXC,YAAY,EAAE,EAAG;YACjBC,MAAM,EAAC,WAAW;YAClB3B,UAAU,EAAE;cACRC,MAAM,EAAEvD,kBAAkB,CAACZ,OAAO;cAClCoE,MAAM,EAAEvD,kBAAkB,CAACb;YAC/B,CAAE;YACFqE,QAAQ,EAAGC,MAAM,IAAK;cAClBA,MAAM,CAACC,MAAM,CAACL,UAAU,CAACC,MAAM,GAAGvD,kBAAkB,CAACZ,OAAO;cAC5DsE,MAAM,CAACC,MAAM,CAACL,UAAU,CAACE,MAAM,GAAGvD,kBAAkB,CAACb,OAAO;cAC5DsE,MAAM,CAACJ,UAAU,CAACM,IAAI,CAAC,CAAC;cACxBF,MAAM,CAACJ,UAAU,CAACrE,MAAM,CAAC,CAAC;YAC9B,CAAE;YACFiG,eAAe,EAAE;cACbC,MAAM,EAAE,CAAC;cACTC,OAAO,EAAE,CAAC;cACVC,KAAK,EAAE,GAAG;cACVC,QAAQ,EAAE,CAAC;cACXC,YAAY,EAAE;YAClB,CAAE;YACFC,QAAQ,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC1BC,WAAW,EAAE;cACT,GAAG,EAAE;gBAAEZ,aAAa,EAAEhI,OAAO,GAAG,CAAC,GAAG;cAAI,CAAC;cACzC,GAAG,EAAE;gBAAEgI,aAAa,EAAE;cAAE;YAC5B,CAAE;YAAAzF,QAAA,EAED9N,OAAO,aAAPA,OAAO,wBAAAsK,WAAA,GAAPtK,OAAO,CAAG,GAAG,CAAC,cAAAsK,WAAA,wBAAAC,iBAAA,GAAdD,WAAA,CAAgBgD,KAAK,cAAA/C,iBAAA,uBAArBA,iBAAA,CAAuBkG,GAAG,CACvB,CAAC2D,WAAW,EAAEpG,KAAK;cAAA,IAAAqG,IAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA,EAAAC,uBAAA;cAAA,oBACf5V,OAAA,CAACZ,WAAW;gBACRiQ,GAAG,EAAE1C,WAAW,GAAG,KAAK,GAAG,KAAM;gBAAA0B,QAAA,eAEjCrO,OAAA;kBAAKkP,SAAS,EAAE,+DAAgE;kBAAAb,QAAA,gBAE5ErO,OAAA;oBAAKkP,SAAS,EAAC,QAAQ;oBAAAb,QAAA,eACnBrO,OAAA;sBACIsP,GAAG,GAAAsF,IAAA,GAAE,CAAC,GAAG,CAAC,cAAAA,IAAA,uBAALA,IAAA,CAAQD,WAAW,aAAXA,WAAW,wBAAAE,qBAAA,GAAXF,WAAW,CAAEvD,mBAAmB,cAAAyD,qBAAA,uBAAhCA,qBAAA,CAAkC3B,KAAK,CAAE;sBACtD9D,MAAM,EAAE,EAAG;sBACX1O,KAAK,EAAE,EAAG;sBACV8O,GAAG,EAAEmF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEkB,IAAK;sBACvB3G,SAAS,EAAC;oBAAoE;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjF;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eAEN9P,OAAA;oBAAKkP,SAAS,EAAC,YAAY;oBAAAb,QAAA,gBACvBrO,OAAA;sBAAIkP,SAAS,EAAC,iCAAiC;sBAC3CC,KAAK,EAAE;wBAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;sBAAE,CAAE;sBAAA2N,QAAA,EAEzDsG,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAG9H,QAAQ;oBAAC;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACL9P,OAAA;sBAAGkP,SAAS,EAAC,uCAAuC;sBAChDC,KAAK,EAAE;wBAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;sBAAE,CAAE;sBAAA2N,QAAA,EAEzDsG,WAAW,aAAXA,WAAW,wBAAAG,sBAAA,GAAXH,WAAW,CAAEvD,mBAAmB,cAAA0D,sBAAA,wBAAAC,sBAAA,GAAhCD,sBAAA,CAAkClH,QAAQ,cAAAmH,sBAAA,wBAAAC,sBAAA,GAA1CD,sBAAA,CAA6C,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAA/CD,sBAAA,CAAiDzU,OAAO,cAAA0U,sBAAA,wBAAAC,sBAAA,GAAxDD,sBAAA,CAA0Da,QAAQ,cAAAZ,sBAAA,uBAAlEA,sBAAA,CAAqE9U,QAAQ;oBAAC;sBAAAuP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChF,CAAC,eACJ9P,OAAA;sBAAGkP,SAAS,EAAC,iDAAiD;sBAC1DC,KAAK,EAAE;wBAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;sBAAE,CAAE;sBAAA2N,QAAA,EAEzDsG,WAAW,aAAXA,WAAW,wBAAAQ,sBAAA,GAAXR,WAAW,CAAEvD,mBAAmB,cAAA+D,sBAAA,wBAAAC,sBAAA,GAAhCD,sBAAA,CAAkCvH,QAAQ,cAAAwH,sBAAA,wBAAAC,sBAAA,GAA1CD,sBAAA,CAA6C,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,uBAAA,GAA/CD,sBAAA,CAAiD9U,OAAO,cAAA+U,uBAAA,wBAAAC,uBAAA,GAAxDD,uBAAA,CAA0DS,KAAK,cAAAR,uBAAA,uBAA/DA,uBAAA,CAAkEnV,QAAQ;oBAAC;sBAAAuP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7E,CAAC,eACJ9P,OAAA;sBAAKkP,SAAS,EAAE,kCAAmC;sBAAAb,QAAA,gBAC/CrO,OAAA;wBACIsP,GAAG,EAAC,mHAAmH;wBACvHF,MAAM,EAAE,EAAG;wBACX1O,KAAK,EAAE,EAAG;wBACV8O,GAAG,EAAEmF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEkB,IAAK;wBACvB3G,SAAS,EAAC;sBAAmB;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChC,CAAC,eACF9P,OAAA;wBAAGkP,SAAS,EAAE,qCAAqCvC,WAAW,GAAG,WAAW,GAAG,YAAY,EAAG;wBAC1FwC,KAAK,EAAE;0BAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;wBAAE,CAAE;wBAAA2N,QAAA,EAEzDsG,WAAW,aAAXA,WAAW,wBAAAa,uBAAA,GAAXb,WAAW,CAAEvD,mBAAmB,cAAAoE,uBAAA,wBAAAC,uBAAA,GAAhCD,uBAAA,CAAkC5H,QAAQ,cAAA6H,uBAAA,wBAAAC,uBAAA,GAA1CD,uBAAA,CAA6C,CAAC,CAAC,cAAAC,uBAAA,wBAAAC,uBAAA,GAA/CD,uBAAA,CAAiDnV,OAAO,cAAAoV,uBAAA,wBAAAC,uBAAA,GAAxDD,uBAAA,CAA0DK,OAAO,cAAAJ,uBAAA,uBAAjEA,uBAAA,CAAoExV,QAAQ;sBAAC;wBAAAuP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/E,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEL;cAAC,GA/CQvB,KAAK;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgDV,CAAC;YAAA,CAEtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC,eAIb9P,OAAA;YAAKkP,SAAS,EAAE,+CAA+C,CAACvC,WAAW,IAAI,kBAAkB,EAAG;YAAA0B,QAAA,gBAChGrO,OAAA;cACIoT,GAAG,EAAEpE,kBAAmB;cACxBE,SAAS,EAAC,wGAAwG;cAAAb,QAAA,eAElHrO,OAAA;gBACIsP,GAAG,EAAC,8GAA8G;gBAClH5O,KAAK,EAAC,IAAI;gBACV0O,MAAM,EAAC,IAAI;gBACXI,GAAG,EAAC,EAAE;gBACNN,SAAS,EAAE,GAAGvC,WAAW,IAAI,cAAc;cAAG;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACT9P,OAAA;cACIoT,GAAG,EAAEnE,kBAAmB;cACxBC,SAAS,EAAC,wGAAwG;cAAAb,QAAA,eAElHrO,OAAA;gBACIsP,GAAG,EAAC,8GAA8G;gBAClH5O,KAAK,EAAC,IAAI;gBACV0O,MAAM,EAAC,IAAI;gBACXI,GAAG,EAAC,EAAE;gBACNN,SAAS,EAAE,GAAGvC,WAAW,IAAI,cAAc;cAAG;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGX9P,OAAA;MAAUkP,SAAS,EAAE,kBAAkBpD,OAAO,GAAG,uBAAuB,GAAG,WAAW,iBAAkB;MACpGqD,KAAK,EAAE;QAAEuB,OAAO,EAAE,QAAQ9E,UAAU,GAAGjM,WAAW,CAAC,GAAG,EAAEe,KAAK,CAAC,GAAG,MAAM;MAAG,CAAE;MAAA2N,QAAA,eAE5ErO,OAAA;QAAKkP,SAAS,EAAC,mBAAmB;QAAAb,QAAA,eAC9BrO,OAAA;UAAKkP,SAAS,EAAC,4BAA4B;UAAAb,QAAA,gBACvCrO,OAAA;YAAIkP,SAAS,EAAC,sCAAsC;YAChDC,KAAK,EAAE;cAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;YAAE,CAAE;YAAA2N,QAAA,EAEzD9N,OAAO,aAAPA,OAAO,wBAAAwK,WAAA,GAAPxK,OAAO,CAAG,GAAG,CAAC,cAAAwK,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBxK,OAAO,cAAAyK,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBgF,KAAK,cAAA/E,qBAAA,uBAA9BA,qBAAA,CAAiC7K,QAAQ;UAAC;YAAAuP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACL9P,OAAA;YAAKkP,SAAS,EAAC,UAAU;YAAAb,QAAA,gBACrBrO,OAAA;cAAKkP,SAAS,EAAE,8DAA+D;cAC3EC,KAAK,EAAE;gBAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;cAAE,CAAE;cAC1DiQ,uBAAuB,EAAE;gBAAEC,MAAM,EAAErQ,OAAO,aAAPA,OAAO,wBAAA2K,WAAA,GAAP3K,OAAO,CAAG,GAAG,CAAC,cAAA2K,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgB3K,OAAO,cAAA4K,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBgF,WAAW,cAAA/E,qBAAA,uBAApCA,qBAAA,CAAuChL,QAAQ;cAAE;YAAE;cAAAuP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC,eACF9P,OAAA;cACIkP,SAAS,EAAE,YAAYvC,WAAW,GAAGb,OAAO,GAAG,0BAA0B,GAAG,eAAe,GAAG,eAAe;AAC7I,kCAAkC1L,QAAQ,KAAK,IAAI,GAAG,UAAU,GAAG,EAAE,EAAG;cACxC+O,KAAK,EAAE;gBACH8G,eAAe,EAAE,OAAOnX,YAAY,GAAG;gBACvCoX,gBAAgB,EAAE,WAAW;gBAC7BC,cAAc,EAAE,SAAS;gBACzBzV,KAAK,EAAE,OAAO;gBACd0O,MAAM,EAAE,MAAM;gBACdgH,YAAY,EAAE;cAClB;YAAE;cAAAzG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN9P,OAAA;YACIkP,SAAS,EAAC,+EAA+E;YACzFC,KAAK,EAAE;cAAEpB,QAAQ,EAAEnC,UAAU,IAAIjM,WAAW,CAAC,EAAE,EAAEe,KAAK;YAAE,CAAE;YAAA2N,QAAA,EAEzD9N,OAAO,aAAPA,OAAO,wBAAA8K,WAAA,GAAP9K,OAAO,CAAG,GAAG,CAAC,cAAA8K,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgB9K,OAAO,cAAA+K,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBiF,MAAM,cAAAhF,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAApCD,sBAAA,CAAsCgF,IAAI,cAAA/E,sBAAA,uBAA1CA,sBAAA,CAA6CrL,QAAQ;UAAC;YAAAuP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AACf,CAAC;AAACnP,EAAA,CArqBIR,QAAQ;EAAA,QAMcvB,WAAW,EACjBA,WAAW;AAAA;AAAAyX,EAAA,GAP3BlW,QAAQ;AAuqBd,eAAeA,QAAQ;AAAC,IAAAkW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}