{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\shade_cms\\\\dashboard\\\\src\\\\features\\\\Resources\\\\components\\\\AllForOne.jsx\",\n  _s = $RefreshSig$();\nimport HomePage from \"./websiteComponent/Home\";\nimport SolutionPage from \"./websiteComponent/Solutions\";\nimport AboutUs from \"./websiteComponent/About\";\nimport MarketPage from \"./websiteComponent/Market\";\nimport ProjectPage from \"./websiteComponent/Projects\";\nimport CareerPage from \"./websiteComponent/CareersPage\";\nimport NewsPage from \"./websiteComponent/NewsPage\";\nimport Footer from \"./websiteComponent/subparts/Footerweb\";\nimport Header from \"./websiteComponent/subparts/Headerweb\";\nimport ProjectDetailPage from \"./websiteComponent/detailspages/ProjectDetails\";\nimport CareerDetailPage from \"./websiteComponent/detailspages/CareersDetails\";\nimport NewsBlogDetailPage from \"./websiteComponent/detailspages/NewsDetails\";\nimport Testimonials from \"./websiteComponent/subparts/Testimonials\";\nimport ContactUsModal from \"./websiteComponent/subparts/ContactUsModal\";\nimport Services from \"./websiteComponent/Service\";\nimport ServiceDetails from \"./websiteComponent/detailspages/ServiceDetails\";\nimport SubServiceDetails from \"./websiteComponent/subDetailsPages/SubServiceDetails\";\nimport tempContent from \"./websiteComponent/content.json\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useEffect, useRef, useState } from \"react\";\nimport { updateMainContent } from \"../../common/homeContentSlice\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AllForOne = ({\n  language,\n  screen,\n  content,\n  subPath,\n  setLanguage,\n  fullScreen,\n  currentPath,\n  deepPath,\n  showDifference = false,\n  live,\n  hideScroll\n}) => {\n  _s();\n  // console.log(content)\n  const dispatch = useDispatch();\n  const fontRegular = useSelector(state => state.fontStyle.regular);\n\n  // const platform = useSelector(state => state.platform.platform)\n\n  // useEffect(() => {\n  //     if (platform !== \"EDIT\") {\n  //         return () => dispatch(updateMainContent({ currentPath: \"content\", payload: undefined }))\n  //     }\n  // }, [platform])\n  const divRef = useRef(null);\n  const [width, setWidth] = useState(0);\n  useEffect(() => {\n    const observer = new ResizeObserver(entries => {\n      for (let entry of entries) {\n        setWidth(entry.contentRect.width);\n      }\n    });\n    if (divRef.current) {\n      observer.observe(divRef.current);\n    }\n    return () => {\n      if (divRef.current) {\n        observer.unobserve(divRef.current);\n      }\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    ref: divRef,\n    className: `dark:text-[#2A303C] mt-0 \n            ${fullScreen ? \"overflow-y-hidden\" : \"overflow-y-scroll\"}\n             ${hideScroll ? \"rm-scroll\" : \"customscroller\"} transition-custom border-stone-200 border mx-auto w-full ${fontRegular} bg-[white]`,\n    style: {\n      width: screen > 950 ? \"100%\" : screen,\n      wordBreak: \"break-word\"\n    },\n    children: [currentPath === \"home\" && /*#__PURE__*/_jsxDEV(HomePage, {\n      language: language,\n      screen: screen,\n      fullScreen: fullScreen,\n      content: content,\n      highlight: showDifference,\n      liveContent: live,\n      width: width\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 17\n    }, this), currentPath === \"solution\" && /*#__PURE__*/_jsxDEV(SolutionPage, {\n      language: language,\n      currentContent: content,\n      screen: screen,\n      width: width\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 17\n    }, this), currentPath === \"about-us\" && /*#__PURE__*/_jsxDEV(AboutUs, {\n      language: language,\n      currentContent: content,\n      screen: screen,\n      width: width\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 17\n    }, this), currentPath === \"service\" ? subPath ? deepPath ? /*#__PURE__*/_jsxDEV(SubServiceDetails, {\n      width: width,\n      language: language,\n      content: content,\n      serviceId: subPath,\n      screen: screen,\n      deepPath: deepPath\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 21\n    }, this) : /*#__PURE__*/_jsxDEV(ServiceDetails, {\n      width: width,\n      language: language,\n      content: content,\n      serviceId: subPath,\n      screen: screen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 21\n    }, this) : /*#__PURE__*/_jsxDEV(Services, {\n      language: language,\n      width: width,\n      currentContent: content,\n      screen: screen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 21\n    }, this) : \"\", currentPath === \"market\" && /*#__PURE__*/_jsxDEV(MarketPage, {\n      width: width,\n      language: language,\n      currentContent: content,\n      screen: screen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 17\n    }, this), currentPath === 'projects' || currentPath === 'project' ? subPath ? /*#__PURE__*/_jsxDEV(ProjectDetailPage, {\n      width: width,\n      language: language,\n      contentOn: content === null || content === void 0 ? void 0 : content.projectDetail,\n      projectId: subPath,\n      screen: screen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 21\n    }, this) : /*#__PURE__*/_jsxDEV(ProjectPage, {\n      width: width,\n      language: language,\n      currentContent: content,\n      screen: screen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 21\n    }, this) : \"\", currentPath === \"careers\" ? subPath ? /*#__PURE__*/_jsxDEV(CareerDetailPage, {\n      width: width,\n      language: language,\n      contentOn: content === null || content === void 0 ? void 0 : content.careerDetails,\n      careerId: subPath,\n      screen: screen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 21\n    }, this) : /*#__PURE__*/_jsxDEV(CareerPage, {\n      width: width,\n      language: language,\n      currentContent: content,\n      screen: screen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 21\n    }, this) : \"\", currentPath === \"news-blogs\" ? subPath ? /*#__PURE__*/_jsxDEV(NewsBlogDetailPage, {\n      width: width,\n      language: language,\n      contentOn: content === null || content === void 0 ? void 0 : content.newsBlogsDetails,\n      newsId: subPath,\n      screen: screen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 21\n    }, this) : /*#__PURE__*/_jsxDEV(NewsPage, {\n      language: language,\n      currentContent: content,\n      screen: screen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 21\n    }, this) : \"\", currentPath === \"footer\" && /*#__PURE__*/_jsxDEV(Footer, {\n      width: width,\n      language: language,\n      currentContent: content,\n      screen: screen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 17\n    }, this), currentPath === \"header\" && /*#__PURE__*/_jsxDEV(Header, {\n      width: width,\n      language: language,\n      currentContent: content,\n      screen: screen,\n      setLanguage: setLanguage\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 17\n    }, this), currentPath === \"testimonials\" || currentPath === \"testimonial\" && /*#__PURE__*/_jsxDEV(Testimonials, {\n      width: width,\n      language: language,\n      currentContent: content,\n      screen: screen,\n      testimonyId: subPath\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 17\n    }, this), currentPath === 'contactus-modal' && /*#__PURE__*/_jsxDEV(ContactUsModal, {\n      width: width,\n      language: language,\n      currentContent: content,\n      screen: screen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 156,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 9\n  }, this);\n};\n_s(AllForOne, \"T5YnMwzC/VYX8yIY53dS90fXh2o=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = AllForOne;\nexport default AllForOne;\nvar _c;\n$RefreshReg$(_c, \"AllForOne\");", "map": {"version": 3, "names": ["HomePage", "SolutionPage", "AboutUs", "MarketPage", "ProjectPage", "CareerPage", "NewsPage", "Footer", "Header", "ProjectDetailPage", "CareerDetailPage", "NewsBlogDetailPage", "Testimonials", "ContactUsModal", "Services", "ServiceDetails", "SubServiceDetails", "temp<PERSON><PERSON><PERSON>", "useDispatch", "useSelector", "useEffect", "useRef", "useState", "update<PERSON>ain<PERSON><PERSON>nt", "jsxDEV", "_jsxDEV", "AllForOne", "language", "screen", "content", "subPath", "setLanguage", "fullScreen", "currentPath", "<PERSON><PERSON><PERSON>", "showDifference", "live", "hideScroll", "_s", "dispatch", "fontRegular", "state", "fontStyle", "regular", "divRef", "width", "<PERSON><PERSON><PERSON><PERSON>", "observer", "ResizeObserver", "entries", "entry", "contentRect", "current", "observe", "unobserve", "ref", "className", "style", "wordBreak", "children", "highlight", "liveContent", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "currentC<PERSON>nt", "serviceId", "contentOn", "projectDetail", "projectId", "careerDetails", "careerId", "newsBlogsDetails", "newsId", "testimonyId", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/shade_cms/dashboard/src/features/Resources/components/AllForOne.jsx"], "sourcesContent": ["import HomePage from \"./websiteComponent/Home\";\r\nimport SolutionPage from \"./websiteComponent/Solutions\";\r\nimport AboutUs from \"./websiteComponent/About\";\r\nimport MarketPage from \"./websiteComponent/Market\";\r\nimport ProjectPage from \"./websiteComponent/Projects\";\r\nimport CareerPage from \"./websiteComponent/CareersPage\";\r\nimport NewsPage from \"./websiteComponent/NewsPage\";\r\nimport Footer from \"./websiteComponent/subparts/Footerweb\";\r\nimport Header from \"./websiteComponent/subparts/Headerweb\";\r\nimport ProjectDetailPage from \"./websiteComponent/detailspages/ProjectDetails\";\r\nimport CareerDetailPage from \"./websiteComponent/detailspages/CareersDetails\";\r\nimport NewsBlogDetailPage from \"./websiteComponent/detailspages/NewsDetails\";\r\nimport Testimonials from \"./websiteComponent/subparts/Testimonials\";\r\nimport ContactUsModal from \"./websiteComponent/subparts/ContactUsModal\";\r\nimport Services from \"./websiteComponent/Service\";\r\nimport ServiceDetails from \"./websiteComponent/detailspages/ServiceDetails\";\r\nimport SubServiceDetails from \"./websiteComponent/subDetailsPages/SubServiceDetails\";\r\nimport tempContent from \"./websiteComponent/content.json\"\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useEffect, useRef, useState } from \"react\";\r\nimport { updateMainContent } from \"../../common/homeContentSlice\";\r\n\r\nconst AllForOne = ({ language, screen, content, subPath, setLanguage, fullScreen, currentPath, deepPath, showDifference = false, live, hideScroll }) => {\r\n    // console.log(content)\r\n    const dispatch = useDispatch()\r\n    const fontRegular = useSelector(state => state.fontStyle.regular)\r\n\r\n    // const platform = useSelector(state => state.platform.platform)\r\n\r\n    // useEffect(() => {\r\n    //     if (platform !== \"EDIT\") {\r\n    //         return () => dispatch(updateMainContent({ currentPath: \"content\", payload: undefined }))\r\n    //     }\r\n    // }, [platform])\r\n    const divRef = useRef(null);\r\n    const [width, setWidth] = useState(0);\r\n\r\n    useEffect(() => {\r\n        const observer = new ResizeObserver(entries => {\r\n            for (let entry of entries) {\r\n                setWidth(entry.contentRect.width);\r\n            }\r\n        });\r\n\r\n        if (divRef.current) {\r\n            observer.observe(divRef.current);\r\n        }\r\n\r\n        return () => {\r\n            if (divRef.current) {\r\n                observer.unobserve(divRef.current);\r\n            }\r\n        };\r\n    }, []);\r\n\r\n    return (\r\n        <div\r\n            ref={divRef}\r\n            className={`dark:text-[#2A303C] mt-0 \r\n            ${fullScreen ? \"overflow-y-hidden\" : \"overflow-y-scroll\"}\r\n             ${hideScroll ? \"rm-scroll\" : \"customscroller\"} transition-custom border-stone-200 border mx-auto w-full ${fontRegular} bg-[white]`}\r\n            style={{ width: screen > 950 ? \"100%\" : screen, wordBreak: \"break-word\" }}\r\n        >\r\n            {\r\n                currentPath === \"home\" &&\r\n                <HomePage language={language}\r\n                    screen={screen}\r\n                    fullScreen={fullScreen}\r\n                    content={content}\r\n                    highlight={showDifference}\r\n                    liveContent={live}\r\n                    width={width}\r\n                />\r\n            }\r\n            {\r\n                currentPath === \"solution\" &&\r\n                <SolutionPage language={language} currentContent={content} screen={screen}\r\n                    width={width}\r\n                />\r\n            }\r\n            {\r\n                currentPath === \"about-us\" &&\r\n                <AboutUs language={language} currentContent={content} screen={screen}\r\n                    width={width}\r\n                />\r\n            }\r\n            {\r\n                currentPath === \"service\" ? subPath ? deepPath ?\r\n                    <SubServiceDetails\r\n                        width={width}\r\n                        language={language} content={content} serviceId={subPath} screen={screen} deepPath={deepPath} /> :\r\n                    <ServiceDetails\r\n                        width={width}\r\n                        language={language} content={content} serviceId={subPath} screen={screen} /> :\r\n                    <Services language={language}\r\n                        width={width}\r\n                        currentContent={content} screen={screen} /> : \"\"\r\n            }\r\n            {/* {\r\n                (currentPath === \"service\" && subPath) &&\r\n                <ServiceDetails language={language} contentOn={content?.serviceDetails} serviceId={subPath} screen={screen} />\r\n            } */}\r\n            {\r\n                currentPath === \"market\" &&\r\n                <MarketPage\r\n                    width={width}\r\n                    language={language} currentContent={content} screen={screen} />\r\n            }\r\n            {\r\n                currentPath === 'projects' || currentPath === 'project' ? subPath ?\r\n                    <ProjectDetailPage\r\n                        width={width}\r\n                        language={language} contentOn={content?.projectDetail} projectId={subPath} screen={screen} /> :\r\n                    <ProjectPage\r\n                        width={width}\r\n                        language={language} currentContent={content} screen={screen} /> : \"\"\r\n            }\r\n            {\r\n                currentPath === \"careers\" ? subPath ?\r\n                    <CareerDetailPage\r\n                        width={width}\r\n                        language={language} contentOn={content?.careerDetails} careerId={subPath} screen={screen} /> :\r\n                    <CareerPage\r\n                        width={width}\r\n                        language={language} currentContent={content} screen={screen} /> : \"\"\r\n            }\r\n            {\r\n                currentPath === \"news-blogs\" ? subPath ?\r\n                    <NewsBlogDetailPage\r\n                        width={width}\r\n                        language={language} contentOn={content?.newsBlogsDetails} newsId={subPath} screen={screen} /> :\r\n                    <NewsPage language={language} currentContent={content} screen={screen} /> : \"\"\r\n            }\r\n\r\n            {/* sub pages */}\r\n            {\r\n                currentPath === \"footer\" &&\r\n                <Footer\r\n                    width={width}\r\n                    language={language} currentContent={content} screen={screen} />\r\n            }\r\n            {\r\n                currentPath === \"header\" &&\r\n                <Header\r\n                    width={width}\r\n                    language={language} currentContent={content} screen={screen} setLanguage={setLanguage} />\r\n            }\r\n            {\r\n                currentPath === \"testimonials\" || currentPath === \"testimonial\" &&\r\n                <Testimonials\r\n                    width={width}\r\n                    language={language} currentContent={content} screen={screen} testimonyId={subPath} />\r\n            }\r\n            {\r\n                currentPath === 'contactus-modal' &&\r\n                <ContactUsModal\r\n                    width={width}\r\n                    language={language} currentContent={content} screen={screen} />\r\n            }\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default AllForOne"], "mappings": ";;AAAA,OAAOA,QAAQ,MAAM,yBAAyB;AAC9C,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,UAAU,MAAM,2BAA2B;AAClD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,QAAQ,MAAM,6BAA6B;AAClD,OAAOC,MAAM,MAAM,uCAAuC;AAC1D,OAAOC,MAAM,MAAM,uCAAuC;AAC1D,OAAOC,iBAAiB,MAAM,gDAAgD;AAC9E,OAAOC,gBAAgB,MAAM,gDAAgD;AAC7E,OAAOC,kBAAkB,MAAM,6CAA6C;AAC5E,OAAOC,YAAY,MAAM,0CAA0C;AACnE,OAAOC,cAAc,MAAM,4CAA4C;AACvE,OAAOC,QAAQ,MAAM,4BAA4B;AACjD,OAAOC,cAAc,MAAM,gDAAgD;AAC3E,OAAOC,iBAAiB,MAAM,sDAAsD;AACpF,OAAOC,WAAW,MAAM,iCAAiC;AACzD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,iBAAiB,QAAQ,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,SAAS,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,MAAM;EAAEC,OAAO;EAAEC,OAAO;EAAEC,WAAW;EAAEC,UAAU;EAAEC,WAAW;EAAEC,QAAQ;EAAEC,cAAc,GAAG,KAAK;EAAEC,IAAI;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EACpJ;EACA,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAMsB,WAAW,GAAGrB,WAAW,CAACsB,KAAK,IAAIA,KAAK,CAACC,SAAS,CAACC,OAAO,CAAC;;EAEjE;;EAEA;EACA;EACA;EACA;EACA;EACA,MAAMC,MAAM,GAAGvB,MAAM,CAAC,IAAI,CAAC;EAC3B,MAAM,CAACwB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;EAErCF,SAAS,CAAC,MAAM;IACZ,MAAM2B,QAAQ,GAAG,IAAIC,cAAc,CAACC,OAAO,IAAI;MAC3C,KAAK,IAAIC,KAAK,IAAID,OAAO,EAAE;QACvBH,QAAQ,CAACI,KAAK,CAACC,WAAW,CAACN,KAAK,CAAC;MACrC;IACJ,CAAC,CAAC;IAEF,IAAID,MAAM,CAACQ,OAAO,EAAE;MAChBL,QAAQ,CAACM,OAAO,CAACT,MAAM,CAACQ,OAAO,CAAC;IACpC;IAEA,OAAO,MAAM;MACT,IAAIR,MAAM,CAACQ,OAAO,EAAE;QAChBL,QAAQ,CAACO,SAAS,CAACV,MAAM,CAACQ,OAAO,CAAC;MACtC;IACJ,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,oBACI3B,OAAA;IACI8B,GAAG,EAAEX,MAAO;IACZY,SAAS,EAAE;AACvB,cAAcxB,UAAU,GAAG,mBAAmB,GAAG,mBAAmB;AACpE,eAAeK,UAAU,GAAG,WAAW,GAAG,gBAAgB,6DAA6DG,WAAW,aAAc;IACpIiB,KAAK,EAAE;MAAEZ,KAAK,EAAEjB,MAAM,GAAG,GAAG,GAAG,MAAM,GAAGA,MAAM;MAAE8B,SAAS,EAAE;IAAa,CAAE;IAAAC,QAAA,GAGtE1B,WAAW,KAAK,MAAM,iBACtBR,OAAA,CAACzB,QAAQ;MAAC2B,QAAQ,EAAEA,QAAS;MACzBC,MAAM,EAAEA,MAAO;MACfI,UAAU,EAAEA,UAAW;MACvBH,OAAO,EAAEA,OAAQ;MACjB+B,SAAS,EAAEzB,cAAe;MAC1B0B,WAAW,EAAEzB,IAAK;MAClBS,KAAK,EAAEA;IAAM;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAGFhC,WAAW,KAAK,UAAU,iBAC1BR,OAAA,CAACxB,YAAY;MAAC0B,QAAQ,EAAEA,QAAS;MAACuC,cAAc,EAAErC,OAAQ;MAACD,MAAM,EAAEA,MAAO;MACtEiB,KAAK,EAAEA;IAAM;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAGFhC,WAAW,KAAK,UAAU,iBAC1BR,OAAA,CAACvB,OAAO;MAACyB,QAAQ,EAAEA,QAAS;MAACuC,cAAc,EAAErC,OAAQ;MAACD,MAAM,EAAEA,MAAO;MACjEiB,KAAK,EAAEA;IAAM;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EAGFhC,WAAW,KAAK,SAAS,GAAGH,OAAO,GAAGI,QAAQ,gBAC1CT,OAAA,CAACT,iBAAiB;MACd6B,KAAK,EAAEA,KAAM;MACblB,QAAQ,EAAEA,QAAS;MAACE,OAAO,EAAEA,OAAQ;MAACsC,SAAS,EAAErC,OAAQ;MAACF,MAAM,EAAEA,MAAO;MAACM,QAAQ,EAAEA;IAAS;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBACpGxC,OAAA,CAACV,cAAc;MACX8B,KAAK,EAAEA,KAAM;MACblB,QAAQ,EAAEA,QAAS;MAACE,OAAO,EAAEA,OAAQ;MAACsC,SAAS,EAAErC,OAAQ;MAACF,MAAM,EAAEA;IAAO;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAChFxC,OAAA,CAACX,QAAQ;MAACa,QAAQ,EAAEA,QAAS;MACzBkB,KAAK,EAAEA,KAAM;MACbqB,cAAc,EAAErC,OAAQ;MAACD,MAAM,EAAEA;IAAO;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GAAG,EAAE,EAOxDhC,WAAW,KAAK,QAAQ,iBACxBR,OAAA,CAACtB,UAAU;MACP0C,KAAK,EAAEA,KAAM;MACblB,QAAQ,EAAEA,QAAS;MAACuC,cAAc,EAAErC,OAAQ;MAACD,MAAM,EAAEA;IAAO;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGnEhC,WAAW,KAAK,UAAU,IAAIA,WAAW,KAAK,SAAS,GAAGH,OAAO,gBAC7DL,OAAA,CAAChB,iBAAiB;MACdoC,KAAK,EAAEA,KAAM;MACblB,QAAQ,EAAEA,QAAS;MAACyC,SAAS,EAAEvC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwC,aAAc;MAACC,SAAS,EAAExC,OAAQ;MAACF,MAAM,EAAEA;IAAO;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBACjGxC,OAAA,CAACrB,WAAW;MACRyC,KAAK,EAAEA,KAAM;MACblB,QAAQ,EAAEA,QAAS;MAACuC,cAAc,EAAErC,OAAQ;MAACD,MAAM,EAAEA;IAAO;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GAAG,EAAE,EAG5EhC,WAAW,KAAK,SAAS,GAAGH,OAAO,gBAC/BL,OAAA,CAACf,gBAAgB;MACbmC,KAAK,EAAEA,KAAM;MACblB,QAAQ,EAAEA,QAAS;MAACyC,SAAS,EAAEvC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE0C,aAAc;MAACC,QAAQ,EAAE1C,OAAQ;MAACF,MAAM,EAAEA;IAAO;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAChGxC,OAAA,CAACpB,UAAU;MACPwC,KAAK,EAAEA,KAAM;MACblB,QAAQ,EAAEA,QAAS;MAACuC,cAAc,EAAErC,OAAQ;MAACD,MAAM,EAAEA;IAAO;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GAAG,EAAE,EAG5EhC,WAAW,KAAK,YAAY,GAAGH,OAAO,gBAClCL,OAAA,CAACd,kBAAkB;MACfkC,KAAK,EAAEA,KAAM;MACblB,QAAQ,EAAEA,QAAS;MAACyC,SAAS,EAAEvC,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE4C,gBAAiB;MAACC,MAAM,EAAE5C,OAAQ;MAACF,MAAM,EAAEA;IAAO;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBACjGxC,OAAA,CAACnB,QAAQ;MAACqB,QAAQ,EAAEA,QAAS;MAACuC,cAAc,EAAErC,OAAQ;MAACD,MAAM,EAAEA;IAAO;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GAAG,EAAE,EAKlFhC,WAAW,KAAK,QAAQ,iBACxBR,OAAA,CAAClB,MAAM;MACHsC,KAAK,EAAEA,KAAM;MACblB,QAAQ,EAAEA,QAAS;MAACuC,cAAc,EAAErC,OAAQ;MAACD,MAAM,EAAEA;IAAO;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGnEhC,WAAW,KAAK,QAAQ,iBACxBR,OAAA,CAACjB,MAAM;MACHqC,KAAK,EAAEA,KAAM;MACblB,QAAQ,EAAEA,QAAS;MAACuC,cAAc,EAAErC,OAAQ;MAACD,MAAM,EAAEA,MAAO;MAACG,WAAW,EAAEA;IAAY;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAG7FhC,WAAW,KAAK,cAAc,IAAIA,WAAW,KAAK,aAAa,iBAC/DR,OAAA,CAACb,YAAY;MACTiC,KAAK,EAAEA,KAAM;MACblB,QAAQ,EAAEA,QAAS;MAACuC,cAAc,EAAErC,OAAQ;MAACD,MAAM,EAAEA,MAAO;MAAC+C,WAAW,EAAE7C;IAAQ;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGzFhC,WAAW,KAAK,iBAAiB,iBACjCR,OAAA,CAACZ,cAAc;MACXgC,KAAK,EAAEA,KAAM;MACblB,QAAQ,EAAEA,QAAS;MAACuC,cAAc,EAAErC,OAAQ;MAACD,MAAM,EAAEA;IAAO;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAEtE,CAAC;AAEd,CAAC;AAAA3B,EAAA,CA3IKZ,SAAS;EAAA,QAEMR,WAAW,EACRC,WAAW;AAAA;AAAAyD,EAAA,GAH7BlD,SAAS;AA6If,eAAeA,SAAS;AAAA,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}