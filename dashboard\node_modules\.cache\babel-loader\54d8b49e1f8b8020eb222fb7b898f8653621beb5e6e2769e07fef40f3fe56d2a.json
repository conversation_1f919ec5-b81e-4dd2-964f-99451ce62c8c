{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\shade_cms\\\\dashboard\\\\src\\\\features\\\\Component\\\\Paginations.jsx\",\n  _s = $RefreshSig$();\nimport { FaAngleLeft, FaAngleRight } from \"react-icons/fa6\";\nimport { useContext } from \"react\";\nimport { ScrollContext } from \"../Context/Context\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Paginations = ({\n  currentPage,\n  totalPages,\n  setCurrentPage,\n  data\n}) => {\n  _s();\n  const scrollContainerRef = useContext(ScrollContext);\n  const scrollToTop = () => {\n    var _scrollContainerRef$c;\n    (_scrollContainerRef$c = scrollContainerRef.current) === null || _scrollContainerRef$c === void 0 ? void 0 : _scrollContainerRef$c.scrollTo({\n      top: 0,\n      behavior: \"smooth\"\n    });\n  };\n  const maxVisiblePages = 5;\n  let pages = [];\n  if (totalPages <= maxVisiblePages) {\n    pages = Array.from({\n      length: totalPages\n    }, (_, i) => i + 1);\n  } else if (currentPage <= maxVisiblePages) {\n    pages = [...Array(maxVisiblePages).keys()].map(i => i + 1);\n    pages.push(\"...\");\n  } else if (currentPage > totalPages - maxVisiblePages) {\n    pages.push(\"...\");\n    pages = pages.concat([...Array(maxVisiblePages).keys()].map(i => totalPages - maxVisiblePages + 1 + i));\n  } else {\n    pages = pages.concat([...Array(maxVisiblePages).keys()].map(i => currentPage - Math.floor(maxVisiblePages / 2) + i));\n    pages.push(\"...\");\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex justify-end items-center mt-6 gap-2 pr-2\",\n    style: {\n      // display: !(data?.length > 0) && \"none\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => {\n        setCurrentPage(prev => Math.max(prev - 1, 1));\n        scrollToTop();\n      },\n      disabled: currentPage === 1 || !totalPages,\n      className: `w-[2rem] p-2 flex items-center justify-center  text-sm rounded-full ${currentPage === 1 ? \"bg-[#ededed] cursor-not-allowed dark:text-[black]\" : \"bg-[#29469c] text-white\"}`,\n      children: /*#__PURE__*/_jsxDEV(FaAngleLeft, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 13\n    }, this), pages.map((page, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => {\n        if (page === \"...\") {\n          if (index === 0) {\n            setCurrentPage(1); // \"...\" at the beginning\n          } else if (index === pages.length - 1) {\n            setCurrentPage(totalPages); // \"...\" at the end\n          }\n        } else {\n          setCurrentPage(page);\n        }\n        scrollToTop();\n      },\n      className: `px-3 py-1 pt-2 ${page > 9 ? \"pl-2 pr-2\" : \"\"} rounded-full w-[2rem] h-[2rem] ${page === \"...\" ? \"hover:underline text-[23px] -translate-x-1 -translate-y-1\" : currentPage === page ? \"bg-[#29469c] text-white text-sm\" : \"bg-[#ededed] dark:text-[black] text-sm\"}`,\n      children: page\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 17\n    }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => {\n        setCurrentPage(prev => Math.min(prev + 1, totalPages));\n        scrollToTop();\n      },\n      disabled: currentPage === totalPages || !totalPages,\n      className: `w-[2rem] p-2 flex items-center justify-center text-sm rounded-full ${currentPage === totalPages || totalPages === 0 ? \"bg-[#ededed] cursor-not-allowed dark:text-[black]\" : \"bg-[#29469C] text-white\"}`,\n      children: /*#__PURE__*/_jsxDEV(FaAngleRight, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 9\n  }, this);\n};\n_s(Paginations, \"ZRglmJL1mvJtT/KFvxYnO6ZfpRg=\");\n_c = Paginations;\nexport default Paginations;\nvar _c;\n$RefreshReg$(_c, \"Paginations\");", "map": {"version": 3, "names": ["FaAngleLeft", "FaAngleRight", "useContext", "ScrollContext", "jsxDEV", "_jsxDEV", "Paginations", "currentPage", "totalPages", "setCurrentPage", "data", "_s", "scrollContainerRef", "scrollToTop", "_scrollContainerRef$c", "current", "scrollTo", "top", "behavior", "maxVisiblePages", "pages", "Array", "from", "length", "_", "i", "keys", "map", "push", "concat", "Math", "floor", "className", "style", "children", "onClick", "prev", "max", "disabled", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "page", "index", "min", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/shade_cms/dashboard/src/features/Component/Paginations.jsx"], "sourcesContent": ["import { FaAngleLeft, FaAngleRight } from \"react-icons/fa6\";\r\nimport { useContext } from \"react\";\r\nimport { ScrollContext } from \"../Context/Context\";\r\n\r\nconst Paginations = ({ currentPage, totalPages, setCurrentPage, data, }) => {\r\n    const scrollContainerRef = useContext(ScrollContext);\r\n\r\n    const scrollToTop = () => {\r\n        scrollContainerRef.current?.scrollTo({ top: 0, behavior: \"smooth\" });\r\n    };\r\n\r\n    const maxVisiblePages = 5;\r\n    let pages = [];\r\n\r\n    if (totalPages <= maxVisiblePages) {\r\n        pages = Array.from({ length: totalPages }, (_, i) => i + 1);\r\n    } else if (currentPage <= maxVisiblePages) {\r\n        pages = [...Array(maxVisiblePages).keys()].map(i => i + 1);\r\n        pages.push(\"...\");\r\n    } else if (currentPage > totalPages - maxVisiblePages) {\r\n        pages.push(\"...\");\r\n        pages = pages.concat([...Array(maxVisiblePages).keys()].map(i => totalPages - maxVisiblePages + 1 + i));\r\n    } else {\r\n        pages = pages.concat([...Array(maxVisiblePages).keys()].map(i => currentPage - Math.floor(maxVisiblePages / 2) + i));\r\n        pages.push(\"...\");\r\n    }\r\n\r\n    return (\r\n        <div className=\"flex justify-end items-center mt-6 gap-2 pr-2\" style={{\r\n            // display: !(data?.length > 0) && \"none\"\r\n        }}>\r\n            <button\r\n                onClick={() => {\r\n                    setCurrentPage(prev => Math.max(prev - 1, 1))\r\n                    scrollToTop()\r\n                }}\r\n                disabled={currentPage === 1 || !totalPages}\r\n                className={`w-[2rem] p-2 flex items-center justify-center  text-sm rounded-full ${currentPage === 1 ? \"bg-[#ededed] cursor-not-allowed dark:text-[black]\" : \"bg-[#29469c] text-white\"}`}\r\n            >\r\n                <FaAngleLeft />\r\n            </button>\r\n\r\n            {pages.map((page, index) => (\r\n                <button\r\n                    key={index}\r\n                    onClick={() => {\r\n                        if (page === \"...\") {\r\n                            if (index === 0) {\r\n                                setCurrentPage(1); // \"...\" at the beginning\r\n                            } else if (index === pages.length - 1) {\r\n                                setCurrentPage(totalPages); // \"...\" at the end\r\n                            }\r\n                        } else {\r\n                            setCurrentPage(page);\r\n                        }\r\n                        scrollToTop()\r\n                    }}\r\n                    className={`px-3 py-1 pt-2 ${page > 9 ? \"pl-2 pr-2\" : \"\"} rounded-full w-[2rem] h-[2rem] ${page === \"...\" ? \"hover:underline text-[23px] -translate-x-1 -translate-y-1\" : currentPage === page ? \"bg-[#29469c] text-white text-sm\" : \"bg-[#ededed] dark:text-[black] text-sm\"}`}\r\n                >\r\n                    {page}\r\n                </button>\r\n            ))}\r\n\r\n\r\n            <button\r\n                onClick={() => {\r\n                    setCurrentPage(prev => Math.min(prev + 1, totalPages))\r\n                    scrollToTop()\r\n                }}\r\n                disabled={currentPage === totalPages || !totalPages}\r\n                className={`w-[2rem] p-2 flex items-center justify-center text-sm rounded-full ${(currentPage === totalPages || totalPages === 0) ? \"bg-[#ededed] cursor-not-allowed dark:text-[black]\" : \"bg-[#29469C] text-white\"}`}\r\n            >\r\n                <FaAngleRight />\r\n            </button>\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default Paginations"], "mappings": ";;AAAA,SAASA,WAAW,EAAEC,YAAY,QAAQ,iBAAiB;AAC3D,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,aAAa,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,WAAW,GAAGA,CAAC;EAAEC,WAAW;EAAEC,UAAU;EAAEC,cAAc;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAMC,kBAAkB,GAAGV,UAAU,CAACC,aAAa,CAAC;EAEpD,MAAMU,WAAW,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IACtB,CAAAA,qBAAA,GAAAF,kBAAkB,CAACG,OAAO,cAAAD,qBAAA,uBAA1BA,qBAAA,CAA4BE,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACxE,CAAC;EAED,MAAMC,eAAe,GAAG,CAAC;EACzB,IAAIC,KAAK,GAAG,EAAE;EAEd,IAAIZ,UAAU,IAAIW,eAAe,EAAE;IAC/BC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAEf;IAAW,CAAC,EAAE,CAACgB,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC;EAC/D,CAAC,MAAM,IAAIlB,WAAW,IAAIY,eAAe,EAAE;IACvCC,KAAK,GAAG,CAAC,GAAGC,KAAK,CAACF,eAAe,CAAC,CAACO,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAACF,CAAC,IAAIA,CAAC,GAAG,CAAC,CAAC;IAC1DL,KAAK,CAACQ,IAAI,CAAC,KAAK,CAAC;EACrB,CAAC,MAAM,IAAIrB,WAAW,GAAGC,UAAU,GAAGW,eAAe,EAAE;IACnDC,KAAK,CAACQ,IAAI,CAAC,KAAK,CAAC;IACjBR,KAAK,GAAGA,KAAK,CAACS,MAAM,CAAC,CAAC,GAAGR,KAAK,CAACF,eAAe,CAAC,CAACO,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAACF,CAAC,IAAIjB,UAAU,GAAGW,eAAe,GAAG,CAAC,GAAGM,CAAC,CAAC,CAAC;EAC3G,CAAC,MAAM;IACHL,KAAK,GAAGA,KAAK,CAACS,MAAM,CAAC,CAAC,GAAGR,KAAK,CAACF,eAAe,CAAC,CAACO,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,CAACF,CAAC,IAAIlB,WAAW,GAAGuB,IAAI,CAACC,KAAK,CAACZ,eAAe,GAAG,CAAC,CAAC,GAAGM,CAAC,CAAC,CAAC;IACpHL,KAAK,CAACQ,IAAI,CAAC,KAAK,CAAC;EACrB;EAEA,oBACIvB,OAAA;IAAK2B,SAAS,EAAC,+CAA+C;IAACC,KAAK,EAAE;MAClE;IAAA,CACF;IAAAC,QAAA,gBACE7B,OAAA;MACI8B,OAAO,EAAEA,CAAA,KAAM;QACX1B,cAAc,CAAC2B,IAAI,IAAIN,IAAI,CAACO,GAAG,CAACD,IAAI,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAC7CvB,WAAW,CAAC,CAAC;MACjB,CAAE;MACFyB,QAAQ,EAAE/B,WAAW,KAAK,CAAC,IAAI,CAACC,UAAW;MAC3CwB,SAAS,EAAE,uEAAuEzB,WAAW,KAAK,CAAC,GAAG,mDAAmD,GAAG,yBAAyB,EAAG;MAAA2B,QAAA,eAExL7B,OAAA,CAACL,WAAW;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,EAERtB,KAAK,CAACO,GAAG,CAAC,CAACgB,IAAI,EAAEC,KAAK,kBACnBvC,OAAA;MAEI8B,OAAO,EAAEA,CAAA,KAAM;QACX,IAAIQ,IAAI,KAAK,KAAK,EAAE;UAChB,IAAIC,KAAK,KAAK,CAAC,EAAE;YACbnC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,MAAM,IAAImC,KAAK,KAAKxB,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;YACnCd,cAAc,CAACD,UAAU,CAAC,CAAC,CAAC;UAChC;QACJ,CAAC,MAAM;UACHC,cAAc,CAACkC,IAAI,CAAC;QACxB;QACA9B,WAAW,CAAC,CAAC;MACjB,CAAE;MACFmB,SAAS,EAAE,kBAAkBW,IAAI,GAAG,CAAC,GAAG,WAAW,GAAG,EAAE,mCAAmCA,IAAI,KAAK,KAAK,GAAG,2DAA2D,GAAGpC,WAAW,KAAKoC,IAAI,GAAG,iCAAiC,GAAG,wCAAwC,EAAG;MAAAT,QAAA,EAE/QS;IAAI,GAfAC,KAAK;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAgBN,CACX,CAAC,eAGFrC,OAAA;MACI8B,OAAO,EAAEA,CAAA,KAAM;QACX1B,cAAc,CAAC2B,IAAI,IAAIN,IAAI,CAACe,GAAG,CAACT,IAAI,GAAG,CAAC,EAAE5B,UAAU,CAAC,CAAC;QACtDK,WAAW,CAAC,CAAC;MACjB,CAAE;MACFyB,QAAQ,EAAE/B,WAAW,KAAKC,UAAU,IAAI,CAACA,UAAW;MACpDwB,SAAS,EAAE,sEAAuEzB,WAAW,KAAKC,UAAU,IAAIA,UAAU,KAAK,CAAC,GAAI,mDAAmD,GAAG,yBAAyB,EAAG;MAAA0B,QAAA,eAEtN7B,OAAA,CAACJ,YAAY;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACR,CAAC;AAEd,CAAC;AAAA/B,EAAA,CAxEKL,WAAW;AAAAwC,EAAA,GAAXxC,WAAW;AA0EjB,eAAeA,WAAW;AAAA,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}