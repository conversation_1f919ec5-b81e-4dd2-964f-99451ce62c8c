{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nexport const navBarSlice = createSlice({\n  name: 'navbar',\n  initialState: {\n    resourceType: \"\",\n    // current  title state management\n    resourceTag: \"\",\n    // right drawer state management for opening closing\n    name: \"Pages\"\n  },\n  reducers: {\n    updateType: (state, action) => {\n      state.resourceType = action.payload;\n    },\n    updateTag: (state, action) => {\n      state.resourceTag = action.payload;\n    },\n    updateName: (state, action) => {\n      state.name = action.payload;\n    }\n  }\n});\nexport const {\n  updateType,\n  updateTag,\n  updateName\n} = navBarSlice.actions;\nexport default navBarSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "navBarSlice", "name", "initialState", "resourceType", "resourceTag", "reducers", "updateType", "state", "action", "payload", "updateTag", "updateName", "actions", "reducer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/shade_cms/dashboard/src/features/common/navbarSlice.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit'\r\n\r\nexport const navBarSlice = createSlice({\r\n    name: 'navbar',\r\n    initialState: {\r\n        resourceType: \"\",  // current  title state management\r\n        resourceTag: \"\",   // right drawer state management for opening closing\r\n        name: \"Pages\"\r\n    },\r\n    reducers: {\r\n\r\n        updateType: (state, action) => {\r\n            state.resourceType = action.payload\r\n        },\r\n\r\n        updateTag: (state, action) => {\r\n            state.resourceTag = action.payload\r\n        },\r\n        updateName: (state, action) => {\r\n            state.name = action.payload\r\n        }\r\n    }\r\n})\r\n\r\nexport const { updateType, updateTag, updateName } = navBarSlice.actions\r\n\r\nexport default navBarSlice.reducer"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAE9C,OAAO,MAAMC,WAAW,GAAGD,WAAW,CAAC;EACnCE,IAAI,EAAE,QAAQ;EACdC,YAAY,EAAE;IACVC,YAAY,EAAE,EAAE;IAAG;IACnBC,WAAW,EAAE,EAAE;IAAI;IACnBH,IAAI,EAAE;EACV,CAAC;EACDI,QAAQ,EAAE;IAENC,UAAU,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAC3BD,KAAK,CAACJ,YAAY,GAAGK,MAAM,CAACC,OAAO;IACvC,CAAC;IAEDC,SAAS,EAAEA,CAACH,KAAK,EAAEC,MAAM,KAAK;MAC1BD,KAAK,CAACH,WAAW,GAAGI,MAAM,CAACC,OAAO;IACtC,CAAC;IACDE,UAAU,EAAEA,CAACJ,KAAK,EAAEC,MAAM,KAAK;MAC3BD,KAAK,CAACN,IAAI,GAAGO,MAAM,CAACC,OAAO;IAC/B;EACJ;AACJ,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEH,UAAU;EAAEI,SAAS;EAAEC;AAAW,CAAC,GAAGX,WAAW,CAACY,OAAO;AAExE,eAAeZ,WAAW,CAACa,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}