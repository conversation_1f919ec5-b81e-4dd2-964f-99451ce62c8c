{"ast": null, "code": "export const Img_url = \"https://res.cloudinary.com/dmcxybhjm/image/upload/v1745838647/\";\nconst BASE_URL = \"http://localhost:3000/\";\nconst auth = \"auth\";\nconst role = \"role\";\nconst permission = \"permission\";\nconst user = \"user\";\nconst notification = \"notification\";\nconst content = \"content\";\nconst media = \"media\";\nconst api = {\n  login: `${auth}/login`,\n  // API for Auth\n  signup: `${auth}/signup`,\n  mfa_login: `${auth}/mfa/login`,\n  mfa_verify: `${auth}/mfa/verify`,\n  forgotPassword: `${auth}/forgotPassword`,\n  forgotPassword_verify: `${auth}/forgotPassword/verify`,\n  forgotPassword_update: `${auth}/forgotPassword/updatePassword`,\n  resetPassword: `${auth}/resetPass`,\n  refreshToken: `${auth}/refreshToken`,\n  resendOTP: `${auth}/resendOtp`,\n  userLogs: `${auth}/logs`,\n  // API for logs\n\n  fetchRoles: `${role}/roles`,\n  // API for role start from here\n  getRoleById: `${role}/`,\n  fetchRoleType: `${role}/roleType`,\n  createRole: `${role}/create`,\n  activateRole: `${role}/activate`,\n  deactivateRole: `${role}/deactivate`,\n  updateRole: `${role}/update`,\n  fetchPermissionsByRoleType: `${permission}/permissionsByRoleType/`,\n  //permisions\n\n  getUsers: `${user}/getAllUsers`,\n  // API for users from here\n  createUser: `${user}/create`,\n  getUserById: `${user}/`,\n  updateUser: `${user}/updateUser/`,\n  activateUser: `${user}/activate`,\n  deactivateUser: `${user}/deactivate`,\n  getNotifications: `${notification}/`,\n  // API for notifications\n  markAllNotificationAsRead: `${notification}/read-all/`,\n  // API for marking notification as read\n\n  // Pages\n  getResources: `${content}/getResources`,\n  // Resources\n  getResourceInfo: `${content}/getResourceInfo/`,\n  getEligibleUsers: `${content}/getEligibleUsers`,\n  assignUser: `${content}/assignUser`,\n  removeAssignedUsers: `${content}/removeAssignedUser/`,\n  getAssignedUsers: `${content}/getAssignedUsers`,\n  getContent: `${content}/getContent`,\n  // Content of Resources\n  updateContent: `${content}/updateContent`,\n  publishContent: `${content}/directPublishContent`,\n  getRequests: `${content}/getRequests`,\n  // requests query\n  generateRequest: `${content}/generateRequest`,\n  requestInfo: `${content}/getRequestInfo/`,\n  approveRequest: `${content}/approveRequest/`,\n  rejectRequest: `${content}/rejectRequest/`,\n  versionsList: `${content}/getVersionsList/`,\n  // versions query\n  versionInfo: `${content}/getVersionInfo/`,\n  restoreVersion: `${content}/restoreVersion/`,\n  // Media\n  uploadMedia: `${media}/upload`,\n  deleteMedia: `${media}/delete`,\n  getMedia: `${media}/getMedia`,\n  route(route) {\n    if (this[route]) {\n      return BASE_URL + this[route];\n    } else {\n      throw new Error(`Route ${route} not found`);\n    }\n  }\n};\nexport default api;", "map": {"version": 3, "names": ["Img_url", "BASE_URL", "auth", "role", "permission", "user", "notification", "content", "media", "api", "login", "signup", "mfa_login", "mfa_verify", "forgotPassword", "forgotPassword_verify", "forgotPassword_update", "resetPassword", "refreshToken", "resendOTP", "userLogs", "fetchRoles", "getRoleById", "fetchRoleType", "createRole", "activateRole", "deactivateRole", "updateRole", "fetchPermissionsByRoleType", "getUsers", "createUser", "getUserById", "updateUser", "activateUser", "deactivateUser", "getNotifications", "markAllNotificationAsRead", "getResources", "getResourceInfo", "getEligibleUsers", "assignUser", "removeAssignedUsers", "getAssignedUsers", "get<PERSON>ontent", "updateContent", "publishContent", "getRequests", "generateRequest", "requestInfo", "approveRequest", "rejectRequest", "versionsList", "versionInfo", "restoreVersion", "uploadMedia", "deleteMedia", "getMedia", "route", "Error"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/shade_cms/dashboard/src/routes/backend.js"], "sourcesContent": ["export const Img_url = \"https://res.cloudinary.com/dmcxybhjm/image/upload/v1745838647/\"\r\nconst BASE_URL = \"http://localhost:3000/\";\r\n\r\nconst auth = \"auth\";\r\nconst role = \"role\";\r\nconst permission = \"permission\";\r\nconst user = \"user\";\r\nconst notification = \"notification\";\r\nconst content = \"content\";\r\nconst media = \"media\"\r\n\r\n\r\nconst api = {\r\n  login: `${auth}/login`, // API for Auth\r\n  signup: `${auth}/signup`,\r\n  mfa_login: `${auth}/mfa/login`,\r\n  mfa_verify: `${auth}/mfa/verify`,\r\n  forgotPassword: `${auth}/forgotPassword`,\r\n  forgotPassword_verify: `${auth}/forgotPassword/verify`,\r\n  forgotPassword_update: `${auth}/forgotPassword/updatePassword`,\r\n  resetPassword: `${auth}/resetPass`,\r\n  refreshToken: `${auth}/refreshToken`,\r\n  resendOTP: `${auth}/resendOtp`,\r\n\r\n  userLogs: `${auth}/logs`, // API for logs\r\n\r\n  fetchRoles: `${role}/roles`, // API for role start from here\r\n  getRoleById: `${role}/`,\r\n  fetchRoleType: `${role}/roleType`,\r\n  createRole: `${role}/create`,\r\n  activateRole: `${role}/activate`,\r\n  deactivateRole: `${role}/deactivate`,\r\n  updateRole: `${role}/update`,\r\n\r\n  fetchPermissionsByRoleType: `${permission}/permissionsByRoleType/`, //permisions\r\n\r\n  getUsers: `${user}/getAllUsers`, // API for users from here\r\n  createUser: `${user}/create`,\r\n  getUserById: `${user}/`,\r\n  updateUser: `${user}/updateUser/`,\r\n  activateUser: `${user}/activate`,\r\n  deactivateUser: `${user}/deactivate`,\r\n\r\n  getNotifications: `${notification}/`, // API for notifications\r\n  markAllNotificationAsRead: `${notification}/read-all/`, // API for marking notification as read\r\n\r\n  // Pages\r\n  getResources: `${content}/getResources`, // Resources\r\n  getResourceInfo: `${content}/getResourceInfo/`,\r\n  getEligibleUsers: `${content}/getEligibleUsers`,\r\n  assignUser: `${content}/assignUser`,\r\n  removeAssignedUsers: `${content}/removeAssignedUser/`,\r\n  getAssignedUsers: `${content}/getAssignedUsers`,\r\n\r\n  getContent: `${content}/getContent`, // Content of Resources\r\n  updateContent: `${content}/updateContent`,\r\n  publishContent: `${content}/directPublishContent`,\r\n\r\n  getRequests: `${content}/getRequests`, // requests query\r\n  generateRequest: `${content}/generateRequest`,\r\n  requestInfo: `${content}/getRequestInfo/`,\r\n  approveRequest: `${content}/approveRequest/`,\r\n  rejectRequest: `${content}/rejectRequest/`,\r\n\r\n  versionsList: `${content}/getVersionsList/`, // versions query\r\n  versionInfo: `${content}/getVersionInfo/`,\r\n  restoreVersion: `${content}/restoreVersion/`,\r\n\r\n  // Media\r\n  uploadMedia: `${media}/upload`,\r\n  deleteMedia: `${media}/delete`,\r\n  getMedia: `${media}/getMedia`,\r\n\r\n  route(route) {\r\n    if (this[route]) {\r\n      return BASE_URL + this[route];\r\n    } else {\r\n      throw new Error(`Route ${route} not found`);\r\n    }\r\n  },\r\n};\r\n\r\nexport default api;\r\n"], "mappings": "AAAA,OAAO,MAAMA,OAAO,GAAG,gEAAgE;AACvF,MAAMC,QAAQ,GAAG,wBAAwB;AAEzC,MAAMC,IAAI,GAAG,MAAM;AACnB,MAAMC,IAAI,GAAG,MAAM;AACnB,MAAMC,UAAU,GAAG,YAAY;AAC/B,MAAMC,IAAI,GAAG,MAAM;AACnB,MAAMC,YAAY,GAAG,cAAc;AACnC,MAAMC,OAAO,GAAG,SAAS;AACzB,MAAMC,KAAK,GAAG,OAAO;AAGrB,MAAMC,GAAG,GAAG;EACVC,KAAK,EAAE,GAAGR,IAAI,QAAQ;EAAE;EACxBS,MAAM,EAAE,GAAGT,IAAI,SAAS;EACxBU,SAAS,EAAE,GAAGV,IAAI,YAAY;EAC9BW,UAAU,EAAE,GAAGX,IAAI,aAAa;EAChCY,cAAc,EAAE,GAAGZ,IAAI,iBAAiB;EACxCa,qBAAqB,EAAE,GAAGb,IAAI,wBAAwB;EACtDc,qBAAqB,EAAE,GAAGd,IAAI,gCAAgC;EAC9De,aAAa,EAAE,GAAGf,IAAI,YAAY;EAClCgB,YAAY,EAAE,GAAGhB,IAAI,eAAe;EACpCiB,SAAS,EAAE,GAAGjB,IAAI,YAAY;EAE9BkB,QAAQ,EAAE,GAAGlB,IAAI,OAAO;EAAE;;EAE1BmB,UAAU,EAAE,GAAGlB,IAAI,QAAQ;EAAE;EAC7BmB,WAAW,EAAE,GAAGnB,IAAI,GAAG;EACvBoB,aAAa,EAAE,GAAGpB,IAAI,WAAW;EACjCqB,UAAU,EAAE,GAAGrB,IAAI,SAAS;EAC5BsB,YAAY,EAAE,GAAGtB,IAAI,WAAW;EAChCuB,cAAc,EAAE,GAAGvB,IAAI,aAAa;EACpCwB,UAAU,EAAE,GAAGxB,IAAI,SAAS;EAE5ByB,0BAA0B,EAAE,GAAGxB,UAAU,yBAAyB;EAAE;;EAEpEyB,QAAQ,EAAE,GAAGxB,IAAI,cAAc;EAAE;EACjCyB,UAAU,EAAE,GAAGzB,IAAI,SAAS;EAC5B0B,WAAW,EAAE,GAAG1B,IAAI,GAAG;EACvB2B,UAAU,EAAE,GAAG3B,IAAI,cAAc;EACjC4B,YAAY,EAAE,GAAG5B,IAAI,WAAW;EAChC6B,cAAc,EAAE,GAAG7B,IAAI,aAAa;EAEpC8B,gBAAgB,EAAE,GAAG7B,YAAY,GAAG;EAAE;EACtC8B,yBAAyB,EAAE,GAAG9B,YAAY,YAAY;EAAE;;EAExD;EACA+B,YAAY,EAAE,GAAG9B,OAAO,eAAe;EAAE;EACzC+B,eAAe,EAAE,GAAG/B,OAAO,mBAAmB;EAC9CgC,gBAAgB,EAAE,GAAGhC,OAAO,mBAAmB;EAC/CiC,UAAU,EAAE,GAAGjC,OAAO,aAAa;EACnCkC,mBAAmB,EAAE,GAAGlC,OAAO,sBAAsB;EACrDmC,gBAAgB,EAAE,GAAGnC,OAAO,mBAAmB;EAE/CoC,UAAU,EAAE,GAAGpC,OAAO,aAAa;EAAE;EACrCqC,aAAa,EAAE,GAAGrC,OAAO,gBAAgB;EACzCsC,cAAc,EAAE,GAAGtC,OAAO,uBAAuB;EAEjDuC,WAAW,EAAE,GAAGvC,OAAO,cAAc;EAAE;EACvCwC,eAAe,EAAE,GAAGxC,OAAO,kBAAkB;EAC7CyC,WAAW,EAAE,GAAGzC,OAAO,kBAAkB;EACzC0C,cAAc,EAAE,GAAG1C,OAAO,kBAAkB;EAC5C2C,aAAa,EAAE,GAAG3C,OAAO,iBAAiB;EAE1C4C,YAAY,EAAE,GAAG5C,OAAO,mBAAmB;EAAE;EAC7C6C,WAAW,EAAE,GAAG7C,OAAO,kBAAkB;EACzC8C,cAAc,EAAE,GAAG9C,OAAO,kBAAkB;EAE5C;EACA+C,WAAW,EAAE,GAAG9C,KAAK,SAAS;EAC9B+C,WAAW,EAAE,GAAG/C,KAAK,SAAS;EAC9BgD,QAAQ,EAAE,GAAGhD,KAAK,WAAW;EAE7BiD,KAAKA,CAACA,KAAK,EAAE;IACX,IAAI,IAAI,CAACA,KAAK,CAAC,EAAE;MACf,OAAOxD,QAAQ,GAAG,IAAI,CAACwD,KAAK,CAAC;IAC/B,CAAC,MAAM;MACL,MAAM,IAAIC,KAAK,CAAC,SAASD,KAAK,YAAY,CAAC;IAC7C;EACF;AACF,CAAC;AAED,eAAehD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}