// Enum for defining the status of a page
enum PageStatus {
  PUBLISHED // ACTIVE
  ARCHIVED // INACTIVE
  TRASHED // REMOVED
}

// Enum for defining the general status of an entity
enum Status {
  ACTIVE
  INACTIVE
  TRASHED
}

// Enum for defining the origin of OTP
enum otpOrigin {
  M<PERSON>_Login
  forgot_Pass
  NULL
}

// Enum for defining the log outcome
enum logOutcome {
  Success
  Failure
  Unknown
}

// Enum for defining the action performed by user
enum logAction {
  CREATE
  UPDATE
  DELETE
  LOGIN
  LOGOUT
  NULL
}

// RESOURCE ENUM==================
// Enum for defining type of the resource
enum ResourceType {
  MAIN_PAGE
  SUB_PAGE
  SUB_PAGE_ITEM
  FORM
  HEADER
  FOOTER
  NULL
}

// Enum for defining page type
enum ResourceTag {
  HOME
  ABOUT
  SOLUTION
  SERVICE
  MARKET
  PROJECT
  CAREER
  NEWS
  TESTIMONIAL
  CONTACT
  HEADER
  FOOTER
  SAFETY_RESPONSIBILITY
  NULL
}

// Enum for defining the relation type
enum RelationType {
  PARENT
  CHILD
  NULL
}

// RESOURCE VERSION ENUM==================
// Enum for defining the status of the version after assignment
enum VersionStatus {
  EDITING
  DRAFT
  VERIFICATION_PENDING
  PUBLISH_PENDING
  ARCHIVED
  SCHEDULED
  PUBLISHED
  LIVE
  NULL
}

// SECTION ENUM==================

enum ResourceRoleType {
  MANAGER
  EDITOR
  PUBLISHER
}

enum RequestStatus {
  VERIFICATION_PENDING
  PUBLISH_PENDING
  PUBLISHED
  SCHEDULED
}

enum FlowStatus {
  PENDING
  APPROVED
  REJECTED
  SCHEDULED
  PUBLISHED
}

enum RequestType {
  VERIFICATION
  PUBLICATION
}

enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
}

enum MediaType {
  IMAGE
  VIDEO
  DOCUMENT
  AUDIO
}
