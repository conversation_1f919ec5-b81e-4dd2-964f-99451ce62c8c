{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\shade_cms\\\\dashboard\\\\src\\\\containers\\\\PageContent.jsx\",\n  _s = $RefreshSig$();\nimport Header from \"./Header\";\nimport { Route, Routes, useLocation } from 'react-router-dom';\nimport routes from '../routes';\nimport { Suspense, lazy } from 'react';\nimport SuspenseContent from \"./SuspenseContent\";\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useEffect, useRef } from \"react\";\nimport { useNavigate } from \"react-router-dom\";\nimport { useContext } from \"react\";\nimport { ScrollContext } from \"../features/Context/Context\";\n// import { toast } from \"react-toastify\"\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Page404 = /*#__PURE__*/lazy(_c = () => import('../pages/protected/404'));\n_c2 = Page404;\nfunction PageContent() {\n  _s();\n  const navigate = useNavigate();\n  // const mainContentRef = useRef(null);\n  const {\n    pageTitle\n  } = useSelector(state => state.header);\n  const userRole = useSelector(state => state.user.activeRole);\n  const scrollContainerRef = useContext(ScrollContext);\n\n  // const applied = useSelector(state => state)\n  const location = useLocation();\n  useEffect(() => {\n    scrollContainerRef.current.scroll({\n      top: 0,\n      behavior: \"smooth\"\n    });\n  }, [pageTitle]);\n  useEffect(() => {\n    let route = localStorage.getItem(\"route\");\n    if (route && location.pathname.split('/').length <= 4) {\n      navigate(route);\n    }\n\n    // dispatch()\n  }, []);\n  localStorage.setItem(\"route\", location.pathname);\n  // useEffect(() => {\n  // }, [])\n  // useEffect(() => {\n  //     if (userRole.role && userRole.permissions?.length === 0) {\n  //         toast.error(\"The current role has no permissios\", { hideProgressBar: true })\n  //     }\n\n  // }, [userRole])\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col flex-8 w-full overflow-x-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"flex-1 overflow-y-scroll pt-0 px-3 pb-8 bg-base-100 customscroller\",\n      ref: scrollContainerRef,\n      children: [/*#__PURE__*/_jsxDEV(Suspense, {\n        fallback: /*#__PURE__*/_jsxDEV(SuspenseContent, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 37\n        }, this),\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [routes.map((route, key) => {\n            let path = route.path;\n            if (route !== null && route !== void 0 && route.permission) {\n              var _userRole$permissions;\n              if (!(userRole !== null && userRole !== void 0 && (_userRole$permissions = userRole.permissions) !== null && _userRole$permissions !== void 0 && _userRole$permissions.includes(route.permission))) {\n                return null;\n              }\n            }\n            return /*#__PURE__*/_jsxDEV(Route, {\n              exact: true,\n              path: route.path === \"/resources\" ? `${route.path}/*` : path,\n              element: /*#__PURE__*/_jsxDEV(route.component, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 50\n              }, this)\n            }, key, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 37\n            }, this);\n          }), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"*\",\n            element: /*#__PURE__*/_jsxDEV(Page404, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 9\n  }, this);\n}\n_s(PageContent, \"xI/4VGEBO4I0cZ+2X1vpBSIie3g=\", false, function () {\n  return [useNavigate, useSelector, useSelector, useLocation];\n});\n_c3 = PageContent;\nexport default PageContent;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"Page404$lazy\");\n$RefreshReg$(_c2, \"Page404\");\n$RefreshReg$(_c3, \"PageContent\");", "map": {"version": 3, "names": ["Header", "Route", "Routes", "useLocation", "routes", "Suspense", "lazy", "SuspenseContent", "useDispatch", "useSelector", "useEffect", "useRef", "useNavigate", "useContext", "ScrollContext", "jsxDEV", "_jsxDEV", "Page404", "_c", "_c2", "<PERSON><PERSON><PERSON><PERSON>", "_s", "navigate", "pageTitle", "state", "header", "userRole", "user", "activeRole", "scrollContainerRef", "location", "current", "scroll", "top", "behavior", "route", "localStorage", "getItem", "pathname", "split", "length", "setItem", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "fallback", "map", "key", "path", "permission", "_userRole$permissions", "permissions", "includes", "exact", "element", "component", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/shade_cms/dashboard/src/containers/PageContent.jsx"], "sourcesContent": ["import Header from \"./Header\"\r\nimport { Route, Routes, useLocation } from 'react-router-dom'\r\nimport routes from '../routes'\r\nimport { Suspense, lazy } from 'react'\r\nimport SuspenseContent from \"./SuspenseContent\"\r\nimport { useDispatch, useSelector } from 'react-redux'\r\nimport { useEffect, useRef } from \"react\"\r\nimport { useNavigate } from \"react-router-dom\"\r\nimport { useContext } from \"react\";\r\nimport { ScrollContext } from \"../features/Context/Context\"\r\n// import { toast } from \"react-toastify\"\r\n\r\nconst Page404 = lazy(() => import('../pages/protected/404'))\r\n\r\n\r\nfunction PageContent() {\r\n    const navigate = useNavigate()\r\n    // const mainContentRef = useRef(null);\r\n    const { pageTitle } = useSelector(state => state.header)\r\n    const userRole = useSelector(state => state.user.activeRole)\r\n    const scrollContainerRef = useContext(ScrollContext);\r\n\r\n    // const applied = useSelector(state => state)\r\n    const location = useLocation()\r\n\r\n\r\n    useEffect(() => {\r\n        scrollContainerRef.current.scroll({\r\n            top: 0,\r\n            behavior: \"smooth\"\r\n        });\r\n    }, [pageTitle])\r\n\r\n    useEffect(() => {\r\n        let route = localStorage.getItem(\"route\")\r\n        if (route && location.pathname.split('/').length <= 4) {\r\n            navigate(route)\r\n        }\r\n\r\n        // dispatch()\r\n    }, [])\r\n\r\n    localStorage.setItem(\"route\", location.pathname)\r\n    // useEffect(() => {\r\n    // }, [])\r\n    // useEffect(() => {\r\n    //     if (userRole.role && userRole.permissions?.length === 0) {\r\n    //         toast.error(\"The current role has no permissios\", { hideProgressBar: true })\r\n    //     }\r\n\r\n    // }, [userRole])\r\n\r\n    return (\r\n        <div className=\"flex flex-col flex-8 w-full overflow-x-hidden\">\r\n            <Header />\r\n            <main className=\"flex-1 overflow-y-scroll pt-0 px-3 pb-8 bg-base-100 customscroller\" ref={scrollContainerRef}>\r\n                <Suspense fallback={<SuspenseContent />}>\r\n                    <Routes>\r\n                        {\r\n                            routes.map((route, key) => {\r\n                                let path = route.path\r\n                                if (route?.permission) {\r\n                                    if (!userRole?.permissions?.includes(route.permission)) {\r\n                                        return null\r\n                                    }\r\n                                }\r\n                                return (\r\n                                    <Route\r\n                                        key={key}\r\n                                        exact={true}\r\n                                        path={route.path === \"/resources\" ? `${route.path}/*` : path}\r\n                                        element={<route.component />}\r\n                                    />\r\n                                )\r\n                            })\r\n                        }\r\n\r\n                        {/* Redirecting unknown url to 404 page */}\r\n                        <Route path=\"*\" element={<Page404 />} />\r\n                    </Routes>\r\n                </Suspense>\r\n                <div className=\"\"></div>\r\n            </main>\r\n        </div>\r\n    )\r\n}\r\n\r\n\r\nexport default PageContent\r\n"], "mappings": ";;AAAA,OAAOA,MAAM,MAAM,UAAU;AAC7B,SAASC,KAAK,EAAEC,MAAM,EAAEC,WAAW,QAAQ,kBAAkB;AAC7D,OAAOC,MAAM,MAAM,WAAW;AAC9B,SAASC,QAAQ,EAAEC,IAAI,QAAQ,OAAO;AACtC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,aAAa,QAAQ,6BAA6B;AAC3D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAMC,OAAO,gBAAGX,IAAI,CAAAY,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,wBAAwB,CAAC,CAAC;AAAAC,GAAA,GAAtDF,OAAO;AAGb,SAASG,WAAWA,CAAA,EAAG;EAAAC,EAAA;EACnB,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B;EACA,MAAM;IAAEW;EAAU,CAAC,GAAGd,WAAW,CAACe,KAAK,IAAIA,KAAK,CAACC,MAAM,CAAC;EACxD,MAAMC,QAAQ,GAAGjB,WAAW,CAACe,KAAK,IAAIA,KAAK,CAACG,IAAI,CAACC,UAAU,CAAC;EAC5D,MAAMC,kBAAkB,GAAGhB,UAAU,CAACC,aAAa,CAAC;;EAEpD;EACA,MAAMgB,QAAQ,GAAG3B,WAAW,CAAC,CAAC;EAG9BO,SAAS,CAAC,MAAM;IACZmB,kBAAkB,CAACE,OAAO,CAACC,MAAM,CAAC;MAC9BC,GAAG,EAAE,CAAC;MACNC,QAAQ,EAAE;IACd,CAAC,CAAC;EACN,CAAC,EAAE,CAACX,SAAS,CAAC,CAAC;EAEfb,SAAS,CAAC,MAAM;IACZ,IAAIyB,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IACzC,IAAIF,KAAK,IAAIL,QAAQ,CAACQ,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,IAAI,CAAC,EAAE;MACnDlB,QAAQ,CAACa,KAAK,CAAC;IACnB;;IAEA;EACJ,CAAC,EAAE,EAAE,CAAC;EAENC,YAAY,CAACK,OAAO,CAAC,OAAO,EAAEX,QAAQ,CAACQ,QAAQ,CAAC;EAChD;EACA;EACA;EACA;EACA;EACA;;EAEA;;EAEA,oBACItB,OAAA;IAAK0B,SAAS,EAAC,+CAA+C;IAAAC,QAAA,gBAC1D3B,OAAA,CAAChB,MAAM;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACV/B,OAAA;MAAM0B,SAAS,EAAC,oEAAoE;MAACM,GAAG,EAAEnB,kBAAmB;MAAAc,QAAA,gBACzG3B,OAAA,CAACX,QAAQ;QAAC4C,QAAQ,eAAEjC,OAAA,CAACT,eAAe;UAAAqC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,eACpC3B,OAAA,CAACd,MAAM;UAAAyC,QAAA,GAECvC,MAAM,CAAC8C,GAAG,CAAC,CAACf,KAAK,EAAEgB,GAAG,KAAK;YACvB,IAAIC,IAAI,GAAGjB,KAAK,CAACiB,IAAI;YACrB,IAAIjB,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEkB,UAAU,EAAE;cAAA,IAAAC,qBAAA;cACnB,IAAI,EAAC5B,QAAQ,aAARA,QAAQ,gBAAA4B,qBAAA,GAAR5B,QAAQ,CAAE6B,WAAW,cAAAD,qBAAA,eAArBA,qBAAA,CAAuBE,QAAQ,CAACrB,KAAK,CAACkB,UAAU,CAAC,GAAE;gBACpD,OAAO,IAAI;cACf;YACJ;YACA,oBACIrC,OAAA,CAACf,KAAK;cAEFwD,KAAK,EAAE,IAAK;cACZL,IAAI,EAAEjB,KAAK,CAACiB,IAAI,KAAK,YAAY,GAAG,GAAGjB,KAAK,CAACiB,IAAI,IAAI,GAAGA,IAAK;cAC7DM,OAAO,eAAE1C,OAAA,CAACmB,KAAK,CAACwB,SAAS;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE,GAHxBI,GAAG;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIX,CAAC;UAEV,CAAC,CAAC,eAIN/B,OAAA,CAACf,KAAK;YAACmD,IAAI,EAAC,GAAG;YAACM,OAAO,eAAE1C,OAAA,CAACC,OAAO;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACX/B,OAAA;QAAK0B,SAAS,EAAC;MAAE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEd;AAAC1B,EAAA,CAtEQD,WAAW;EAAA,QACCR,WAAW,EAENH,WAAW,EAChBA,WAAW,EAIXN,WAAW;AAAA;AAAAyD,GAAA,GARvBxC,WAAW;AAyEpB,eAAeA,WAAW;AAAA,IAAAF,EAAA,EAAAC,GAAA,EAAAyC,GAAA;AAAAC,YAAA,CAAA3C,EAAA;AAAA2C,YAAA,CAAA1C,GAAA;AAAA0C,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}