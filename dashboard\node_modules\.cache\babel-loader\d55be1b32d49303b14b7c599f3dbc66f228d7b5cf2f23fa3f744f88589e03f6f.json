{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nconst fontFamily = createSlice({\n  name: \"fontStyle\",\n  initialState: {\n    regular: \"bankgothic-medium-dt\",\n    light: \"bank-light\"\n  },\n  reducers: {\n    changefont: (state, action) => {\n      state[action.payload.regularKey] = action.payload.regular;\n      state[action.payload.lightKey] = action.payload.light;\n    }\n  }\n});\nexport const {\n  changefont\n} = fontFamily.actions;\nexport default fontFamily.reducer;", "map": {"version": 3, "names": ["createSlice", "fontFamily", "name", "initialState", "regular", "light", "reducers", "changefont", "state", "action", "payload", "<PERSON><PERSON><PERSON>", "light<PERSON>ey", "actions", "reducer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/shade_cms/dashboard/src/features/common/fontStyle.js"], "sourcesContent": ["import { createSlice } from '@reduxjs/toolkit'\r\n\r\nconst fontFamily = createSlice({\r\n    name: \"fontStyle\",\r\n    initialState: {\r\n        regular: \"bankgothic-medium-dt\",\r\n        light: \"bank-light\"\r\n    },\r\n    reducers: {\r\n        changefont: (state, action) => {\r\n            state[action.payload.regularKey] = action.payload.regular\r\n            state[action.payload.lightKey] = action.payload.light\r\n        }\r\n    }\r\n})\r\n\r\nexport const { changefont } = fontFamily.actions;\r\n\r\nexport default fontFamily.reducer;"], "mappings": "AAAA,SAASA,WAAW,QAAQ,kBAAkB;AAE9C,MAAMC,UAAU,GAAGD,WAAW,CAAC;EAC3BE,IAAI,EAAE,WAAW;EACjBC,YAAY,EAAE;IACVC,OAAO,EAAE,sBAAsB;IAC/BC,KAAK,EAAE;EACX,CAAC;EACDC,QAAQ,EAAE;IACNC,UAAU,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MAC3BD,KAAK,CAACC,MAAM,CAACC,OAAO,CAACC,UAAU,CAAC,GAAGF,MAAM,CAACC,OAAO,CAACN,OAAO;MACzDI,KAAK,CAACC,MAAM,CAACC,OAAO,CAACE,QAAQ,CAAC,GAAGH,MAAM,CAACC,OAAO,CAACL,KAAK;IACzD;EACJ;AACJ,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEE;AAAW,CAAC,GAAGN,UAAU,CAACY,OAAO;AAEhD,eAAeZ,UAAU,CAACa,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}