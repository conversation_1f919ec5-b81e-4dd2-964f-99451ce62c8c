{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\shade_cms\\\\dashboard\\\\src\\\\features\\\\logstable\\\\index.jsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n// libraries import\nimport { useEffect, useState } from \"react\";\n// self modules\nimport { userLogs } from \"../../app/fetch\";\nimport SearchBar from \"../../components/Input/SearchBar\";\nimport TitleCard from \"../../components/Cards/TitleCard\";\nimport ShowLogs from \"./ShowLog\";\nimport capitalizeWords from \"../../app/capitalizeword\";\nimport Paginations from \"../Component/Paginations\";\nimport formatTimestamp from \"../../app/TimeFormat\";\n// icons\nimport XMarkIcon from \"@heroicons/react/24/outline/XMarkIcon\";\nimport { FiEye } from \"react-icons/fi\";\nimport { LuListFilter } from \"react-icons/lu\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TopSideButtons = ({\n  removeFilter,\n  applyFilter,\n  applySearch\n  // openAddForm,\n}) => {\n  _s();\n  const [filterParam, setFilterParam] = useState(\"\");\n  const [searchText, setSearchText] = useState(\"\");\n  const statusFilters = [\"Success\", \"Failure\"];\n  const showFiltersAndApply = status => {\n    applyFilter(status);\n    setFilterParam(status);\n  };\n  const removeAppliedFilter = () => {\n    removeFilter();\n    setFilterParam(\"\");\n    setSearchText(\"\");\n  };\n  useEffect(() => {\n    if (searchText === \"\") {\n      removeAppliedFilter();\n    } else {\n      applySearch(searchText);\n    }\n  }, [searchText]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"inline-block float-right w-full flex items-center gap-3 border dark:border-neutral-600 rounded-lg p-1\",\n    children: [/*#__PURE__*/_jsxDEV(SearchBar, {\n      searchText: searchText,\n      styleClass: \"w-700px border-none w-full flex-1\",\n      setSearchText: setSearchText,\n      placeholderText: \"Search Logs by name and roles keywords\",\n      outline: false\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this), filterParam && /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: () => removeAppliedFilter(),\n      className: \"btn btn-xs mr-2 btn-active btn-ghost normal-case\",\n      children: [filterParam, /*#__PURE__*/_jsxDEV(XMarkIcon, {\n        className: \"w-4 ml-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"dropdown dropdown-bottom dropdown-end\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        tabIndex: 0,\n        className: \"capitalize border text-[14px] self-center border-stone-300 dark:border-neutral-500 rounded-lg h-[40px] w-[91px] flex items-center gap-1 font-[300] px-[14px] py-[10px]\",\n        children: [/*#__PURE__*/_jsxDEV(LuListFilter, {\n          className: \"w-5 \"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), \"Filter\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        tabIndex: 0,\n        className: \"dropdown-content menu p-2 text-sm shadow bg-base-100 rounded-box w-52 text-[#0E2354] font-[400]\",\n        children: [statusFilters.map((status, key) => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"dark:text-gray-300\",\n            onClick: () => showFiltersAndApply(status),\n            style: {\n              textTransform: \"capitalize\"\n            },\n            children: capitalizeWords(status)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 15\n          }, this)\n        }, key, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 13\n        }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"divider mt-0 mb-0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"dark:text-gray-300\",\n            onClick: () => removeAppliedFilter(),\n            children: \"Remove Filter\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n};\n_s(TopSideButtons, \"SSIiaToJxEMZ3QNjhSfBSrh7r4o=\");\n_c = TopSideButtons;\nfunction Logs() {\n  _s2();\n  const [logs, setLogs] = useState([]);\n  const [originalLogs, setOriginalLogs] = useState([]);\n  const [selectedLogs, setSelectedLogs] = useState(null);\n  const [showDetailsModal, setShowDetailsModal] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [searchValue, setSearchValue] = useState(\"\");\n  const [debouncedValue, setDebouncedValue] = useState(\"\");\n  const [filteredLogs, setFilteredLogs] = useState(\"\");\n  const removeFilter = () => {\n    setFilteredLogs(\"\");\n    setCurrentPage(1);\n  };\n  const applyFilter = outcome => {\n    setFilteredLogs(outcome);\n    setCurrentPage(1);\n  };\n  function handleSearchInput(value) {\n    if (value.length >= 3 || value.trim() === \"\") {\n      setSearchValue(value);\n    }\n  }\n\n  // const applySearch = (value) => {\n  //   const filteredRoles = originalLogs?.filter((log) =>\n  //     log?.action_performed.toLowerCase().includes(value.toLowerCase())\n  //   );\n  //   setCurrentPage(1)\n  //   setLogs(filteredRoles);\n  // };\n\n  // Pagination logic\n  const currentLogs = logs;\n  const [totalPages, setTotalPages] = useState(0);\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(searchValue);\n    }, 700); // debounce delay\n\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [searchValue]);\n\n  // useEffect(() => {\n  //   if (debouncedValue) {\n  //     applySearch(debouncedValue);\n  //   }\n  // }, [debouncedValue]);\n\n  useEffect(() => {\n    async function fetchRoleData() {\n      var _response$logs, _response$logs2, _response$pagination;\n      let query = {\n        page: currentPage\n      };\n      if (debouncedValue) query.search = debouncedValue;\n      if (filteredLogs) query.status = filteredLogs;\n      const response = await userLogs(query);\n      setLogs((_response$logs = response.logs) !== null && _response$logs !== void 0 ? _response$logs : []);\n      setOriginalLogs((_response$logs2 = response.logs) !== null && _response$logs2 !== void 0 ? _response$logs2 : []); // Store the original unfiltered data\n      setTotalPages(((_response$pagination = response.pagination) === null || _response$pagination === void 0 ? void 0 : _response$pagination.totalPages) || 0);\n    }\n    fetchRoleData();\n  }, [currentPage, debouncedValue, filteredLogs]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"relative min-h-full\",\n    children: [/*#__PURE__*/_jsxDEV(TitleCard, {\n      title: \"Logs\",\n      topMargin: \"mt-2\",\n      TopSideButtons: /*#__PURE__*/_jsxDEV(TopSideButtons, {\n        applySearch: handleSearchInput,\n        applyFilter: applyFilter,\n        removeFilter: removeFilter,\n        openAddForm: () => {}\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 11\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-[30rem] flex flex-col justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"overflow-x-auto w-full border dark:border-stone-600 rounded-2xl\",\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            className: \"table text-center min-w-full dark:text-[white]\",\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              className: \"\",\n              style: {\n                borderRadius: \"\"\n              },\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"!capitalize\",\n                style: {\n                  textTransform: \"capitalize\"\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"font-medium text-[12px] text-left font-poppins leading-normal bg-[#FAFBFB] dark:bg-slate-700 dark:text-[white] text-[#42526D] px-[20px] py-[13px] !capitalize\",\n                  style: {\n                    position: \"static\",\n                    width: \"\"\n                  },\n                  children: \"Action Perfomed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-[#42526D] font-poppins font-medium text-[12px] leading-normal bg-[#FAFBFB] dark:bg-slate-700 dark:text-[white]  px-[20px] py-[13px] !capitalize\",\n                  children: \"Action Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-[#42526D] font-poppins font-medium text-[12px] leading-normal bg-[#FAFBFB] dark:bg-slate-700 dark:text-[white]  px-[20px] py-[13px] !capitalize\",\n                  children: \"Performed By\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 190,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-[#42526D] font-poppins font-medium text-[12px] leading-normal bg-[#FAFBFB] dark:bg-slate-700 dark:text-[white]  px-[20px] py-[13px] !capitalize\",\n                  children: \"Target\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-[#42526D] font-poppins font-medium text-[12px] leading-normal bg-[#FAFBFB] dark:bg-slate-700 dark:text-[white]  px-[20px] py-[13px] !capitalize text-center\",\n                  children: \"IP Address\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-[#42526D] font-poppins font-medium text-[12px] leading-normal bg-[#FAFBFB] dark:bg-slate-700 dark:text-[white]  px-[20px] py-[13px] text-center !capitalize\",\n                  children: \"Outcome\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-[#42526D] font-poppins font-medium text-[12px] leading-normal bg-[#FAFBFB] dark:bg-slate-700 dark:text-[white]  px-[20px] py-[13px] text-center !capitalize\",\n                  children: \"Date Time\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  className: \"text-[#42526D] font-poppins font-medium text-[12px] leading-normal bg-[#FAFBFB] dark:bg-slate-700 dark:text-[white]  px-[20px] py-[13px] text-center !capitalize\",\n                  children: \"Actions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 177,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              className: \"\",\n              children: Array.isArray(logs) && currentLogs.length > 0 ? currentLogs === null || currentLogs === void 0 ? void 0 : currentLogs.map((log, index) => {\n                var _log$user, _log$user$user, _log$ipAddress;\n                return /*#__PURE__*/_jsxDEV(\"tr\", {\n                  className: \"font-light \",\n                  style: {\n                    height: \"65px\"\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                    className: `font-poppins h-[65px] truncate font-normal text-[14px] leading-normal text-[#101828] p-[10px] pl-5 flex items-center`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-col\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"dark:text-[white]\",\n                        children: log.action_performed\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 223,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"font-poppins font-light text-[14px] leading-normal text-[#101828] px-[26px] py-[10px] dark:text-[white]\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"\",\n                      children: (log === null || log === void 0 ? void 0 : log.actionType) || \"N/A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 230,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"font-poppins font-light text-[14px] leading-normal text-[#101828] px-[26px] py-[10px] dark:text-[white]\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"\",\n                      children: (log === null || log === void 0 ? void 0 : (_log$user = log.user) === null || _log$user === void 0 ? void 0 : (_log$user$user = _log$user.user) === null || _log$user$user === void 0 ? void 0 : _log$user$user.name) || \"N/A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 233,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 232,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"font-poppins font-light text-[14px] leading-normal text-[#101828] px-[26px] py-[10px] dark:text-[white]\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"\",\n                      children: (log === null || log === void 0 ? void 0 : log.entity) || \"N/A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 239,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"font-poppins font-light text-[14px] leading-normal text-[#101828] px-[26px] py-[10px] dark:text-[white]\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"\",\n                      children: (log === null || log === void 0 ? void 0 : (_log$ipAddress = log.ipAddress) === null || _log$ipAddress === void 0 ? void 0 : _log$ipAddress.slice(7, this === null || this === void 0 ? void 0 : this.length)) || \"N/A\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"font-poppins font-light text-[14px] leading-normal text-[#101828] px-[26px] py-[10px] dark:text-[white]\",\n                    style: {\n                      whiteSpace: \"wrap\"\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"\",\n                      children: /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: `w-[85px] mx-auto \n                                before:content-['•'] before:text-2xl \n                                flex h-7 items-center text-[12px] \n                                justify-center gap-1 px-1 py-0 font-[500] \n                                ${(log === null || log === void 0 ? void 0 : log.outcome) === \"Success\" ? \"text-green-600 bg-green-100 before:text-green-600 px-1\" : (log === null || log === void 0 ? void 0 : log.outcome) === \"Failed\" ? \"text-red-600 bg-red-100 before:text-red-600\" : \"text-stone-600 bg-stone-100 before:text-stone-600\"} rounded-2xl`,\n                        style: {\n                          textTransform: \"capitalize\"\n                        },\n                        children: capitalizeWords(log === null || log === void 0 ? void 0 : log.outcome) || \"N/A\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 251,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 250,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"font-poppins font-light text-[12px] leading-normal text-[#101828] px-[16px] py-[10px] dark:text-[white]\",\n                    children: formatTimestamp(log === null || log === void 0 ? void 0 : log.timestamp) || \"N/A\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 268,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                    className: \"font-poppins font-light text-[14px] leading-normal text-[#101828] px-[26px] py-[8px] dark:text-[white]\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"w-fit mx-auto flex gap-[15px] justify-center border border border-[1px] border-[#E6E7EC] dark:border-stone-400 rounded-[8px] p-[13.6px] py-[10px]\",\n                      children: /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          setSelectedLogs(log);\n                          setShowDetailsModal(true);\n                        },\n                        children: /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"flex items-center gap-1 rounded-md text-[#101828]\",\n                          children: /*#__PURE__*/_jsxDEV(FiEye, {\n                            className: \"w-5 h-6  text-[#3b4152] dark:text-stone-200\",\n                            strokeWidth: 1\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 280,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 279,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 273,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 271,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 23\n                }, this);\n              }) : /*#__PURE__*/_jsxDEV(\"tr\", {\n                className: \"text-[14px]\",\n                children: /*#__PURE__*/_jsxDEV(\"td\", {\n                  colSpan: 8,\n                  children: \"No Data Available\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Paginations, {\n          data: logs,\n          currentPage: currentPage,\n          setCurrentPage: setCurrentPage,\n          totalPages: totalPages\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ShowLogs, {\n      log: selectedLogs,\n      show: showDetailsModal,\n      onClose: () => setShowDetailsModal(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 160,\n    columnNumber: 5\n  }, this);\n}\n_s2(Logs, \"+bfxzgmklwk/MN4UiTlRhSnWhaU=\");\n_c2 = Logs;\nexport default Logs;\nvar _c, _c2;\n$RefreshReg$(_c, \"TopSideButtons\");\n$RefreshReg$(_c2, \"Logs\");", "map": {"version": 3, "names": ["useEffect", "useState", "userLogs", "SearchBar", "TitleCard", "ShowLogs", "capitalizeWords", "Paginations", "formatTimestamp", "XMarkIcon", "FiEye", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "TopSideButtons", "removeFilter", "applyFilter", "applySearch", "_s", "filterParam", "setFilterParam", "searchText", "setSearchText", "statusFilters", "showFiltersAndApply", "status", "removeAppliedFilter", "className", "children", "styleClass", "placeholderText", "outline", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "tabIndex", "map", "key", "style", "textTransform", "_c", "Logs", "_s2", "logs", "setLogs", "originalLogs", "setOriginalLogs", "selected<PERSON><PERSON>s", "setSelectedLogs", "showDetailsModal", "setShowDetailsModal", "currentPage", "setCurrentPage", "searchValue", "setSearchValue", "debounced<PERSON><PERSON><PERSON>", "setDebouncedValue", "filteredLogs", "setFilteredLogs", "outcome", "handleSearchInput", "value", "length", "trim", "currentLogs", "totalPages", "setTotalPages", "handler", "setTimeout", "clearTimeout", "fetchRoleData", "_response$logs", "_response$logs2", "_response$pagination", "query", "page", "search", "response", "pagination", "title", "<PERSON><PERSON><PERSON><PERSON>", "openAddForm", "borderRadius", "position", "width", "Array", "isArray", "log", "index", "_log$user", "_log$user$user", "_log$ipAddress", "height", "action_performed", "actionType", "user", "name", "entity", "ip<PERSON><PERSON><PERSON>", "slice", "whiteSpace", "timestamp", "strokeWidth", "colSpan", "data", "show", "onClose", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/shade_cms/dashboard/src/features/logstable/index.jsx"], "sourcesContent": ["// libraries import\r\nimport { useEffect, useState } from \"react\";\r\n// self modules\r\nimport { userLogs } from \"../../app/fetch\";\r\nimport SearchBar from \"../../components/Input/SearchBar\";\r\nimport TitleCard from \"../../components/Cards/TitleCard\";\r\nimport ShowLogs from \"./ShowLog\";\r\nimport capitalizeWords from \"../../app/capitalizeword\";\r\nimport Paginations from \"../Component/Paginations\";\r\nimport formatTimestamp from \"../../app/TimeFormat\";\r\n// icons\r\nimport XMarkIcon from \"@heroicons/react/24/outline/XMarkIcon\";\r\nimport { FiEye } from \"react-icons/fi\";\r\nimport { LuListFilter } from \"react-icons/lu\";\r\n\r\nconst TopSideButtons = ({\r\n  removeFilter,\r\n  applyFilter,\r\n  applySearch,\r\n  // openAddForm,\r\n}) => {\r\n  const [filterParam, setFilterParam] = useState(\"\");\r\n  const [searchText, setSearchText] = useState(\"\");\r\n  const statusFilters = [\"Success\", \"Failure\"];\r\n  const showFiltersAndApply = (status) => {\r\n    applyFilter(status);\r\n    setFilterParam(status);\r\n  };\r\n  const removeAppliedFilter = () => {\r\n    removeFilter();\r\n    setFilterParam(\"\");\r\n    setSearchText(\"\");\r\n  };\r\n  useEffect(() => {\r\n    if (searchText === \"\") {\r\n      removeAppliedFilter();\r\n    } else {\r\n      applySearch(searchText);\r\n    }\r\n  }, [searchText]);\r\n  return (\r\n    <div className=\"inline-block float-right w-full flex items-center gap-3 border dark:border-neutral-600 rounded-lg p-1\">\r\n      <SearchBar\r\n        searchText={searchText}\r\n        styleClass=\"w-700px border-none w-full flex-1\"\r\n        setSearchText={setSearchText}\r\n        placeholderText={\"Search Logs by name and roles keywords\"}\r\n        outline={false}\r\n      />\r\n      {filterParam && (\r\n        <button\r\n          onClick={() => removeAppliedFilter()}\r\n          className=\"btn btn-xs mr-2 btn-active btn-ghost normal-case\"\r\n        >\r\n          {filterParam}\r\n          <XMarkIcon className=\"w-4 ml-2\" />\r\n        </button>\r\n      )}\r\n      <div className=\"dropdown dropdown-bottom dropdown-end\">\r\n        <label\r\n          tabIndex={0}\r\n          className=\"capitalize border text-[14px] self-center border-stone-300 dark:border-neutral-500 rounded-lg h-[40px] w-[91px] flex items-center gap-1 font-[300] px-[14px] py-[10px]\"\r\n        >\r\n          <LuListFilter className=\"w-5 \" />\r\n          Filter\r\n        </label>\r\n        <ul\r\n          tabIndex={0}\r\n          className=\"dropdown-content menu p-2 text-sm shadow bg-base-100 rounded-box w-52 text-[#0E2354] font-[400]\"\r\n        >\r\n          {statusFilters.map((status, key) => (\r\n            <li key={key}>\r\n              <button\r\n                className=\"dark:text-gray-300\"\r\n                onClick={() => showFiltersAndApply(status)}\r\n                style={{ textTransform: \"capitalize\" }}\r\n              >\r\n                {capitalizeWords(status)}\r\n              </button>\r\n            </li>\r\n          ))}\r\n          <div className=\"divider mt-0 mb-0\"></div>\r\n          <li>\r\n            <button className=\"dark:text-gray-300\" onClick={() => removeAppliedFilter()}>Remove Filter</button>\r\n          </li>\r\n        </ul>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\nfunction Logs() {\r\n  const [logs, setLogs] = useState([]);\r\n  const [originalLogs, setOriginalLogs] = useState([]);\r\n  const [selectedLogs, setSelectedLogs] = useState(null);\r\n  const [showDetailsModal, setShowDetailsModal] = useState(false);\r\n  const [currentPage, setCurrentPage] = useState(1);\r\n  const [searchValue, setSearchValue] = useState(\"\");\r\n  const [debouncedValue, setDebouncedValue] = useState(\"\");\r\n  const [filteredLogs, setFilteredLogs] = useState(\"\")\r\n\r\n\r\n  const removeFilter = () => {\r\n    setFilteredLogs(\"\")\r\n    setCurrentPage(1)\r\n  };\r\n\r\n  const applyFilter = (outcome) => {\r\n    setFilteredLogs(outcome)\r\n    setCurrentPage(1)\r\n  };\r\n\r\n  function handleSearchInput(value) {\r\n    if (value.length >= 3 || value.trim() === \"\") {\r\n      setSearchValue(value);\r\n    }\r\n  }\r\n\r\n  // const applySearch = (value) => {\r\n  //   const filteredRoles = originalLogs?.filter((log) =>\r\n  //     log?.action_performed.toLowerCase().includes(value.toLowerCase())\r\n  //   );\r\n  //   setCurrentPage(1)\r\n  //   setLogs(filteredRoles);\r\n  // };\r\n\r\n  // Pagination logic\r\n  const currentLogs = logs\r\n  const [totalPages, setTotalPages] = useState(0)\r\n\r\n  useEffect(() => {\r\n    const handler = setTimeout(() => {\r\n      setDebouncedValue(searchValue);\r\n    }, 700); // debounce delay\r\n\r\n    return () => {\r\n      clearTimeout(handler);\r\n    };\r\n  }, [searchValue]);\r\n\r\n  // useEffect(() => {\r\n  //   if (debouncedValue) {\r\n  //     applySearch(debouncedValue);\r\n  //   }\r\n  // }, [debouncedValue]);\r\n\r\n  useEffect(() => {\r\n    async function fetchRoleData() {\r\n      let query = { page: currentPage }\r\n      if (debouncedValue) query.search = debouncedValue\r\n      if (filteredLogs) query.status = filteredLogs\r\n      const response = await userLogs(query);\r\n      setLogs(response.logs ?? []);\r\n      setOriginalLogs(response.logs ?? []); // Store the original unfiltered data\r\n      setTotalPages(response.pagination?.totalPages || 0)\r\n    }\r\n    fetchRoleData();\r\n  }, [currentPage, debouncedValue, filteredLogs]);\r\n\r\n  return (\r\n    <div className=\"relative min-h-full\">\r\n      <TitleCard\r\n        title={\"Logs\"}\r\n        topMargin=\"mt-2\"\r\n        TopSideButtons={\r\n          <TopSideButtons\r\n            applySearch={handleSearchInput}\r\n            applyFilter={applyFilter}\r\n            removeFilter={removeFilter}\r\n            openAddForm={() => { }}\r\n          />\r\n        }\r\n      >\r\n        <div className=\"min-h-[30rem] flex flex-col justify-between\">\r\n          <div className=\"overflow-x-auto w-full border dark:border-stone-600 rounded-2xl\">\r\n            <table className=\"table text-center min-w-full dark:text-[white]\">\r\n              <thead className=\"\" style={{ borderRadius: \"\" }}>\r\n                <tr\r\n                  className=\"!capitalize\"\r\n                  style={{ textTransform: \"capitalize\" }}\r\n                >\r\n                  <th\r\n                    className=\"font-medium text-[12px] text-left font-poppins leading-normal bg-[#FAFBFB] dark:bg-slate-700 dark:text-[white] text-[#42526D] px-[20px] py-[13px] !capitalize\"\r\n                    style={{ position: \"static\", width: \"\" }}\r\n                  >\r\n                    Action Perfomed\r\n                  </th>\r\n                  <th className=\"text-[#42526D] font-poppins font-medium text-[12px] leading-normal bg-[#FAFBFB] dark:bg-slate-700 dark:text-[white]  px-[20px] py-[13px] !capitalize\">\r\n                    Action Type\r\n                  </th>\r\n                  <th className=\"text-[#42526D] font-poppins font-medium text-[12px] leading-normal bg-[#FAFBFB] dark:bg-slate-700 dark:text-[white]  px-[20px] py-[13px] !capitalize\">\r\n                    Performed By\r\n                  </th>\r\n                  <th className=\"text-[#42526D] font-poppins font-medium text-[12px] leading-normal bg-[#FAFBFB] dark:bg-slate-700 dark:text-[white]  px-[20px] py-[13px] !capitalize\">\r\n                    Target\r\n                  </th>\r\n                  <th className=\"text-[#42526D] font-poppins font-medium text-[12px] leading-normal bg-[#FAFBFB] dark:bg-slate-700 dark:text-[white]  px-[20px] py-[13px] !capitalize text-center\">\r\n                    IP Address\r\n                  </th>\r\n                  <th className=\"text-[#42526D] font-poppins font-medium text-[12px] leading-normal bg-[#FAFBFB] dark:bg-slate-700 dark:text-[white]  px-[20px] py-[13px] text-center !capitalize\">\r\n                    Outcome\r\n                  </th>\r\n                  <th className=\"text-[#42526D] font-poppins font-medium text-[12px] leading-normal bg-[#FAFBFB] dark:bg-slate-700 dark:text-[white]  px-[20px] py-[13px] text-center !capitalize\">\r\n                    Date Time\r\n                  </th>\r\n                  <th className=\"text-[#42526D] font-poppins font-medium text-[12px] leading-normal bg-[#FAFBFB] dark:bg-slate-700 dark:text-[white]  px-[20px] py-[13px] text-center !capitalize\">\r\n                    Actions\r\n                  </th>\r\n                </tr>\r\n              </thead>\r\n              <tbody className=\"\">\r\n                {Array.isArray(logs) && currentLogs.length > 0 ? (\r\n                  currentLogs?.map((log, index) => {\r\n                    return (\r\n                      <tr\r\n                        key={index}\r\n                        className=\"font-light \"\r\n                        style={{ height: \"65px\" }}\r\n                      >\r\n                        <td\r\n                          className={`font-poppins h-[65px] truncate font-normal text-[14px] leading-normal text-[#101828] p-[10px] pl-5 flex items-center`}\r\n                        >\r\n                          <div className=\"flex flex-col\">\r\n                            <p className=\"dark:text-[white]\">\r\n                              {log.action_performed}\r\n                            </p>\r\n                          </div>\r\n                        </td>\r\n\r\n                        <td className=\"font-poppins font-light text-[14px] leading-normal text-[#101828] px-[26px] py-[10px] dark:text-[white]\">\r\n                          <span className=\"\">{log?.actionType || \"N/A\"}</span>\r\n                        </td>\r\n                        <td className=\"font-poppins font-light text-[14px] leading-normal text-[#101828] px-[26px] py-[10px] dark:text-[white]\">\r\n                          <span className=\"\">\r\n                            {log?.user?.user?.name || \"N/A\"}\r\n                          </span>\r\n                        </td>\r\n\r\n                        <td className=\"font-poppins font-light text-[14px] leading-normal text-[#101828] px-[26px] py-[10px] dark:text-[white]\">\r\n                          <span className=\"\">{log?.entity || \"N/A\"}</span>\r\n                        </td>\r\n                        <td className=\"font-poppins font-light text-[14px] leading-normal text-[#101828] px-[26px] py-[10px] dark:text-[white]\">\r\n                          <span className=\"\">\r\n                            {log?.ipAddress?.slice(7, this?.length) || \"N/A\"}\r\n                          </span>\r\n                        </td>\r\n                        <td\r\n                          className=\"font-poppins font-light text-[14px] leading-normal text-[#101828] px-[26px] py-[10px] dark:text-[white]\"\r\n                          style={{ whiteSpace: \"wrap\" }}\r\n                        >\r\n                          <span className=\"\">\r\n                            <p\r\n                              className={`w-[85px] mx-auto \r\n                                before:content-['•'] before:text-2xl \r\n                                flex h-7 items-center text-[12px] \r\n                                justify-center gap-1 px-1 py-0 font-[500] \r\n                                ${log?.outcome === \"Success\" ?\r\n                                  \"text-green-600 bg-green-100 before:text-green-600 px-1\" :\r\n                                  log?.outcome === \"Failed\" ?\r\n                                    \"text-red-600 bg-red-100 before:text-red-600\"\r\n                                    : \"text-stone-600 bg-stone-100 before:text-stone-600\"\r\n                                } rounded-2xl`}\r\n                              style={{ textTransform: \"capitalize\" }}\r\n                            >\r\n                              {capitalizeWords(log?.outcome) || \"N/A\"}\r\n                            </p>\r\n                          </span>\r\n                        </td>\r\n                        <td className=\"font-poppins font-light text-[12px] leading-normal text-[#101828] px-[16px] py-[10px] dark:text-[white]\">\r\n                          {formatTimestamp(log?.timestamp) || \"N/A\"}\r\n                        </td>\r\n                        <td className=\"font-poppins font-light text-[14px] leading-normal text-[#101828] px-[26px] py-[8px] dark:text-[white]\">\r\n                          <div className=\"w-fit mx-auto flex gap-[15px] justify-center border border border-[1px] border-[#E6E7EC] dark:border-stone-400 rounded-[8px] p-[13.6px] py-[10px]\">\r\n                            <button\r\n                              onClick={() => {\r\n                                setSelectedLogs(log);\r\n                                setShowDetailsModal(true);\r\n                              }}\r\n                            >\r\n                              <span className=\"flex items-center gap-1 rounded-md text-[#101828]\">\r\n                                <FiEye\r\n                                  className=\"w-5 h-6  text-[#3b4152] dark:text-stone-200\"\r\n                                  strokeWidth={1}\r\n                                />\r\n                              </span>\r\n                            </button>\r\n                          </div>\r\n                        </td>\r\n                      </tr>\r\n                    );\r\n                  })\r\n                ) : (\r\n                  <tr className=\"text-[14px]\">\r\n                    <td colSpan={8}>No Data Available</td>\r\n                  </tr>\r\n                )}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n          {/* Pagination Controls */}\r\n          <Paginations\r\n            data={logs}\r\n            currentPage={currentPage}\r\n            setCurrentPage={setCurrentPage}\r\n            totalPages={totalPages}\r\n          />\r\n        </div>\r\n      </TitleCard>\r\n\r\n      {/* log Details Modal */}\r\n      <ShowLogs\r\n        log={selectedLogs}\r\n        show={showDetailsModal}\r\n        onClose={() => setShowDetailsModal(false)}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Logs;"], "mappings": ";;;AAAA;AACA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C;AACA,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAOC,SAAS,MAAM,kCAAkC;AACxD,OAAOC,SAAS,MAAM,kCAAkC;AACxD,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,eAAe,MAAM,sBAAsB;AAClD;AACA,OAAOC,SAAS,MAAM,uCAAuC;AAC7D,SAASC,KAAK,QAAQ,gBAAgB;AACtC,SAASC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,MAAMC,cAAc,GAAGA,CAAC;EACtBC,YAAY;EACZC,WAAW;EACXC;EACA;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGnB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAMsB,aAAa,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC;EAC5C,MAAMC,mBAAmB,GAAIC,MAAM,IAAK;IACtCT,WAAW,CAACS,MAAM,CAAC;IACnBL,cAAc,CAACK,MAAM,CAAC;EACxB,CAAC;EACD,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChCX,YAAY,CAAC,CAAC;IACdK,cAAc,CAAC,EAAE,CAAC;IAClBE,aAAa,CAAC,EAAE,CAAC;EACnB,CAAC;EACDtB,SAAS,CAAC,MAAM;IACd,IAAIqB,UAAU,KAAK,EAAE,EAAE;MACrBK,mBAAmB,CAAC,CAAC;IACvB,CAAC,MAAM;MACLT,WAAW,CAACI,UAAU,CAAC;IACzB;EACF,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAChB,oBACER,OAAA;IAAKc,SAAS,EAAC,uGAAuG;IAAAC,QAAA,gBACpHf,OAAA,CAACV,SAAS;MACRkB,UAAU,EAAEA,UAAW;MACvBQ,UAAU,EAAC,mCAAmC;MAC9CP,aAAa,EAAEA,aAAc;MAC7BQ,eAAe,EAAE,wCAAyC;MAC1DC,OAAO,EAAE;IAAM;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,EACDhB,WAAW,iBACVN,OAAA;MACEuB,OAAO,EAAEA,CAAA,KAAMV,mBAAmB,CAAC,CAAE;MACrCC,SAAS,EAAC,kDAAkD;MAAAC,QAAA,GAE3DT,WAAW,eACZN,OAAA,CAACJ,SAAS;QAACkB,SAAS,EAAC;MAAU;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CACT,eACDtB,OAAA;MAAKc,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDf,OAAA;QACEwB,QAAQ,EAAE,CAAE;QACZV,SAAS,EAAC,wKAAwK;QAAAC,QAAA,gBAElLf,OAAA,CAACF,YAAY;UAACgB,SAAS,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,UAEnC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACRtB,OAAA;QACEwB,QAAQ,EAAE,CAAE;QACZV,SAAS,EAAC,iGAAiG;QAAAC,QAAA,GAE1GL,aAAa,CAACe,GAAG,CAAC,CAACb,MAAM,EAAEc,GAAG,kBAC7B1B,OAAA;UAAAe,QAAA,eACEf,OAAA;YACEc,SAAS,EAAC,oBAAoB;YAC9BS,OAAO,EAAEA,CAAA,KAAMZ,mBAAmB,CAACC,MAAM,CAAE;YAC3Ce,KAAK,EAAE;cAAEC,aAAa,EAAE;YAAa,CAAE;YAAAb,QAAA,EAEtCtB,eAAe,CAACmB,MAAM;UAAC;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC,GAPFI,GAAG;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQR,CACL,CAAC,eACFtB,OAAA;UAAKc,SAAS,EAAC;QAAmB;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzCtB,OAAA;UAAAe,QAAA,eACEf,OAAA;YAAQc,SAAS,EAAC,oBAAoB;YAACS,OAAO,EAAEA,CAAA,KAAMV,mBAAmB,CAAC,CAAE;YAAAE,QAAA,EAAC;UAAa;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CA1EIJ,cAAc;AAAA4B,EAAA,GAAd5B,cAAc;AA2EpB,SAAS6B,IAAIA,CAAA,EAAG;EAAAC,GAAA;EACd,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAAC8C,YAAY,EAAEC,eAAe,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACgD,YAAY,EAAEC,eAAe,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACkD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACsD,WAAW,EAAEC,cAAc,CAAC,GAAGvD,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC0D,YAAY,EAAEC,eAAe,CAAC,GAAG3D,QAAQ,CAAC,EAAE,CAAC;EAGpD,MAAMc,YAAY,GAAGA,CAAA,KAAM;IACzB6C,eAAe,CAAC,EAAE,CAAC;IACnBN,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,MAAMtC,WAAW,GAAI6C,OAAO,IAAK;IAC/BD,eAAe,CAACC,OAAO,CAAC;IACxBP,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,SAASQ,iBAAiBA,CAACC,KAAK,EAAE;IAChC,IAAIA,KAAK,CAACC,MAAM,IAAI,CAAC,IAAID,KAAK,CAACE,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC5CT,cAAc,CAACO,KAAK,CAAC;IACvB;EACF;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAMG,WAAW,GAAGrB,IAAI;EACxB,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGnE,QAAQ,CAAC,CAAC,CAAC;EAE/CD,SAAS,CAAC,MAAM;IACd,MAAMqE,OAAO,GAAGC,UAAU,CAAC,MAAM;MAC/BZ,iBAAiB,CAACH,WAAW,CAAC;IAChC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;;IAET,OAAO,MAAM;MACXgB,YAAY,CAACF,OAAO,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACd,WAAW,CAAC,CAAC;;EAEjB;EACA;EACA;EACA;EACA;;EAEAvD,SAAS,CAAC,MAAM;IACd,eAAewE,aAAaA,CAAA,EAAG;MAAA,IAAAC,cAAA,EAAAC,eAAA,EAAAC,oBAAA;MAC7B,IAAIC,KAAK,GAAG;QAAEC,IAAI,EAAExB;MAAY,CAAC;MACjC,IAAII,cAAc,EAAEmB,KAAK,CAACE,MAAM,GAAGrB,cAAc;MACjD,IAAIE,YAAY,EAAEiB,KAAK,CAACnD,MAAM,GAAGkC,YAAY;MAC7C,MAAMoB,QAAQ,GAAG,MAAM7E,QAAQ,CAAC0E,KAAK,CAAC;MACtC9B,OAAO,EAAA2B,cAAA,GAACM,QAAQ,CAAClC,IAAI,cAAA4B,cAAA,cAAAA,cAAA,GAAI,EAAE,CAAC;MAC5BzB,eAAe,EAAA0B,eAAA,GAACK,QAAQ,CAAClC,IAAI,cAAA6B,eAAA,cAAAA,eAAA,GAAI,EAAE,CAAC,CAAC,CAAC;MACtCN,aAAa,CAAC,EAAAO,oBAAA,GAAAI,QAAQ,CAACC,UAAU,cAAAL,oBAAA,uBAAnBA,oBAAA,CAAqBR,UAAU,KAAI,CAAC,CAAC;IACrD;IACAK,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACnB,WAAW,EAAEI,cAAc,EAAEE,YAAY,CAAC,CAAC;EAE/C,oBACE9C,OAAA;IAAKc,SAAS,EAAC,qBAAqB;IAAAC,QAAA,gBAClCf,OAAA,CAACT,SAAS;MACR6E,KAAK,EAAE,MAAO;MACdC,SAAS,EAAC,MAAM;MAChBpE,cAAc,eACZD,OAAA,CAACC,cAAc;QACbG,WAAW,EAAE6C,iBAAkB;QAC/B9C,WAAW,EAAEA,WAAY;QACzBD,YAAY,EAAEA,YAAa;QAC3BoE,WAAW,EAAEA,CAAA,KAAM,CAAE;MAAE;QAAAnD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CACF;MAAAP,QAAA,eAEDf,OAAA;QAAKc,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAC1Df,OAAA;UAAKc,SAAS,EAAC,iEAAiE;UAAAC,QAAA,eAC9Ef,OAAA;YAAOc,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC/Df,OAAA;cAAOc,SAAS,EAAC,EAAE;cAACa,KAAK,EAAE;gBAAE4C,YAAY,EAAE;cAAG,CAAE;cAAAxD,QAAA,eAC9Cf,OAAA;gBACEc,SAAS,EAAC,aAAa;gBACvBa,KAAK,EAAE;kBAAEC,aAAa,EAAE;gBAAa,CAAE;gBAAAb,QAAA,gBAEvCf,OAAA;kBACEc,SAAS,EAAC,+JAA+J;kBACzKa,KAAK,EAAE;oBAAE6C,QAAQ,EAAE,QAAQ;oBAAEC,KAAK,EAAE;kBAAG,CAAE;kBAAA1D,QAAA,EAC1C;gBAED;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtB,OAAA;kBAAIc,SAAS,EAAC,sJAAsJ;kBAAAC,QAAA,EAAC;gBAErK;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtB,OAAA;kBAAIc,SAAS,EAAC,sJAAsJ;kBAAAC,QAAA,EAAC;gBAErK;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtB,OAAA;kBAAIc,SAAS,EAAC,sJAAsJ;kBAAAC,QAAA,EAAC;gBAErK;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtB,OAAA;kBAAIc,SAAS,EAAC,kKAAkK;kBAAAC,QAAA,EAAC;gBAEjL;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtB,OAAA;kBAAIc,SAAS,EAAC,kKAAkK;kBAAAC,QAAA,EAAC;gBAEjL;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtB,OAAA;kBAAIc,SAAS,EAAC,kKAAkK;kBAAAC,QAAA,EAAC;gBAEjL;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLtB,OAAA;kBAAIc,SAAS,EAAC,kKAAkK;kBAAAC,QAAA,EAAC;gBAEjL;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRtB,OAAA;cAAOc,SAAS,EAAC,EAAE;cAAAC,QAAA,EAChB2D,KAAK,CAACC,OAAO,CAAC3C,IAAI,CAAC,IAAIqB,WAAW,CAACF,MAAM,GAAG,CAAC,GAC5CE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE5B,GAAG,CAAC,CAACmD,GAAG,EAAEC,KAAK,KAAK;gBAAA,IAAAC,SAAA,EAAAC,cAAA,EAAAC,cAAA;gBAC/B,oBACEhF,OAAA;kBAEEc,SAAS,EAAC,aAAa;kBACvBa,KAAK,EAAE;oBAAEsD,MAAM,EAAE;kBAAO,CAAE;kBAAAlE,QAAA,gBAE1Bf,OAAA;oBACEc,SAAS,EAAE,sHAAuH;oBAAAC,QAAA,eAElIf,OAAA;sBAAKc,SAAS,EAAC,eAAe;sBAAAC,QAAA,eAC5Bf,OAAA;wBAAGc,SAAS,EAAC,mBAAmB;wBAAAC,QAAA,EAC7B6D,GAAG,CAACM;sBAAgB;wBAAA/D,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eAELtB,OAAA;oBAAIc,SAAS,EAAC,yGAAyG;oBAAAC,QAAA,eACrHf,OAAA;sBAAMc,SAAS,EAAC,EAAE;sBAAAC,QAAA,EAAE,CAAA6D,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEO,UAAU,KAAI;oBAAK;sBAAAhE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACLtB,OAAA;oBAAIc,SAAS,EAAC,yGAAyG;oBAAAC,QAAA,eACrHf,OAAA;sBAAMc,SAAS,EAAC,EAAE;sBAAAC,QAAA,EACf,CAAA6D,GAAG,aAAHA,GAAG,wBAAAE,SAAA,GAAHF,GAAG,CAAEQ,IAAI,cAAAN,SAAA,wBAAAC,cAAA,GAATD,SAAA,CAAWM,IAAI,cAAAL,cAAA,uBAAfA,cAAA,CAAiBM,IAAI,KAAI;oBAAK;sBAAAlE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3B;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eAELtB,OAAA;oBAAIc,SAAS,EAAC,yGAAyG;oBAAAC,QAAA,eACrHf,OAAA;sBAAMc,SAAS,EAAC,EAAE;sBAAAC,QAAA,EAAE,CAAA6D,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEU,MAAM,KAAI;oBAAK;sBAAAnE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9C,CAAC,eACLtB,OAAA;oBAAIc,SAAS,EAAC,yGAAyG;oBAAAC,QAAA,eACrHf,OAAA;sBAAMc,SAAS,EAAC,EAAE;sBAAAC,QAAA,EACf,CAAA6D,GAAG,aAAHA,GAAG,wBAAAI,cAAA,GAAHJ,GAAG,CAAEW,SAAS,cAAAP,cAAA,uBAAdA,cAAA,CAAgBQ,KAAK,CAAC,CAAC,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAErC,MAAM,CAAC,KAAI;oBAAK;sBAAAhC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACLtB,OAAA;oBACEc,SAAS,EAAC,yGAAyG;oBACnHa,KAAK,EAAE;sBAAE8D,UAAU,EAAE;oBAAO,CAAE;oBAAA1E,QAAA,eAE9Bf,OAAA;sBAAMc,SAAS,EAAC,EAAE;sBAAAC,QAAA,eAChBf,OAAA;wBACEc,SAAS,EAAE;AACzC;AACA;AACA;AACA,kCAAkC,CAAA8D,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE5B,OAAO,MAAK,SAAS,GAC1B,wDAAwD,GACxD,CAAA4B,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE5B,OAAO,MAAK,QAAQ,GACvB,6CAA6C,GAC3C,mDAAmD,cAC1C;wBACjBrB,KAAK,EAAE;0BAAEC,aAAa,EAAE;wBAAa,CAAE;wBAAAb,QAAA,EAEtCtB,eAAe,CAACmF,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAE5B,OAAO,CAAC,IAAI;sBAAK;wBAAA7B,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,eACLtB,OAAA;oBAAIc,SAAS,EAAC,yGAAyG;oBAAAC,QAAA,EACpHpB,eAAe,CAACiF,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEc,SAAS,CAAC,IAAI;kBAAK;oBAAAvE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvC,CAAC,eACLtB,OAAA;oBAAIc,SAAS,EAAC,wGAAwG;oBAAAC,QAAA,eACpHf,OAAA;sBAAKc,SAAS,EAAC,mJAAmJ;sBAAAC,QAAA,eAChKf,OAAA;wBACEuB,OAAO,EAAEA,CAAA,KAAM;0BACbc,eAAe,CAACuC,GAAG,CAAC;0BACpBrC,mBAAmB,CAAC,IAAI,CAAC;wBAC3B,CAAE;wBAAAxB,QAAA,eAEFf,OAAA;0BAAMc,SAAS,EAAC,mDAAmD;0BAAAC,QAAA,eACjEf,OAAA,CAACH,KAAK;4BACJiB,SAAS,EAAC,6CAA6C;4BACvD6E,WAAW,EAAE;0BAAE;4BAAAxE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAChB;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GAxEAuD,KAAK;kBAAA1D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyER,CAAC;cAET,CAAC,CAAC,gBAEFtB,OAAA;gBAAIc,SAAS,EAAC,aAAa;gBAAAC,QAAA,eACzBf,OAAA;kBAAI4F,OAAO,EAAE,CAAE;kBAAA7E,QAAA,EAAC;gBAAiB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YACL;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAENtB,OAAA,CAACN,WAAW;UACVmG,IAAI,EAAE7D,IAAK;UACXQ,WAAW,EAAEA,WAAY;UACzBC,cAAc,EAAEA,cAAe;UAC/Ba,UAAU,EAAEA;QAAW;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGZtB,OAAA,CAACR,QAAQ;MACPoF,GAAG,EAAExC,YAAa;MAClB0D,IAAI,EAAExD,gBAAiB;MACvByD,OAAO,EAAEA,CAAA,KAAMxD,mBAAmB,CAAC,KAAK;IAAE;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV;AAACS,GAAA,CAlOQD,IAAI;AAAAkE,GAAA,GAAJlE,IAAI;AAoOb,eAAeA,IAAI;AAAC,IAAAD,EAAA,EAAAmE,GAAA;AAAAC,YAAA,CAAApE,EAAA;AAAAoE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}