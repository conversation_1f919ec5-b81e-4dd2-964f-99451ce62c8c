{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\shade_cms\\\\dashboard\\\\src\\\\features\\\\Resources\\\\components\\\\contentmanager\\\\CMforDetails\\\\ServiceDetailsManager.jsx\",\n  _s = $RefreshSig$();\nimport { useDispatch } from \"react-redux\";\nimport FileUploader from \"../../../../../components/Input/InputFileUploader\";\nimport { updateMainContent } from \"../../../../common/homeContentSlice\";\nimport ContentSection from \"../../breakUI/ContentSections\";\nimport MultiSelectForProjects from \"../../breakUI/MultiSelectForProjects\";\nimport content from \"../../websiteComponent/content.json\";\nimport { useEffect } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ServiceDetailsManager = ({\n  serviceId,\n  currentContent,\n  currentPath,\n  language\n}) => {\n  _s();\n  var _currentContent, _currentContent2;\n  const dispatch = useDispatch();\n  const serviceIndex = currentContent === null || currentContent === void 0 ? void 0 : currentContent.findIndex(e => {\n    return e.id == serviceId;\n  });\n\n  // useEffect(() => {\n  //     dispatch(updateMainContent({ currentPath: \"serviceDetails\", payload: content.serviceDetails }))\n  // }, [])\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `w-[299px]`,\n    children: [/*#__PURE__*/_jsxDEV(FileUploader, {\n      id: \"ServiceDetailsIDReference\" + serviceId,\n      label: \"Rerference doc\",\n      fileName: \"Upload your file...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(ContentSection, {\n      currentPath: currentPath,\n      Heading: \"Banner\",\n      inputs: [{\n        input: \"input\",\n        label: \"Heading/title\",\n        updateType: \"title\"\n      }, {\n        input: \"textarea\",\n        label: \"Description\",\n        updateType: \"description\"\n      }],\n      inputFiles: [{\n        label: \"Backround Image\",\n        id: \"serviceBanner/\" + serviceId\n      }],\n      section: \"banner\",\n      language: language,\n      currentContent: currentContent,\n      projectId: serviceIndex + 1\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(MultiSelectForProjects, {\n      heading: \"Sub Services Section\",\n      tabName: \"Select Sub Services\",\n      language: language,\n      referenceOriginal: {\n        dir: \"subServices\"\n      },\n      currentPath: currentPath,\n      options: currentContent === null || currentContent === void 0 ? void 0 : (_currentContent = currentContent[serviceId - 1]) === null || _currentContent === void 0 ? void 0 : _currentContent.subServices,\n      id: serviceIndex\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 38,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(MultiSelectForProjects, {\n      heading: \"Sub Services Section\",\n      tabName: \"Select Sub Services\",\n      language: language,\n      referenceOriginal: {\n        dir: \"otherServices\"\n      },\n      currentPath: currentPath,\n      options: currentContent === null || currentContent === void 0 ? void 0 : (_currentContent2 = currentContent[serviceId - 1]) === null || _currentContent2 === void 0 ? void 0 : _currentContent2.otherServices,\n      id: serviceIndex\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 9\n  }, this);\n};\n_s(ServiceDetailsManager, \"rgTLoBID190wEKCp9+G8W6F7A5M=\", false, function () {\n  return [useDispatch];\n});\n_c = ServiceDetailsManager;\nexport default ServiceDetailsManager;\nvar _c;\n$RefreshReg$(_c, \"ServiceDetailsManager\");", "map": {"version": 3, "names": ["useDispatch", "FileUploader", "update<PERSON>ain<PERSON><PERSON>nt", "ContentSection", "MultiSelectForProjects", "content", "useEffect", "jsxDEV", "_jsxDEV", "ServiceDetailsManager", "serviceId", "currentC<PERSON>nt", "currentPath", "language", "_s", "_currentContent", "_currentContent2", "dispatch", "serviceIndex", "findIndex", "e", "id", "className", "children", "label", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Heading", "inputs", "input", "updateType", "inputFiles", "section", "projectId", "heading", "tabName", "referenceOriginal", "dir", "options", "subServices", "otherServices", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/shade_cms/dashboard/src/features/Resources/components/contentmanager/CMforDetails/ServiceDetailsManager.jsx"], "sourcesContent": ["import { useDispatch } from \"react-redux\"\r\nimport FileUploader from \"../../../../../components/Input/InputFileUploader\"\r\nimport { updateMainContent } from \"../../../../common/homeContentSlice\"\r\nimport ContentSection from \"../../breakUI/ContentSections\"\r\nimport MultiSelectForProjects from \"../../breakUI/MultiSelectForProjects\"\r\nimport content from \"../../websiteComponent/content.json\"\r\nimport { useEffect } from \"react\"\r\n\r\nconst ServiceDetailsManager = ({ serviceId, currentContent, currentPath, language,  }) => {\r\n    const dispatch = useDispatch()\r\n    const serviceIndex = currentContent?.findIndex(e => {\r\n        return e.id == serviceId\r\n    })\r\n\r\n    // useEffect(() => {\r\n    //     dispatch(updateMainContent({ currentPath: \"serviceDetails\", payload: content.serviceDetails }))\r\n    // }, [])\r\n    return (\r\n        <div className={`w-[299px]`}>\r\n            {/* file doc */}\r\n            <FileUploader id={\"ServiceDetailsIDReference\" + serviceId} label={\"Rerference doc\"} fileName={\"Upload your file...\"} />\r\n            {/** Hero Banner */}\r\n            <ContentSection\r\n                currentPath={currentPath}\r\n                Heading={\"Banner\"}\r\n                inputs={[\r\n                    { input: \"input\", label: \"Heading/title\", updateType: \"title\" },\r\n                    { input: \"textarea\", label: \"Description\", updateType: \"description\" },\r\n                ]}\r\n                inputFiles={[{ label: \"Backround Image\", id: \"serviceBanner/\" + (serviceId) }]}\r\n                section={\"banner\"}\r\n                language={language}\r\n                currentContent={currentContent}\r\n                projectId={serviceIndex + 1}\r\n            />\r\n\r\n            {/* sub services */}\r\n            <MultiSelectForProjects\r\n                heading={\"Sub Services Section\"}\r\n                tabName={\"Select Sub Services\"}\r\n                language={language}\r\n                referenceOriginal={{ dir: \"subServices\" }}\r\n                currentPath={currentPath}\r\n                options={currentContent?.[serviceId - 1]?.subServices}\r\n                id={serviceIndex}\r\n            />\r\n\r\n            {/* other services */}\r\n            <MultiSelectForProjects\r\n                heading={\"Sub Services Section\"}\r\n                tabName={\"Select Sub Services\"}\r\n                language={language}\r\n                referenceOriginal={{ dir: \"otherServices\" }}\r\n                currentPath={currentPath}\r\n                options={currentContent?.[serviceId - 1]?.otherServices}\r\n                id={serviceIndex}\r\n            />\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default ServiceDetailsManager"], "mappings": ";;AAAA,SAASA,WAAW,QAAQ,aAAa;AACzC,OAAOC,YAAY,MAAM,mDAAmD;AAC5E,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,OAAOC,cAAc,MAAM,+BAA+B;AAC1D,OAAOC,sBAAsB,MAAM,sCAAsC;AACzE,OAAOC,OAAO,MAAM,qCAAqC;AACzD,SAASC,SAAS,QAAQ,OAAO;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEjC,MAAMC,qBAAqB,GAAGA,CAAC;EAAEC,SAAS;EAAEC,cAAc;EAAEC,WAAW;EAAEC;AAAW,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,gBAAA;EACtF,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAMkB,YAAY,GAAGP,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEQ,SAAS,CAACC,CAAC,IAAI;IAChD,OAAOA,CAAC,CAACC,EAAE,IAAIX,SAAS;EAC5B,CAAC,CAAC;;EAEF;EACA;EACA;EACA,oBACIF,OAAA;IAAKc,SAAS,EAAE,WAAY;IAAAC,QAAA,gBAExBf,OAAA,CAACP,YAAY;MAACoB,EAAE,EAAE,2BAA2B,GAAGX,SAAU;MAACc,KAAK,EAAE,gBAAiB;MAACC,QAAQ,EAAE;IAAsB;MAAAA,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEvHpB,OAAA,CAACL,cAAc;MACXS,WAAW,EAAEA,WAAY;MACzBiB,OAAO,EAAE,QAAS;MAClBC,MAAM,EAAE,CACJ;QAAEC,KAAK,EAAE,OAAO;QAAEP,KAAK,EAAE,eAAe;QAAEQ,UAAU,EAAE;MAAQ,CAAC,EAC/D;QAAED,KAAK,EAAE,UAAU;QAAEP,KAAK,EAAE,aAAa;QAAEQ,UAAU,EAAE;MAAc,CAAC,CACxE;MACFC,UAAU,EAAE,CAAC;QAAET,KAAK,EAAE,iBAAiB;QAAEH,EAAE,EAAE,gBAAgB,GAAIX;MAAW,CAAC,CAAE;MAC/EwB,OAAO,EAAE,QAAS;MAClBrB,QAAQ,EAAEA,QAAS;MACnBF,cAAc,EAAEA,cAAe;MAC/BwB,SAAS,EAAEjB,YAAY,GAAG;IAAE;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC,eAGFpB,OAAA,CAACJ,sBAAsB;MACnBgC,OAAO,EAAE,sBAAuB;MAChCC,OAAO,EAAE,qBAAsB;MAC/BxB,QAAQ,EAAEA,QAAS;MACnByB,iBAAiB,EAAE;QAAEC,GAAG,EAAE;MAAc,CAAE;MAC1C3B,WAAW,EAAEA,WAAY;MACzB4B,OAAO,EAAE7B,cAAc,aAAdA,cAAc,wBAAAI,eAAA,GAAdJ,cAAc,CAAGD,SAAS,GAAG,CAAC,CAAC,cAAAK,eAAA,uBAA/BA,eAAA,CAAiC0B,WAAY;MACtDpB,EAAE,EAAEH;IAAa;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,eAGFpB,OAAA,CAACJ,sBAAsB;MACnBgC,OAAO,EAAE,sBAAuB;MAChCC,OAAO,EAAE,qBAAsB;MAC/BxB,QAAQ,EAAEA,QAAS;MACnByB,iBAAiB,EAAE;QAAEC,GAAG,EAAE;MAAgB,CAAE;MAC5C3B,WAAW,EAAEA,WAAY;MACzB4B,OAAO,EAAE7B,cAAc,aAAdA,cAAc,wBAAAK,gBAAA,GAAdL,cAAc,CAAGD,SAAS,GAAG,CAAC,CAAC,cAAAM,gBAAA,uBAA/BA,gBAAA,CAAiC0B,aAAc;MACxDrB,EAAE,EAAEH;IAAa;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEd,CAAC;AAAAd,EAAA,CAnDKL,qBAAqB;EAAA,QACNT,WAAW;AAAA;AAAA2C,EAAA,GAD1BlC,qBAAqB;AAqD3B,eAAeA,qBAAqB;AAAA,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}