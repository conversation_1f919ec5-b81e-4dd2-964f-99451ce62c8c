{"ast": null, "code": "import { configureStore } from '@reduxjs/toolkit';\nimport headerSlice from '../features/common/headerSlice';\nimport modalSlice from '../features/common/modalSlice';\nimport rightDrawerSlice from '../features/common/rightDrawerSlice';\nimport leadsSlice from '../features/Resources/leadSlice';\nimport userSlice from \"../features/common/userSlice\";\nimport debounceSlice from \"../features/common/debounceSlice\";\nimport sidebarReducer from \"../features/common/SbStateSlice\";\nimport imagesReducer from '../features/common/ImagesSlice';\nimport homeContentReducer from '../features/common/homeContentSlice';\nimport InitialContentReducer from \"../features/common/InitialContentSlice\";\nimport navBarReducer from '../features/common/navbarSlice';\nimport routeListsReducer from '../features/common/routeLists';\nimport versionsReducer from \"../features/common/resourceSlice\";\nimport platformReducer from \"../features/common/platformSlice\";\nimport fontFamilyReducer from \"../features/common/fontStyle\";\nconst combinedReducer = {\n  header: headerSlice,\n  rightDrawer: rightDrawerSlice,\n  modal: modalSlice,\n  lead: leadsSlice,\n  user: userSlice,\n  debounce: debounceSlice,\n  sidebar: sidebarReducer,\n  homeContent: homeContentReducer,\n  InitialContentValue: InitialContentReducer,\n  navBar: navBarReducer,\n  routesList: routeListsReducer,\n  versions: versionsReducer,\n  platform: platformReducer,\n  fontStyle: fontFamilyReducer\n};\nexport default configureStore({\n  reducer: combinedReducer\n});", "map": {"version": 3, "names": ["configureStore", "headerSlice", "modalSlice", "rightDrawerSlice", "leadsSlice", "userSlice", "debounceSlice", "sidebarReducer", "imagesReducer", "homeContentReducer", "InitialContentReducer", "navBarReducer", "routeListsReducer", "versionsReducer", "platformReducer", "fontFamilyReducer", "combinedReducer", "header", "rightD<PERSON><PERSON>", "modal", "lead", "user", "debounce", "sidebar", "homeContent", "InitialContentValue", "navBar", "routesList", "versions", "platform", "fontStyle", "reducer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/shade_cms/dashboard/src/app/store.js"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit'\r\nimport headerSlice from '../features/common/headerSlice'\r\nimport modalSlice from '../features/common/modalSlice'\r\nimport rightDrawerSlice from '../features/common/rightDrawerSlice'\r\nimport leadsSlice from '../features/Resources/leadSlice'\r\nimport userSlice from \"../features/common/userSlice\"\r\nimport debounceSlice from \"../features/common/debounceSlice\"\r\nimport sidebarReducer from \"../features/common/SbStateSlice\";\r\nimport imagesReducer from '../features/common/ImagesSlice'\r\nimport homeContentReducer from '../features/common/homeContentSlice'\r\nimport InitialContentReducer from \"../features/common/InitialContentSlice\"\r\nimport navBarReducer from '../features/common/navbarSlice'\r\nimport routeListsReducer from '../features/common/routeLists'\r\nimport versionsReducer from \"../features/common/resourceSlice\"\r\nimport platformReducer from \"../features/common/platformSlice\"\r\nimport fontFamilyReducer from \"../features/common/fontStyle\"\r\n\r\nconst combinedReducer = {\r\n  header: headerSlice,\r\n  rightDrawer: rightDrawerSlice,\r\n  modal: modalSlice,\r\n  lead: leadsSlice,\r\n  user: userSlice,\r\n  debounce: debounceSlice,\r\n  sidebar: sidebarReducer,\r\n  homeContent: homeContentReducer,\r\n  InitialContentValue: InitialContentReducer,\r\n  navBar: navBarReducer,\r\n  routesList: routeListsReducer,\r\n  versions: versionsReducer,\r\n  platform: platformReducer,\r\n  fontStyle: fontFamilyReducer\r\n}\r\n\r\nexport default configureStore({\r\n  reducer: combinedReducer\r\n})"], "mappings": "AAAA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,OAAOC,WAAW,MAAM,gCAAgC;AACxD,OAAOC,UAAU,MAAM,+BAA+B;AACtD,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,UAAU,MAAM,iCAAiC;AACxD,OAAOC,SAAS,MAAM,8BAA8B;AACpD,OAAOC,aAAa,MAAM,kCAAkC;AAC5D,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,OAAOC,qBAAqB,MAAM,wCAAwC;AAC1E,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,iBAAiB,MAAM,8BAA8B;AAE5D,MAAMC,eAAe,GAAG;EACtBC,MAAM,EAAEhB,WAAW;EACnBiB,WAAW,EAAEf,gBAAgB;EAC7BgB,KAAK,EAAEjB,UAAU;EACjBkB,IAAI,EAAEhB,UAAU;EAChBiB,IAAI,EAAEhB,SAAS;EACfiB,QAAQ,EAAEhB,aAAa;EACvBiB,OAAO,EAAEhB,cAAc;EACvBiB,WAAW,EAAEf,kBAAkB;EAC/BgB,mBAAmB,EAAEf,qBAAqB;EAC1CgB,MAAM,EAAEf,aAAa;EACrBgB,UAAU,EAAEf,iBAAiB;EAC7BgB,QAAQ,EAAEf,eAAe;EACzBgB,QAAQ,EAAEf,eAAe;EACzBgB,SAAS,EAAEf;AACb,CAAC;AAED,eAAef,cAAc,CAAC;EAC5B+B,OAAO,EAAEf;AACX,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}