{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\shade_cms\\\\dashboard\\\\src\\\\features\\\\Resources\\\\components\\\\contentmanager\\\\HomeManager.jsx\",\n  _s = $RefreshSig$();\n// import { useSelector } from \"react-redux\";\nimport { useEffect, useState } from \"react\";\nimport FileUploader from \"../../../../components/Input/InputFileUploader\";\nimport ContentSection from \"../breakUI/ContentSections\";\nimport MultiSelect from \"../breakUI/MultiSelect\";\nimport { updateMainContent } from \"../../../common/homeContentSlice\";\n// import content from \"../websiteComponent/content.json\"\nimport { getResources } from \"../../../../app/fetch\";\nimport createContent from \"../../defineContent\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomeManager = ({\n  language,\n  currentPath,\n  outOfEditing\n}) => {\n  _s();\n  var _content$, _content$$content, _content$$content$tit, _content$2, _content$2$content, _content$2$content$de, _content$3, _content$3$content, _content$3$content$bu, _content$3$content$bu2, _content$3$content$bu3, _content$4, _content$4$content, _content$4$content$im, _content$4$content$im2, _content$5, _content$5$content, _content$5$content$ti, _content$6, _content$6$content, _content$6$content$de, _content$7, _content$7$content, _content$7$content$bu, _content$7$content$bu2, _content$7$content$bu3, _content$8, _content$8$content, _content$8$content$im, _content$8$content$im2, _content$9, _content$10, _content$10$content, _content$10$content$t, _content$11, _content$11$content, _content$11$content$d, _content$12, _content$12$content, _content$12$content$b, _content$12$content$b2, _content$12$content$b3, _content$16, _content$16$sections, _content$18, _content$18$content, _content$19, _content$19$content, _content$20, _content$20$content, _content$20$content$c, _content$21, _content$21$content, _content$22, _content$23, _content$23$content, _content$23$content$t, _content$24, _content$24$content, _content$24$content$d, _content$25, _content$25$content, _content$25$content$b, _content$25$content$b2, _content$25$content$b3;\n  // states\n  const [currentId, setCurrentId] = useState(\"\");\n  const [ServicesOptions, setServicesOptions] = useState([]);\n  const [ProjectOptions, setProjectOptions] = useState([]);\n  const [TestimonialsOptions, setTestimonialsOptions] = useState([]);\n  const currentContent = useSelector(state => state.homeContent.present);\n  const {\n    content,\n    indexes\n  } = createContent(currentContent, \"edit\");\n  const dispatch = useDispatch();\n  // fucntions\n\n  useEffect(() => {\n    async function getOptionsforServices() {\n      const response = await getResources({\n        resourceType: \"SUB_PAGE\",\n        resourceTag: \"SERVICE\",\n        apiCallType: \"INTERNAL\"\n      });\n      const response2 = await getResources({\n        resourceType: \"SUB_PAGE\",\n        resourceTag: \"PROJECT\",\n        apiCallType: \"INTERNAL\"\n      });\n      const response3 = await getResources({\n        resourceType: \"SUB_PAGE\",\n        resourceTag: \"TESTIMONIAL\",\n        fetchType: \"CONTENT\",\n        apiCallType: \"INTERNAL\"\n      });\n      if (response.message === \"Success\") {\n        var _response$resources, _response$resources$r;\n        let options = response === null || response === void 0 ? void 0 : (_response$resources = response.resources) === null || _response$resources === void 0 ? void 0 : (_response$resources$r = _response$resources.resources) === null || _response$resources$r === void 0 ? void 0 : _response$resources$r.map((e, i) => ({\n          id: e.id,\n          order: i + 1,\n          slug: e.slug,\n          titleEn: e.titleEn,\n          titleAr: e.titleAr\n          // icon: e.icon,\n          // image: e.image\n        }));\n        setServicesOptions(options);\n      }\n      if (response2.message === \"Success\") {\n        var _response2$resources, _response2$resources$;\n        let options = response2 === null || response2 === void 0 ? void 0 : (_response2$resources = response2.resources) === null || _response2$resources === void 0 ? void 0 : (_response2$resources$ = _response2$resources.resources) === null || _response2$resources$ === void 0 ? void 0 : _response2$resources$.map((e, i) => ({\n          id: e.id,\n          order: i + 1,\n          slug: e.slug,\n          titleEn: e.titleEn,\n          titleAr: e.titleAr\n          // icon: e.icon,\n          // image: e.image\n        }));\n        setProjectOptions(options);\n      }\n      if (response3.ok) {\n        var _response3$resources, _response3$resources$;\n        let options = response3 === null || response3 === void 0 ? void 0 : (_response3$resources = response3.resources) === null || _response3$resources === void 0 ? void 0 : (_response3$resources$ = _response3$resources.resources) === null || _response3$resources$ === void 0 ? void 0 : _response3$resources$.map((e, i) => ({\n          id: e.id,\n          order: i + 1,\n          slug: e.slug,\n          titleEn: e.titleEn,\n          titleAr: e.titleAr,\n          // icon: e.icon,\n          // image: e.image\n          liveModeVersionData: e.liveModeVersionData\n        }));\n        setTestimonialsOptions(options);\n      }\n    }\n    getOptionsforServices();\n  }, []);\n\n  // useEffect(() => {\n  //     return () => dispatch(updateMainContent({ currentPath: \"content\", payload: undefined }))\n  // }, [])\n\n  return (\n    /*#__PURE__*/\n    /// Component\n    _jsxDEV(\"div\", {\n      className: \"w-full\",\n      children: [/*#__PURE__*/_jsxDEV(FileUploader, {\n        id: \"homeReference\",\n        label: \"Rerference doc\",\n        fileName: \"Upload your file...\",\n        outOfEditing: outOfEditing\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(ContentSection, {\n        currentPath: currentPath,\n        Heading: \"Hero Banner\",\n        inputs: [{\n          input: \"input\",\n          label: \"Heading/title\",\n          updateType: \"title\",\n          value: content === null || content === void 0 ? void 0 : (_content$ = content[\"1\"]) === null || _content$ === void 0 ? void 0 : (_content$$content = _content$.content) === null || _content$$content === void 0 ? void 0 : (_content$$content$tit = _content$$content.title) === null || _content$$content$tit === void 0 ? void 0 : _content$$content$tit[language]\n        }, {\n          input: \"textarea\",\n          label: \"Description\",\n          updateType: \"description\",\n          maxLength: 500,\n          value: content === null || content === void 0 ? void 0 : (_content$2 = content[\"1\"]) === null || _content$2 === void 0 ? void 0 : (_content$2$content = _content$2.content) === null || _content$2$content === void 0 ? void 0 : (_content$2$content$de = _content$2$content.description) === null || _content$2$content$de === void 0 ? void 0 : _content$2$content$de[language]\n        }, {\n          input: \"input\",\n          label: \"Button Text\",\n          updateType: \"button\",\n          maxLength: 20,\n          value: content === null || content === void 0 ? void 0 : (_content$3 = content[\"1\"]) === null || _content$3 === void 0 ? void 0 : (_content$3$content = _content$3.content) === null || _content$3$content === void 0 ? void 0 : (_content$3$content$bu = _content$3$content.button) === null || _content$3$content$bu === void 0 ? void 0 : (_content$3$content$bu2 = _content$3$content$bu[0]) === null || _content$3$content$bu2 === void 0 ? void 0 : (_content$3$content$bu3 = _content$3$content$bu2.text) === null || _content$3$content$bu3 === void 0 ? void 0 : _content$3$content$bu3[language],\n          index: 0\n        }],\n        inputFiles: [{\n          label: \"Backround Image\",\n          id: \"homeBanner\",\n          order: 1,\n          url: content === null || content === void 0 ? void 0 : (_content$4 = content['1']) === null || _content$4 === void 0 ? void 0 : (_content$4$content = _content$4.content) === null || _content$4$content === void 0 ? void 0 : (_content$4$content$im = _content$4$content.images) === null || _content$4$content$im === void 0 ? void 0 : (_content$4$content$im2 = _content$4$content$im[0]) === null || _content$4$content$im2 === void 0 ? void 0 : _content$4$content$im2.url\n        }],\n        section: \"homeBanner\",\n        language: language,\n        currentContent: content,\n        sectionIndex: indexes === null || indexes === void 0 ? void 0 : indexes[\"1\"],\n        resourceId: currentId,\n        outOfEditing: outOfEditing\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(ContentSection, {\n        currentPath: currentPath,\n        Heading: \"About Section\",\n        inputs: [{\n          input: \"input\",\n          label: \"Heading/title\",\n          updateType: \"title\",\n          value: content === null || content === void 0 ? void 0 : (_content$5 = content['2']) === null || _content$5 === void 0 ? void 0 : (_content$5$content = _content$5.content) === null || _content$5$content === void 0 ? void 0 : (_content$5$content$ti = _content$5$content.title) === null || _content$5$content$ti === void 0 ? void 0 : _content$5$content$ti[language]\n        }, {\n          input: \"richtext\",\n          label: \"About section\",\n          updateType: \"description\",\n          maxLength: 800,\n          value: content === null || content === void 0 ? void 0 : (_content$6 = content['2']) === null || _content$6 === void 0 ? void 0 : (_content$6$content = _content$6.content) === null || _content$6$content === void 0 ? void 0 : (_content$6$content$de = _content$6$content.description) === null || _content$6$content$de === void 0 ? void 0 : _content$6$content$de[language]\n        }, {\n          input: \"input\",\n          label: \"Button Text\",\n          updateType: \"button\",\n          value: content === null || content === void 0 ? void 0 : (_content$7 = content['2']) === null || _content$7 === void 0 ? void 0 : (_content$7$content = _content$7.content) === null || _content$7$content === void 0 ? void 0 : (_content$7$content$bu = _content$7$content.button) === null || _content$7$content$bu === void 0 ? void 0 : (_content$7$content$bu2 = _content$7$content$bu[0]) === null || _content$7$content$bu2 === void 0 ? void 0 : (_content$7$content$bu3 = _content$7$content$bu2.text) === null || _content$7$content$bu3 === void 0 ? void 0 : _content$7$content$bu3[language],\n          index: 0\n        }],\n        inputFiles: [{\n          label: \"Backround Image\",\n          id: \"aboutUsSection\",\n          order: 1,\n          url: content === null || content === void 0 ? void 0 : (_content$8 = content[\"2\"]) === null || _content$8 === void 0 ? void 0 : (_content$8$content = _content$8.content) === null || _content$8$content === void 0 ? void 0 : (_content$8$content$im = _content$8$content.images) === null || _content$8$content$im === void 0 ? void 0 : (_content$8$content$im2 = _content$8$content$im[0]) === null || _content$8$content$im2 === void 0 ? void 0 : _content$8$content$im2.url\n        }],\n        section: \"aboutUsSection\",\n        language: language,\n        currentContent: content,\n        sectionIndex: indexes === null || indexes === void 0 ? void 0 : indexes['2'],\n        resourceId: currentId,\n        outOfEditing: outOfEditing\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(MultiSelect, {\n        currentPath: currentPath,\n        section: \"serviceSection\",\n        language: language,\n        label: \"Select Service List\",\n        heading: \"Services Section\",\n        tabName: \"Select Services\",\n        options: content === null || content === void 0 ? void 0 : (_content$9 = content['3']) === null || _content$9 === void 0 ? void 0 : _content$9.items,\n        listOptions: ServicesOptions,\n        referenceOriginal: {\n          dir: \"home\",\n          index: 0\n        },\n        currentContent: content,\n        sectionIndex: indexes === null || indexes === void 0 ? void 0 : indexes['3'],\n        outOfEditing: outOfEditing\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full\",\n        children: [/*#__PURE__*/_jsxDEV(ContentSection, {\n          currentPath: currentPath,\n          Heading: \"Experience Section\",\n          inputs: [{\n            input: \"input\",\n            label: \"Heading/title\",\n            updateType: \"title\",\n            value: content === null || content === void 0 ? void 0 : (_content$10 = content['4']) === null || _content$10 === void 0 ? void 0 : (_content$10$content = _content$10.content) === null || _content$10$content === void 0 ? void 0 : (_content$10$content$t = _content$10$content.title) === null || _content$10$content$t === void 0 ? void 0 : _content$10$content$t[language]\n          }, {\n            input: \"textarea\",\n            label: \"Description\",\n            updateType: \"description\",\n            value: content === null || content === void 0 ? void 0 : (_content$11 = content['4']) === null || _content$11 === void 0 ? void 0 : (_content$11$content = _content$11.content) === null || _content$11$content === void 0 ? void 0 : (_content$11$content$d = _content$11$content.description) === null || _content$11$content$d === void 0 ? void 0 : _content$11$content$d[language]\n          }, {\n            input: \"input\",\n            label: \"Button Text\",\n            updateType: \"button\",\n            value: content === null || content === void 0 ? void 0 : (_content$12 = content['4']) === null || _content$12 === void 0 ? void 0 : (_content$12$content = _content$12.content) === null || _content$12$content === void 0 ? void 0 : (_content$12$content$b = _content$12$content.button) === null || _content$12$content$b === void 0 ? void 0 : (_content$12$content$b2 = _content$12$content$b[0]) === null || _content$12$content$b2 === void 0 ? void 0 : (_content$12$content$b3 = _content$12$content$b2.text) === null || _content$12$content$b3 === void 0 ? void 0 : _content$12$content$b3[language],\n            index: 0\n          }],\n          isBorder: false,\n          fileId: \"experienceSection\",\n          section: \"experienceSection\",\n          language: language,\n          currentContent: content,\n          sectionIndex: indexes === null || indexes === void 0 ? void 0 : indexes['4'],\n          resourceId: currentId,\n          outOfEditing: outOfEditing\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 17\n        }, this), [\"Item 1\", \"Item 2\", \"Item 3\", \"Item 4\"].map((item, index, array) => {\n          var _content$13, _content$13$content, _content$13$content$c, _content$13$content$c2, _content$14, _content$14$content, _content$14$content$c, _content$14$content$c2, _content$14$content$c3, _content$15, _content$15$content, _content$15$content$c, _content$15$content$c2;\n          const isLast = index === array.length - 1;\n          return /*#__PURE__*/_jsxDEV(ContentSection, {\n            currentPath: currentPath,\n            subHeading: item,\n            inputs: [{\n              input: \"input\",\n              label: \"Item text 1\",\n              updateType: \"count\",\n              value: content === null || content === void 0 ? void 0 : (_content$13 = content['4']) === null || _content$13 === void 0 ? void 0 : (_content$13$content = _content$13.content) === null || _content$13$content === void 0 ? void 0 : (_content$13$content$c = _content$13$content.cards) === null || _content$13$content$c === void 0 ? void 0 : (_content$13$content$c2 = _content$13$content$c[index]) === null || _content$13$content$c2 === void 0 ? void 0 : _content$13$content$c2.count\n            }, {\n              input: \"input\",\n              label: \"Item text 2\",\n              updateType: \"title\",\n              value: content === null || content === void 0 ? void 0 : (_content$14 = content['4']) === null || _content$14 === void 0 ? void 0 : (_content$14$content = _content$14.content) === null || _content$14$content === void 0 ? void 0 : (_content$14$content$c = _content$14$content.cards) === null || _content$14$content$c === void 0 ? void 0 : (_content$14$content$c2 = _content$14$content$c[index]) === null || _content$14$content$c2 === void 0 ? void 0 : (_content$14$content$c3 = _content$14$content$c2.title) === null || _content$14$content$c3 === void 0 ? void 0 : _content$14$content$c3[language]\n            }],\n            inputFiles: [{\n              label: \"Item Icon\",\n              id: item,\n              order: index + 1,\n              directIcon: true,\n              url: content === null || content === void 0 ? void 0 : (_content$15 = content['4']) === null || _content$15 === void 0 ? void 0 : (_content$15$content = _content$15.content) === null || _content$15$content === void 0 ? void 0 : (_content$15$content$c = _content$15$content.cards) === null || _content$15$content$c === void 0 ? void 0 : (_content$15$content$c2 = _content$15$content$c[index]) === null || _content$15$content$c2 === void 0 ? void 0 : _content$15$content$c2.icon\n            }]\n            // fileId={item}\n            ,\n            language: language,\n            section: \"experienceSection\",\n            subSection: \"cards\",\n            index: +index,\n            isBorder: isLast,\n            currentContent: content,\n            sectionIndex: indexes === null || indexes === void 0 ? void 0 : indexes['4'],\n            resourceId: currentId,\n            outOfEditing: outOfEditing\n          }, item + index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 25\n          }, this);\n        })]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: `font-semibold text-[1.25rem] mb-4 mt-4`,\n          children: \"Project Section\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 17\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: content === null || content === void 0 ? void 0 : (_content$16 = content['5']) === null || _content$16 === void 0 ? void 0 : (_content$16$sections = _content$16.sections) === null || _content$16$sections === void 0 ? void 0 : _content$16$sections.map((section, index, array) => {\n            var _section$title, _section$title$en, _section$content, _section$content$titl, _section$content2, _section$content2$des, _content$17, _content$17$sections, _content$17$sections$;\n            const names = {\n              0: \"Projects\",\n              1: \"Markets\",\n              2: \"Safety & Responsibility\"\n            };\n            const isLast = index === array.length - 1;\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3 \",\n              children: [/*#__PURE__*/_jsxDEV(ContentSection, {\n                currentPath: currentPath,\n                subHeading: 'section ' + (index + 1),\n                inputs: [{\n                  input: \"input\",\n                  label: section === null || section === void 0 ? void 0 : (_section$title = section.title) === null || _section$title === void 0 ? void 0 : (_section$title$en = _section$title.en) === null || _section$title$en === void 0 ? void 0 : _section$title$en.toUpperCase(),\n                  updateType: \"title\",\n                  value: section === null || section === void 0 ? void 0 : (_section$content = section.content) === null || _section$content === void 0 ? void 0 : (_section$content$titl = _section$content.title) === null || _section$content$titl === void 0 ? void 0 : _section$content$titl[language]\n                }, {\n                  input: \"textarea\",\n                  label: \"Description\",\n                  updateType: \"description\",\n                  maxLength: 500,\n                  value: section === null || section === void 0 ? void 0 : (_section$content2 = section.content) === null || _section$content2 === void 0 ? void 0 : (_section$content2$des = _section$content2.description) === null || _section$content2$des === void 0 ? void 0 : _section$content2$des[language]\n                }],\n                language: language,\n                section: \"recentProjectsSection\",\n                subSection: \"sections\",\n                index: +index,\n                isBorder: isLast,\n                currentContent: content,\n                sectionIndex: indexes === null || indexes === void 0 ? void 0 : indexes['5'],\n                resourceId: currentId,\n                outOfEditing: outOfEditing\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 37\n              }, this), /*#__PURE__*/_jsxDEV(MultiSelect, {\n                currentPath: currentPath,\n                language: language,\n                label: `Select ${names[index]}`,\n                tabName: \"Select Projects\",\n                options: content === null || content === void 0 ? void 0 : (_content$17 = content['5']) === null || _content$17 === void 0 ? void 0 : (_content$17$sections = _content$17.sections) === null || _content$17$sections === void 0 ? void 0 : (_content$17$sections$ = _content$17$sections[index]) === null || _content$17$sections$ === void 0 ? void 0 : _content$17$sections$.items,\n                referenceOriginal: {\n                  dir: \"recentproject\",\n                  index\n                },\n                currentContent: content,\n                sectionIndex: indexes === null || indexes === void 0 ? void 0 : indexes['5'],\n                listOptions: ProjectOptions,\n                outOfEditing: outOfEditing\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 37\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 33\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 17\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(ContentSection, {\n        currentPath: currentPath,\n        Heading: \"Client Section\",\n        inputs: [{\n          input: \"input\",\n          label: \"Heading/title\",\n          updateType: \"title\",\n          value: content === null || content === void 0 ? void 0 : (_content$18 = content['6']) === null || _content$18 === void 0 ? void 0 : (_content$18$content = _content$18.content) === null || _content$18$content === void 0 ? void 0 : _content$18$content.title[language]\n        }, {\n          input: \"input\",\n          label: \"Description\",\n          updateType: \"description\",\n          value: content === null || content === void 0 ? void 0 : (_content$19 = content['6']) === null || _content$19 === void 0 ? void 0 : (_content$19$content = _content$19.content) === null || _content$19$content === void 0 ? void 0 : _content$19$content.description[language]\n        }],\n        inputFiles: content === null || content === void 0 ? void 0 : (_content$20 = content['6']) === null || _content$20 === void 0 ? void 0 : (_content$20$content = _content$20.content) === null || _content$20$content === void 0 ? void 0 : (_content$20$content$c = _content$20$content.clientsImages) === null || _content$20$content$c === void 0 ? void 0 : _content$20$content$c.map((e, i) => ({\n          label: \"Client \" + (i + 1),\n          id: e.order,\n          order: e.order,\n          url: e.url\n        })),\n        section: \"clientsImages\",\n        language: language,\n        currentContent: content,\n        sectionIndex: indexes === null || indexes === void 0 ? void 0 : indexes['6'],\n        allowExtraInput: true,\n        resourceId: currentId,\n        outOfEditing: outOfEditing\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(ContentSection, {\n        currentPath: currentPath,\n        Heading: \"Testimonials\",\n        inputs: [{\n          input: \"input\",\n          label: \"Heading/title\",\n          maxLength: 55,\n          updateType: \"title\",\n          value: content === null || content === void 0 ? void 0 : (_content$21 = content['7']) === null || _content$21 === void 0 ? void 0 : (_content$21$content = _content$21.content) === null || _content$21$content === void 0 ? void 0 : _content$21$content.title[language]\n        }],\n        section: \"Testimonials heading\",\n        language: language,\n        currentContent: content,\n        sectionIndex: indexes === null || indexes === void 0 ? void 0 : indexes['7'],\n        resourceId: currentId,\n        outOfEditing: outOfEditing\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(MultiSelect, {\n        currentPath: currentPath,\n        section: \"testimonialsSections\",\n        language: language,\n        label: \"Select Testimony List\",\n        heading: \"Testimonials Section\",\n        tabName: \"Select Testimonies\",\n        options: content === null || content === void 0 ? void 0 : (_content$22 = content['7']) === null || _content$22 === void 0 ? void 0 : _content$22.items,\n        listOptions: TestimonialsOptions,\n        referenceOriginal: {\n          dir: \"testimonials\",\n          index: 0\n        },\n        currentContent: content,\n        sectionIndex: indexes === null || indexes === void 0 ? void 0 : indexes['7'],\n        limitOptions: 4,\n        min: 4,\n        outOfEditing: outOfEditing\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 253,\n        columnNumber: 13\n      }, this), /*#__PURE__*/_jsxDEV(ContentSection, {\n        currentPath: currentPath,\n        Heading: \"New Project\",\n        inputs: [{\n          input: \"input\",\n          label: \"Heading/title\",\n          maxLength: 55,\n          updateType: \"title\",\n          value: content === null || content === void 0 ? void 0 : (_content$23 = content['8']) === null || _content$23 === void 0 ? void 0 : (_content$23$content = _content$23.content) === null || _content$23$content === void 0 ? void 0 : (_content$23$content$t = _content$23$content.title) === null || _content$23$content$t === void 0 ? void 0 : _content$23$content$t[language]\n        }, {\n          input: \"richtext\",\n          label: \"Description 1\",\n          updateType: \"description\",\n          value: content === null || content === void 0 ? void 0 : (_content$24 = content['8']) === null || _content$24 === void 0 ? void 0 : (_content$24$content = _content$24.content) === null || _content$24$content === void 0 ? void 0 : (_content$24$content$d = _content$24$content.description) === null || _content$24$content$d === void 0 ? void 0 : _content$24$content$d[language]\n        }, {\n          input: \"input\",\n          label: \"Button Text\",\n          updateType: \"button\",\n          value: content === null || content === void 0 ? void 0 : (_content$25 = content['8']) === null || _content$25 === void 0 ? void 0 : (_content$25$content = _content$25.content) === null || _content$25$content === void 0 ? void 0 : (_content$25$content$b = _content$25$content.button) === null || _content$25$content$b === void 0 ? void 0 : (_content$25$content$b2 = _content$25$content$b[0]) === null || _content$25$content$b2 === void 0 ? void 0 : (_content$25$content$b3 = _content$25$content$b2.text) === null || _content$25$content$b3 === void 0 ? void 0 : _content$25$content$b3[language],\n          index: 0\n        }],\n        section: \"newProjectSection\",\n        language: language,\n        currentContent: content,\n        sectionIndex: indexes === null || indexes === void 0 ? void 0 : indexes['8'],\n        resourceId: currentId,\n        outOfEditing: outOfEditing\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 13\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 9\n    }, this)\n  );\n};\n_s(HomeManager, \"erXGwZ9I9AKiSHmJIwMEtcQIjnY=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = HomeManager;\nexport default HomeManager;\nvar _c;\n$RefreshReg$(_c, \"HomeManager\");", "map": {"version": 3, "names": ["useEffect", "useState", "FileUploader", "ContentSection", "MultiSelect", "update<PERSON>ain<PERSON><PERSON>nt", "getResources", "createContent", "useDispatch", "useSelector", "jsxDEV", "_jsxDEV", "HomeManager", "language", "currentPath", "outOfEditing", "_s", "_content$", "_content$$content", "_content$$content$tit", "_content$2", "_content$2$content", "_content$2$content$de", "_content$3", "_content$3$content", "_content$3$content$bu", "_content$3$content$bu2", "_content$3$content$bu3", "_content$4", "_content$4$content", "_content$4$content$im", "_content$4$content$im2", "_content$5", "_content$5$content", "_content$5$content$ti", "_content$6", "_content$6$content", "_content$6$content$de", "_content$7", "_content$7$content", "_content$7$content$bu", "_content$7$content$bu2", "_content$7$content$bu3", "_content$8", "_content$8$content", "_content$8$content$im", "_content$8$content$im2", "_content$9", "_content$10", "_content$10$content", "_content$10$content$t", "_content$11", "_content$11$content", "_content$11$content$d", "_content$12", "_content$12$content", "_content$12$content$b", "_content$12$content$b2", "_content$12$content$b3", "_content$16", "_content$16$sections", "_content$18", "_content$18$content", "_content$19", "_content$19$content", "_content$20", "_content$20$content", "_content$20$content$c", "_content$21", "_content$21$content", "_content$22", "_content$23", "_content$23$content", "_content$23$content$t", "_content$24", "_content$24$content", "_content$24$content$d", "_content$25", "_content$25$content", "_content$25$content$b", "_content$25$content$b2", "_content$25$content$b3", "currentId", "setCurrentId", "ServicesOptions", "setServicesOptions", "ProjectOptions", "setProjectOptions", "TestimonialsOptions", "setTestimonialsOptions", "currentC<PERSON>nt", "state", "homeContent", "present", "content", "indexes", "dispatch", "getOptionsforServices", "response", "resourceType", "resourceTag", "apiCallType", "response2", "response3", "fetchType", "message", "_response$resources", "_response$resources$r", "options", "resources", "map", "e", "i", "id", "order", "slug", "titleEn", "titleAr", "_response2$resources", "_response2$resources$", "ok", "_response3$resources", "_response3$resources$", "liveModeVersionData", "className", "children", "label", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Heading", "inputs", "input", "updateType", "value", "title", "max<PERSON><PERSON><PERSON>", "description", "button", "text", "index", "inputFiles", "url", "images", "section", "sectionIndex", "resourceId", "heading", "tabName", "items", "listOptions", "referenceOriginal", "dir", "isBorder", "fileId", "item", "array", "_content$13", "_content$13$content", "_content$13$content$c", "_content$13$content$c2", "_content$14", "_content$14$content", "_content$14$content$c", "_content$14$content$c2", "_content$14$content$c3", "_content$15", "_content$15$content", "_content$15$content$c", "_content$15$content$c2", "isLast", "length", "subHeading", "cards", "count", "directIcon", "icon", "subSection", "sections", "_section$title", "_section$title$en", "_section$content", "_section$content$titl", "_section$content2", "_section$content2$des", "_content$17", "_content$17$sections", "_content$17$sections$", "names", "en", "toUpperCase", "clientsImages", "allowExtraInput", "limitOptions", "min", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/shade_cms/dashboard/src/features/Resources/components/contentmanager/HomeManager.jsx"], "sourcesContent": ["// import { useSelector } from \"react-redux\";\r\nimport { useEffect, useState } from \"react\";\r\nimport FileUploader from \"../../../../components/Input/InputFileUploader\";\r\nimport ContentSection from \"../breakUI/ContentSections\";\r\nimport MultiSelect from \"../breakUI/MultiSelect\";\r\nimport { updateMainContent } from \"../../../common/homeContentSlice\";\r\n// import content from \"../websiteComponent/content.json\"\r\nimport { getResources } from \"../../../../app/fetch\";\r\nimport createContent from \"../../defineContent\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\n\r\nconst HomeManager = ({ language, currentPath, outOfEditing }) => {\r\n    // states\r\n    const [currentId, setCurrentId] = useState(\"\")\r\n    const [ServicesOptions, setServicesOptions] = useState([])\r\n    const [ProjectOptions, setProjectOptions] = useState([])\r\n    const [TestimonialsOptions, setTestimonialsOptions] = useState([])\r\n\r\n    const currentContent = useSelector((state) => state.homeContent.present)\r\n    const { content, indexes } = createContent(currentContent, \"edit\")\r\n    const dispatch = useDispatch()\r\n    // fucntions\r\n\r\n    useEffect(() => {\r\n        async function getOptionsforServices() {\r\n            const response = await getResources({ resourceType: \"SUB_PAGE\", resourceTag: \"SERVICE\", apiCallType: \"INTERNAL\" })\r\n            const response2 = await getResources({ resourceType: \"SUB_PAGE\", resourceTag: \"PROJECT\", apiCallType: \"INTERNAL\" })\r\n            const response3 = await getResources({ resourceType: \"SUB_PAGE\", resourceTag: \"TESTIMONIAL\", fetchType: \"CONTENT\", apiCallType: \"INTERNAL\" })\r\n            if (response.message === \"Success\") {\r\n                let options = response?.resources?.resources?.map((e, i) => ({\r\n                    id: e.id,\r\n                    order: i + 1,\r\n                    slug: e.slug,\r\n                    titleEn: e.titleEn,\r\n                    titleAr: e.titleAr,\r\n                    // icon: e.icon,\r\n                    // image: e.image\r\n                }))\r\n                setServicesOptions(options)\r\n            }\r\n            if (response2.message === \"Success\") {\r\n                let options = response2?.resources?.resources?.map((e, i) => ({\r\n                    id: e.id,\r\n                    order: i + 1,\r\n                    slug: e.slug,\r\n                    titleEn: e.titleEn,\r\n                    titleAr: e.titleAr,\r\n                    // icon: e.icon,\r\n                    // image: e.image\r\n                }))\r\n                setProjectOptions(options)\r\n            }\r\n\r\n            if (response3.ok) {\r\n                let options = response3?.resources?.resources?.map((e, i) => ({\r\n                    id: e.id,\r\n                    order: i + 1,\r\n                    slug: e.slug,\r\n                    titleEn: e.titleEn,\r\n                    titleAr: e.titleAr,\r\n                    // icon: e.icon,\r\n                    // image: e.image\r\n                    liveModeVersionData: e.liveModeVersionData\r\n                }))\r\n                setTestimonialsOptions(options)\r\n            }\r\n        }\r\n\r\n        getOptionsforServices()\r\n    }, [])\r\n\r\n    // useEffect(() => {\r\n    //     return () => dispatch(updateMainContent({ currentPath: \"content\", payload: undefined }))\r\n    // }, [])\r\n\r\n    return ( /// Component\r\n        <div className=\"w-full\">\r\n            {/* reference doc */}\r\n            <FileUploader id={\"homeReference\"} label={\"Rerference doc\"} fileName={\"Upload your file...\"} outOfEditing={outOfEditing} />\r\n            {/* homeBanner */}\r\n            <ContentSection\r\n                currentPath={currentPath}\r\n                Heading={\"Hero Banner\"}\r\n                inputs={[\r\n                    { input: \"input\", label: \"Heading/title\", updateType: \"title\", value: content?.[\"1\"]?.content?.title?.[language] },\r\n                    { input: \"textarea\", label: \"Description\", updateType: \"description\", maxLength: 500, value: content?.[\"1\"]?.content?.description?.[language] },\r\n                    { input: \"input\", label: \"Button Text\", updateType: \"button\", maxLength: 20, value: content?.[\"1\"]?.content?.button?.[0]?.text?.[language], index: 0 }]}\r\n                inputFiles={[{ label: \"Backround Image\", id: \"homeBanner\", order: 1, url: content?.['1']?.content?.images?.[0]?.url }]}\r\n                section={\"homeBanner\"}\r\n                language={language}\r\n                currentContent={content}\r\n                sectionIndex={indexes?.[\"1\"]}\r\n                resourceId={currentId}\r\n                outOfEditing={outOfEditing}\r\n            />\r\n\r\n            {/* about section */}\r\n            <ContentSection\r\n                currentPath={currentPath}\r\n                Heading={\"About Section\"}\r\n                inputs={[\r\n                    { input: \"input\", label: \"Heading/title\", updateType: \"title\", value: content?.['2']?.content?.title?.[language] },\r\n                    { input: \"richtext\", label: \"About section\", updateType: \"description\", maxLength: 800, value: content?.['2']?.content?.description?.[language] },\r\n                    { input: \"input\", label: \"Button Text\", updateType: \"button\", value: content?.['2']?.content?.button?.[0]?.text?.[language], index: 0 }]}\r\n                inputFiles={[{ label: \"Backround Image\", id: \"aboutUsSection\", order: 1, url: content?.[\"2\"]?.content?.images?.[0]?.url }]}\r\n                section={\"aboutUsSection\"}\r\n                language={language}\r\n                currentContent={content}\r\n                sectionIndex={indexes?.['2']}\r\n                resourceId={currentId}\r\n                outOfEditing={outOfEditing}\r\n            />\r\n\r\n\r\n            {/* services  */}\r\n            <MultiSelect\r\n                currentPath={currentPath}\r\n                section={\"serviceSection\"}\r\n                language={language}\r\n                label={\"Select Service List\"}\r\n                heading={\"Services Section\"}\r\n                tabName={\"Select Services\"}\r\n                options={content?.['3']?.items}\r\n                listOptions={ServicesOptions}\r\n                referenceOriginal={{ dir: \"home\", index: 0 }}\r\n                currentContent={content}\r\n                sectionIndex={indexes?.['3']}\r\n                outOfEditing={outOfEditing}\r\n            />\r\n\r\n            {/* exprerince */}\r\n            <div className=\"w-full\">\r\n                <ContentSection\r\n                    currentPath={currentPath}\r\n                    Heading={\"Experience Section\"}\r\n                    inputs={[\r\n                        { input: \"input\", label: \"Heading/title\", updateType: \"title\", value: content?.['4']?.content?.title?.[language] },\r\n                        { input: \"textarea\", label: \"Description\", updateType: \"description\", value: content?.['4']?.content?.description?.[language] },\r\n                        { input: \"input\", label: \"Button Text\", updateType: \"button\", value: content?.['4']?.content?.button?.[0]?.text?.[language], index: 0 }]}\r\n                    isBorder={false}\r\n                    fileId={\"experienceSection\"}\r\n                    section={\"experienceSection\"}\r\n                    language={language}\r\n                    currentContent={content}\r\n                    sectionIndex={indexes?.['4']}\r\n                    resourceId={currentId}\r\n                    outOfEditing={outOfEditing}\r\n                />\r\n                {[\"Item 1\", \"Item 2\", \"Item 3\", \"Item 4\"].map((item, index, array) => {\r\n                    const isLast = index === array.length - 1;\r\n                    return (\r\n                        <ContentSection key={item + index}\r\n                            currentPath={currentPath}\r\n                            subHeading={item}\r\n                            inputs={[\r\n                                { input: \"input\", label: \"Item text 1\", updateType: \"count\", value: content?.['4']?.content?.cards?.[index]?.count },\r\n                                { input: \"input\", label: \"Item text 2\", updateType: \"title\", value: content?.['4']?.content?.cards?.[index]?.title?.[language] }]}\r\n                            inputFiles={[{ label: \"Item Icon\", id: item, order: (index + 1), directIcon: true, url: content?.['4']?.content?.cards?.[index]?.icon }]}\r\n                            // fileId={item}\r\n                            language={language}\r\n                            section={\"experienceSection\"}\r\n                            subSection={\"cards\"}\r\n                            index={+index}\r\n                            isBorder={isLast}\r\n                            currentContent={content}\r\n                            sectionIndex={indexes?.['4']}\r\n                            resourceId={currentId}\r\n                            outOfEditing={outOfEditing}\r\n                        />\r\n                    )\r\n                })}\r\n            </div>\r\n\r\n            {/* project selection */}\r\n            <div className=\"w-full\">\r\n                <h3 className={`font-semibold text-[1.25rem] mb-4 mt-4`} >\r\n                    Project Section\r\n                </h3>\r\n                <div>\r\n                    {\r\n                        content?.['5']?.sections?.map((section, index, array) => {\r\n                            const names = { 0: \"Projects\", 1: \"Markets\", 2: \"Safety & Responsibility\" }\r\n                            const isLast = index === array.length - 1;\r\n                            return (\r\n                                <div key={index} className=\"mt-3 \">\r\n                                    <ContentSection\r\n                                        currentPath={currentPath}\r\n                                        subHeading={'section ' + (index + 1)}\r\n                                        inputs={[\r\n                                            { input: \"input\", label: (section?.title?.en)?.toUpperCase(), updateType: \"title\", value: section?.content?.title?.[language] },\r\n                                            { input: \"textarea\", label: \"Description\", updateType: \"description\", maxLength: 500, value: section?.content?.description?.[language] }\r\n                                        ]}\r\n                                        language={language}\r\n                                        section={\"recentProjectsSection\"}\r\n                                        subSection={\"sections\"}\r\n                                        index={+index}\r\n                                        isBorder={isLast}\r\n                                        currentContent={content}\r\n                                        sectionIndex={indexes?.['5']}\r\n                                        resourceId={currentId}\r\n                                        outOfEditing={outOfEditing}\r\n                                    />\r\n                                    <MultiSelect\r\n                                        currentPath={currentPath}\r\n                                        language={language}\r\n                                        label={`Select ${names[index]}`}\r\n                                        tabName={\"Select Projects\"}\r\n                                        options={content?.['5']?.sections?.[index]?.items}\r\n                                        referenceOriginal={{ dir: \"recentproject\", index }}\r\n                                        currentContent={content}\r\n                                        sectionIndex={indexes?.['5']}\r\n                                        listOptions={ProjectOptions}\r\n                                        outOfEditing={outOfEditing}\r\n                                    />\r\n                                </div>\r\n                            )\r\n                        })}\r\n                </div>\r\n            </div>\r\n\r\n            {/* client section */}\r\n            <ContentSection\r\n                currentPath={currentPath}\r\n                Heading={\"Client Section\"}\r\n                inputs={[\r\n                    { input: \"input\", label: \"Heading/title\", updateType: \"title\", value: content?.['6']?.content?.title[language] },\r\n                    { input: \"input\", label: \"Description\", updateType: \"description\", value: content?.['6']?.content?.description[language] },\r\n                ]}\r\n                inputFiles={content?.['6']?.content?.clientsImages?.map((e, i) => ({ label: \"Client \" + (i + 1), id: e.order, order: e.order, url: e.url }))}\r\n                section={\"clientsImages\"}\r\n                language={language}\r\n                currentContent={content}\r\n                sectionIndex={indexes?.['6']}\r\n                allowExtraInput={true}\r\n                resourceId={currentId}\r\n                outOfEditing={outOfEditing}\r\n            />\r\n\r\n            {/* testimonials */}\r\n            <ContentSection\r\n                currentPath={currentPath}\r\n                Heading={\"Testimonials\"}\r\n                inputs={[\r\n                    { input: \"input\", label: \"Heading/title\", maxLength: 55, updateType: \"title\", value: content?.['7']?.content?.title[language] },\r\n                ]}\r\n                section={\"Testimonials heading\"}\r\n                language={language}\r\n                currentContent={content}\r\n                sectionIndex={indexes?.['7']}\r\n                resourceId={currentId}\r\n                outOfEditing={outOfEditing}\r\n            />\r\n            <MultiSelect\r\n                currentPath={currentPath}\r\n                section={\"testimonialsSections\"}\r\n                language={language}\r\n                label={\"Select Testimony List\"}\r\n                heading={\"Testimonials Section\"}\r\n                tabName={\"Select Testimonies\"}\r\n                options={content?.['7']?.items}\r\n                listOptions={TestimonialsOptions}\r\n                referenceOriginal={{ dir: \"testimonials\", index: 0 }}\r\n                currentContent={content}\r\n                sectionIndex={indexes?.['7']}\r\n                limitOptions={4}\r\n                min={4}\r\n                outOfEditing={outOfEditing}\r\n            />\r\n\r\n\r\n            {/* New Project */}\r\n            <ContentSection\r\n                currentPath={currentPath}\r\n                Heading={\"New Project\"}\r\n                inputs={[\r\n                    { input: \"input\", label: \"Heading/title\", maxLength: 55, updateType: \"title\", value: content?.['8']?.content?.title?.[language] },\r\n                    { input: \"richtext\", label: \"Description 1\", updateType: \"description\", value: content?.['8']?.content?.description?.[language] },\r\n                    { input: \"input\", label: \"Button Text\", updateType: \"button\", value: content?.['8']?.content?.button?.[0]?.text?.[language], index: 0 },\r\n                ]}\r\n                section={\"newProjectSection\"}\r\n                language={language}\r\n                currentContent={content}\r\n                sectionIndex={indexes?.['8']}\r\n                resourceId={currentId}\r\n                outOfEditing={outOfEditing}\r\n            />\r\n\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default HomeManager"], "mappings": ";;AAAA;AACA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,YAAY,MAAM,gDAAgD;AACzE,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAASC,iBAAiB,QAAQ,kCAAkC;AACpE;AACA,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,WAAW,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,WAAW;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,SAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,UAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,WAAA,EAAAC,oBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,WAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAC7D;EACA,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlF,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACmF,eAAe,EAAEC,kBAAkB,CAAC,GAAGpF,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACqF,cAAc,EAAEC,iBAAiB,CAAC,GAAGtF,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACuF,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGxF,QAAQ,CAAC,EAAE,CAAC;EAElE,MAAMyF,cAAc,GAAGjF,WAAW,CAAEkF,KAAK,IAAKA,KAAK,CAACC,WAAW,CAACC,OAAO,CAAC;EACxE,MAAM;IAAEC,OAAO;IAAEC;EAAQ,CAAC,GAAGxF,aAAa,CAACmF,cAAc,EAAE,MAAM,CAAC;EAClE,MAAMM,QAAQ,GAAGxF,WAAW,CAAC,CAAC;EAC9B;;EAEAR,SAAS,CAAC,MAAM;IACZ,eAAeiG,qBAAqBA,CAAA,EAAG;MACnC,MAAMC,QAAQ,GAAG,MAAM5F,YAAY,CAAC;QAAE6F,YAAY,EAAE,UAAU;QAAEC,WAAW,EAAE,SAAS;QAAEC,WAAW,EAAE;MAAW,CAAC,CAAC;MAClH,MAAMC,SAAS,GAAG,MAAMhG,YAAY,CAAC;QAAE6F,YAAY,EAAE,UAAU;QAAEC,WAAW,EAAE,SAAS;QAAEC,WAAW,EAAE;MAAW,CAAC,CAAC;MACnH,MAAME,SAAS,GAAG,MAAMjG,YAAY,CAAC;QAAE6F,YAAY,EAAE,UAAU;QAAEC,WAAW,EAAE,aAAa;QAAEI,SAAS,EAAE,SAAS;QAAEH,WAAW,EAAE;MAAW,CAAC,CAAC;MAC7I,IAAIH,QAAQ,CAACO,OAAO,KAAK,SAAS,EAAE;QAAA,IAAAC,mBAAA,EAAAC,qBAAA;QAChC,IAAIC,OAAO,GAAGV,QAAQ,aAARA,QAAQ,wBAAAQ,mBAAA,GAARR,QAAQ,CAAEW,SAAS,cAAAH,mBAAA,wBAAAC,qBAAA,GAAnBD,mBAAA,CAAqBG,SAAS,cAAAF,qBAAA,uBAA9BA,qBAAA,CAAgCG,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,MAAM;UACzDC,EAAE,EAAEF,CAAC,CAACE,EAAE;UACRC,KAAK,EAAEF,CAAC,GAAG,CAAC;UACZG,IAAI,EAAEJ,CAAC,CAACI,IAAI;UACZC,OAAO,EAAEL,CAAC,CAACK,OAAO;UAClBC,OAAO,EAAEN,CAAC,CAACM;UACX;UACA;QACJ,CAAC,CAAC,CAAC;QACHhC,kBAAkB,CAACuB,OAAO,CAAC;MAC/B;MACA,IAAIN,SAAS,CAACG,OAAO,KAAK,SAAS,EAAE;QAAA,IAAAa,oBAAA,EAAAC,qBAAA;QACjC,IAAIX,OAAO,GAAGN,SAAS,aAATA,SAAS,wBAAAgB,oBAAA,GAAThB,SAAS,CAAEO,SAAS,cAAAS,oBAAA,wBAAAC,qBAAA,GAApBD,oBAAA,CAAsBT,SAAS,cAAAU,qBAAA,uBAA/BA,qBAAA,CAAiCT,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,MAAM;UAC1DC,EAAE,EAAEF,CAAC,CAACE,EAAE;UACRC,KAAK,EAAEF,CAAC,GAAG,CAAC;UACZG,IAAI,EAAEJ,CAAC,CAACI,IAAI;UACZC,OAAO,EAAEL,CAAC,CAACK,OAAO;UAClBC,OAAO,EAAEN,CAAC,CAACM;UACX;UACA;QACJ,CAAC,CAAC,CAAC;QACH9B,iBAAiB,CAACqB,OAAO,CAAC;MAC9B;MAEA,IAAIL,SAAS,CAACiB,EAAE,EAAE;QAAA,IAAAC,oBAAA,EAAAC,qBAAA;QACd,IAAId,OAAO,GAAGL,SAAS,aAATA,SAAS,wBAAAkB,oBAAA,GAATlB,SAAS,CAAEM,SAAS,cAAAY,oBAAA,wBAAAC,qBAAA,GAApBD,oBAAA,CAAsBZ,SAAS,cAAAa,qBAAA,uBAA/BA,qBAAA,CAAiCZ,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,MAAM;UAC1DC,EAAE,EAAEF,CAAC,CAACE,EAAE;UACRC,KAAK,EAAEF,CAAC,GAAG,CAAC;UACZG,IAAI,EAAEJ,CAAC,CAACI,IAAI;UACZC,OAAO,EAAEL,CAAC,CAACK,OAAO;UAClBC,OAAO,EAAEN,CAAC,CAACM,OAAO;UAClB;UACA;UACAM,mBAAmB,EAAEZ,CAAC,CAACY;QAC3B,CAAC,CAAC,CAAC;QACHlC,sBAAsB,CAACmB,OAAO,CAAC;MACnC;IACJ;IAEAX,qBAAqB,CAAC,CAAC;EAC3B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA;EACA;;EAEA;IAAA;IAAS;IACLtF,OAAA;MAAKiH,SAAS,EAAC,QAAQ;MAAAC,QAAA,gBAEnBlH,OAAA,CAACT,YAAY;QAAC+G,EAAE,EAAE,eAAgB;QAACa,KAAK,EAAE,gBAAiB;QAACC,QAAQ,EAAE,qBAAsB;QAAChH,YAAY,EAAEA;MAAa;QAAAgH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE3HvH,OAAA,CAACR,cAAc;QACXW,WAAW,EAAEA,WAAY;QACzBqH,OAAO,EAAE,aAAc;QACvBC,MAAM,EAAE,CACJ;UAAEC,KAAK,EAAE,OAAO;UAAEP,KAAK,EAAE,eAAe;UAAEQ,UAAU,EAAE,OAAO;UAAEC,KAAK,EAAEzC,OAAO,aAAPA,OAAO,wBAAA7E,SAAA,GAAP6E,OAAO,CAAG,GAAG,CAAC,cAAA7E,SAAA,wBAAAC,iBAAA,GAAdD,SAAA,CAAgB6E,OAAO,cAAA5E,iBAAA,wBAAAC,qBAAA,GAAvBD,iBAAA,CAAyBsH,KAAK,cAAArH,qBAAA,uBAA9BA,qBAAA,CAAiCN,QAAQ;QAAE,CAAC,EAClH;UAAEwH,KAAK,EAAE,UAAU;UAAEP,KAAK,EAAE,aAAa;UAAEQ,UAAU,EAAE,aAAa;UAAEG,SAAS,EAAE,GAAG;UAAEF,KAAK,EAAEzC,OAAO,aAAPA,OAAO,wBAAA1E,UAAA,GAAP0E,OAAO,CAAG,GAAG,CAAC,cAAA1E,UAAA,wBAAAC,kBAAA,GAAdD,UAAA,CAAgB0E,OAAO,cAAAzE,kBAAA,wBAAAC,qBAAA,GAAvBD,kBAAA,CAAyBqH,WAAW,cAAApH,qBAAA,uBAApCA,qBAAA,CAAuCT,QAAQ;QAAE,CAAC,EAC/I;UAAEwH,KAAK,EAAE,OAAO;UAAEP,KAAK,EAAE,aAAa;UAAEQ,UAAU,EAAE,QAAQ;UAAEG,SAAS,EAAE,EAAE;UAAEF,KAAK,EAAEzC,OAAO,aAAPA,OAAO,wBAAAvE,UAAA,GAAPuE,OAAO,CAAG,GAAG,CAAC,cAAAvE,UAAA,wBAAAC,kBAAA,GAAdD,UAAA,CAAgBuE,OAAO,cAAAtE,kBAAA,wBAAAC,qBAAA,GAAvBD,kBAAA,CAAyBmH,MAAM,cAAAlH,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAApCD,sBAAA,CAAsCkH,IAAI,cAAAjH,sBAAA,uBAA1CA,sBAAA,CAA6Cd,QAAQ,CAAC;UAAEgI,KAAK,EAAE;QAAE,CAAC,CAAE;QAC5JC,UAAU,EAAE,CAAC;UAAEhB,KAAK,EAAE,iBAAiB;UAAEb,EAAE,EAAE,YAAY;UAAEC,KAAK,EAAE,CAAC;UAAE6B,GAAG,EAAEjD,OAAO,aAAPA,OAAO,wBAAAlE,UAAA,GAAPkE,OAAO,CAAG,GAAG,CAAC,cAAAlE,UAAA,wBAAAC,kBAAA,GAAdD,UAAA,CAAgBkE,OAAO,cAAAjE,kBAAA,wBAAAC,qBAAA,GAAvBD,kBAAA,CAAyBmH,MAAM,cAAAlH,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,uBAApCA,sBAAA,CAAsCgH;QAAI,CAAC,CAAE;QACvHE,OAAO,EAAE,YAAa;QACtBpI,QAAQ,EAAEA,QAAS;QACnB6E,cAAc,EAAEI,OAAQ;QACxBoD,YAAY,EAAEnD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,GAAG,CAAE;QAC7BoD,UAAU,EAAEjE,SAAU;QACtBnE,YAAY,EAAEA;MAAa;QAAAgH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eAGFvH,OAAA,CAACR,cAAc;QACXW,WAAW,EAAEA,WAAY;QACzBqH,OAAO,EAAE,eAAgB;QACzBC,MAAM,EAAE,CACJ;UAAEC,KAAK,EAAE,OAAO;UAAEP,KAAK,EAAE,eAAe;UAAEQ,UAAU,EAAE,OAAO;UAAEC,KAAK,EAAEzC,OAAO,aAAPA,OAAO,wBAAA9D,UAAA,GAAP8D,OAAO,CAAG,GAAG,CAAC,cAAA9D,UAAA,wBAAAC,kBAAA,GAAdD,UAAA,CAAgB8D,OAAO,cAAA7D,kBAAA,wBAAAC,qBAAA,GAAvBD,kBAAA,CAAyBuG,KAAK,cAAAtG,qBAAA,uBAA9BA,qBAAA,CAAiCrB,QAAQ;QAAE,CAAC,EAClH;UAAEwH,KAAK,EAAE,UAAU;UAAEP,KAAK,EAAE,eAAe;UAAEQ,UAAU,EAAE,aAAa;UAAEG,SAAS,EAAE,GAAG;UAAEF,KAAK,EAAEzC,OAAO,aAAPA,OAAO,wBAAA3D,UAAA,GAAP2D,OAAO,CAAG,GAAG,CAAC,cAAA3D,UAAA,wBAAAC,kBAAA,GAAdD,UAAA,CAAgB2D,OAAO,cAAA1D,kBAAA,wBAAAC,qBAAA,GAAvBD,kBAAA,CAAyBsG,WAAW,cAAArG,qBAAA,uBAApCA,qBAAA,CAAuCxB,QAAQ;QAAE,CAAC,EACjJ;UAAEwH,KAAK,EAAE,OAAO;UAAEP,KAAK,EAAE,aAAa;UAAEQ,UAAU,EAAE,QAAQ;UAAEC,KAAK,EAAEzC,OAAO,aAAPA,OAAO,wBAAAxD,UAAA,GAAPwD,OAAO,CAAG,GAAG,CAAC,cAAAxD,UAAA,wBAAAC,kBAAA,GAAdD,UAAA,CAAgBwD,OAAO,cAAAvD,kBAAA,wBAAAC,qBAAA,GAAvBD,kBAAA,CAAyBoG,MAAM,cAAAnG,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAApCD,sBAAA,CAAsCmG,IAAI,cAAAlG,sBAAA,uBAA1CA,sBAAA,CAA6C7B,QAAQ,CAAC;UAAEgI,KAAK,EAAE;QAAE,CAAC,CAAE;QAC7IC,UAAU,EAAE,CAAC;UAAEhB,KAAK,EAAE,iBAAiB;UAAEb,EAAE,EAAE,gBAAgB;UAAEC,KAAK,EAAE,CAAC;UAAE6B,GAAG,EAAEjD,OAAO,aAAPA,OAAO,wBAAAnD,UAAA,GAAPmD,OAAO,CAAG,GAAG,CAAC,cAAAnD,UAAA,wBAAAC,kBAAA,GAAdD,UAAA,CAAgBmD,OAAO,cAAAlD,kBAAA,wBAAAC,qBAAA,GAAvBD,kBAAA,CAAyBoG,MAAM,cAAAnG,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,uBAApCA,sBAAA,CAAsCiG;QAAI,CAAC,CAAE;QAC3HE,OAAO,EAAE,gBAAiB;QAC1BpI,QAAQ,EAAEA,QAAS;QACnB6E,cAAc,EAAEI,OAAQ;QACxBoD,YAAY,EAAEnD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,GAAG,CAAE;QAC7BoD,UAAU,EAAEjE,SAAU;QACtBnE,YAAY,EAAEA;MAAa;QAAAgH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eAIFvH,OAAA,CAACP,WAAW;QACRU,WAAW,EAAEA,WAAY;QACzBmI,OAAO,EAAE,gBAAiB;QAC1BpI,QAAQ,EAAEA,QAAS;QACnBiH,KAAK,EAAE,qBAAsB;QAC7BsB,OAAO,EAAE,kBAAmB;QAC5BC,OAAO,EAAE,iBAAkB;QAC3BzC,OAAO,EAAEd,OAAO,aAAPA,OAAO,wBAAA/C,UAAA,GAAP+C,OAAO,CAAG,GAAG,CAAC,cAAA/C,UAAA,uBAAdA,UAAA,CAAgBuG,KAAM;QAC/BC,WAAW,EAAEnE,eAAgB;QAC7BoE,iBAAiB,EAAE;UAAEC,GAAG,EAAE,MAAM;UAAEZ,KAAK,EAAE;QAAE,CAAE;QAC7CnD,cAAc,EAAEI,OAAQ;QACxBoD,YAAY,EAAEnD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,GAAG,CAAE;QAC7BhF,YAAY,EAAEA;MAAa;QAAAgH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eAGFvH,OAAA;QAAKiH,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACnBlH,OAAA,CAACR,cAAc;UACXW,WAAW,EAAEA,WAAY;UACzBqH,OAAO,EAAE,oBAAqB;UAC9BC,MAAM,EAAE,CACJ;YAAEC,KAAK,EAAE,OAAO;YAAEP,KAAK,EAAE,eAAe;YAAEQ,UAAU,EAAE,OAAO;YAAEC,KAAK,EAAEzC,OAAO,aAAPA,OAAO,wBAAA9C,WAAA,GAAP8C,OAAO,CAAG,GAAG,CAAC,cAAA9C,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgB8C,OAAO,cAAA7C,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBuF,KAAK,cAAAtF,qBAAA,uBAA9BA,qBAAA,CAAiCrC,QAAQ;UAAE,CAAC,EAClH;YAAEwH,KAAK,EAAE,UAAU;YAAEP,KAAK,EAAE,aAAa;YAAEQ,UAAU,EAAE,aAAa;YAAEC,KAAK,EAAEzC,OAAO,aAAPA,OAAO,wBAAA3C,WAAA,GAAP2C,OAAO,CAAG,GAAG,CAAC,cAAA3C,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgB2C,OAAO,cAAA1C,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBsF,WAAW,cAAArF,qBAAA,uBAApCA,qBAAA,CAAuCxC,QAAQ;UAAE,CAAC,EAC/H;YAAEwH,KAAK,EAAE,OAAO;YAAEP,KAAK,EAAE,aAAa;YAAEQ,UAAU,EAAE,QAAQ;YAAEC,KAAK,EAAEzC,OAAO,aAAPA,OAAO,wBAAAxC,WAAA,GAAPwC,OAAO,CAAG,GAAG,CAAC,cAAAxC,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBwC,OAAO,cAAAvC,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBoF,MAAM,cAAAnF,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAApCD,sBAAA,CAAsCmF,IAAI,cAAAlF,sBAAA,uBAA1CA,sBAAA,CAA6C7C,QAAQ,CAAC;YAAEgI,KAAK,EAAE;UAAE,CAAC,CAAE;UAC7Ia,QAAQ,EAAE,KAAM;UAChBC,MAAM,EAAE,mBAAoB;UAC5BV,OAAO,EAAE,mBAAoB;UAC7BpI,QAAQ,EAAEA,QAAS;UACnB6E,cAAc,EAAEI,OAAQ;UACxBoD,YAAY,EAAEnD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,GAAG,CAAE;UAC7BoD,UAAU,EAAEjE,SAAU;UACtBnE,YAAY,EAAEA;QAAa;UAAAgH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,EACD,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAACpB,GAAG,CAAC,CAAC8C,IAAI,EAAEf,KAAK,EAAEgB,KAAK,KAAK;UAAA,IAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,WAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA;UAClE,MAAMC,MAAM,GAAG9B,KAAK,KAAKgB,KAAK,CAACe,MAAM,GAAG,CAAC;UACzC,oBACIjK,OAAA,CAACR,cAAc;YACXW,WAAW,EAAEA,WAAY;YACzB+J,UAAU,EAAEjB,IAAK;YACjBxB,MAAM,EAAE,CACJ;cAAEC,KAAK,EAAE,OAAO;cAAEP,KAAK,EAAE,aAAa;cAAEQ,UAAU,EAAE,OAAO;cAAEC,KAAK,EAAEzC,OAAO,aAAPA,OAAO,wBAAAgE,WAAA,GAAPhE,OAAO,CAAG,GAAG,CAAC,cAAAgE,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBhE,OAAO,cAAAiE,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBe,KAAK,cAAAd,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAiCnB,KAAK,CAAC,cAAAoB,sBAAA,uBAAvCA,sBAAA,CAAyCc;YAAM,CAAC,EACpH;cAAE1C,KAAK,EAAE,OAAO;cAAEP,KAAK,EAAE,aAAa;cAAEQ,UAAU,EAAE,OAAO;cAAEC,KAAK,EAAEzC,OAAO,aAAPA,OAAO,wBAAAoE,WAAA,GAAPpE,OAAO,CAAG,GAAG,CAAC,cAAAoE,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBpE,OAAO,cAAAqE,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBW,KAAK,cAAAV,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAiCvB,KAAK,CAAC,cAAAwB,sBAAA,wBAAAC,sBAAA,GAAvCD,sBAAA,CAAyC7B,KAAK,cAAA8B,sBAAA,uBAA9CA,sBAAA,CAAiDzJ,QAAQ;YAAE,CAAC,CAAE;YACtIiI,UAAU,EAAE,CAAC;cAAEhB,KAAK,EAAE,WAAW;cAAEb,EAAE,EAAE2C,IAAI;cAAE1C,KAAK,EAAG2B,KAAK,GAAG,CAAE;cAAEmC,UAAU,EAAE,IAAI;cAAEjC,GAAG,EAAEjD,OAAO,aAAPA,OAAO,wBAAAyE,WAAA,GAAPzE,OAAO,CAAG,GAAG,CAAC,cAAAyE,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBzE,OAAO,cAAA0E,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBM,KAAK,cAAAL,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAiC5B,KAAK,CAAC,cAAA6B,sBAAA,uBAAvCA,sBAAA,CAAyCO;YAAK,CAAC;YACvI;YAAA;YACApK,QAAQ,EAAEA,QAAS;YACnBoI,OAAO,EAAE,mBAAoB;YAC7BiC,UAAU,EAAE,OAAQ;YACpBrC,KAAK,EAAE,CAACA,KAAM;YACda,QAAQ,EAAEiB,MAAO;YACjBjF,cAAc,EAAEI,OAAQ;YACxBoD,YAAY,EAAEnD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,GAAG,CAAE;YAC7BoD,UAAU,EAAEjE,SAAU;YACtBnE,YAAY,EAAEA;UAAa,GAhBV6I,IAAI,GAAGf,KAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiBhC,CAAC;QAEV,CAAC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNvH,OAAA;QAAKiH,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACnBlH,OAAA;UAAIiH,SAAS,EAAE,wCAAyC;UAAAC,QAAA,EAAE;QAE1D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLvH,OAAA;UAAAkH,QAAA,EAEQ/B,OAAO,aAAPA,OAAO,wBAAAnC,WAAA,GAAPmC,OAAO,CAAG,GAAG,CAAC,cAAAnC,WAAA,wBAAAC,oBAAA,GAAdD,WAAA,CAAgBwH,QAAQ,cAAAvH,oBAAA,uBAAxBA,oBAAA,CAA0BkD,GAAG,CAAC,CAACmC,OAAO,EAAEJ,KAAK,EAAEgB,KAAK,KAAK;YAAA,IAAAuB,cAAA,EAAAC,iBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,WAAA,EAAAC,oBAAA,EAAAC,qBAAA;YACrD,MAAMC,KAAK,GAAG;cAAE,CAAC,EAAE,UAAU;cAAE,CAAC,EAAE,SAAS;cAAE,CAAC,EAAE;YAA0B,CAAC;YAC3E,MAAMlB,MAAM,GAAG9B,KAAK,KAAKgB,KAAK,CAACe,MAAM,GAAG,CAAC;YACzC,oBACIjK,OAAA;cAAiBiH,SAAS,EAAC,OAAO;cAAAC,QAAA,gBAC9BlH,OAAA,CAACR,cAAc;gBACXW,WAAW,EAAEA,WAAY;gBACzB+J,UAAU,EAAE,UAAU,IAAIhC,KAAK,GAAG,CAAC,CAAE;gBACrCT,MAAM,EAAE,CACJ;kBAAEC,KAAK,EAAE,OAAO;kBAAEP,KAAK,EAAGmB,OAAO,aAAPA,OAAO,wBAAAmC,cAAA,GAAPnC,OAAO,CAAET,KAAK,cAAA4C,cAAA,wBAAAC,iBAAA,GAAdD,cAAA,CAAgBU,EAAE,cAAAT,iBAAA,uBAAnBA,iBAAA,CAAsBU,WAAW,CAAC,CAAC;kBAAEzD,UAAU,EAAE,OAAO;kBAAEC,KAAK,EAAEU,OAAO,aAAPA,OAAO,wBAAAqC,gBAAA,GAAPrC,OAAO,CAAEnD,OAAO,cAAAwF,gBAAA,wBAAAC,qBAAA,GAAhBD,gBAAA,CAAkB9C,KAAK,cAAA+C,qBAAA,uBAAvBA,qBAAA,CAA0B1K,QAAQ;gBAAE,CAAC,EAC/H;kBAAEwH,KAAK,EAAE,UAAU;kBAAEP,KAAK,EAAE,aAAa;kBAAEQ,UAAU,EAAE,aAAa;kBAAEG,SAAS,EAAE,GAAG;kBAAEF,KAAK,EAAEU,OAAO,aAAPA,OAAO,wBAAAuC,iBAAA,GAAPvC,OAAO,CAAEnD,OAAO,cAAA0F,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkB9C,WAAW,cAAA+C,qBAAA,uBAA7BA,qBAAA,CAAgC5K,QAAQ;gBAAE,CAAC,CAC1I;gBACFA,QAAQ,EAAEA,QAAS;gBACnBoI,OAAO,EAAE,uBAAwB;gBACjCiC,UAAU,EAAE,UAAW;gBACvBrC,KAAK,EAAE,CAACA,KAAM;gBACda,QAAQ,EAAEiB,MAAO;gBACjBjF,cAAc,EAAEI,OAAQ;gBACxBoD,YAAY,EAAEnD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,GAAG,CAAE;gBAC7BoD,UAAU,EAAEjE,SAAU;gBACtBnE,YAAY,EAAEA;cAAa;gBAAAgH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,eACFvH,OAAA,CAACP,WAAW;gBACRU,WAAW,EAAEA,WAAY;gBACzBD,QAAQ,EAAEA,QAAS;gBACnBiH,KAAK,EAAE,UAAU+D,KAAK,CAAChD,KAAK,CAAC,EAAG;gBAChCQ,OAAO,EAAE,iBAAkB;gBAC3BzC,OAAO,EAAEd,OAAO,aAAPA,OAAO,wBAAA4F,WAAA,GAAP5F,OAAO,CAAG,GAAG,CAAC,cAAA4F,WAAA,wBAAAC,oBAAA,GAAdD,WAAA,CAAgBP,QAAQ,cAAAQ,oBAAA,wBAAAC,qBAAA,GAAxBD,oBAAA,CAA2B9C,KAAK,CAAC,cAAA+C,qBAAA,uBAAjCA,qBAAA,CAAmCtC,KAAM;gBAClDE,iBAAiB,EAAE;kBAAEC,GAAG,EAAE,eAAe;kBAAEZ;gBAAM,CAAE;gBACnDnD,cAAc,EAAEI,OAAQ;gBACxBoD,YAAY,EAAEnD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,GAAG,CAAE;gBAC7BwD,WAAW,EAAEjE,cAAe;gBAC5BvE,YAAY,EAAEA;cAAa;gBAAAgH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC;YAAA,GA7BIW,KAAK;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA8BV,CAAC;UAEd,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAGNvH,OAAA,CAACR,cAAc;QACXW,WAAW,EAAEA,WAAY;QACzBqH,OAAO,EAAE,gBAAiB;QAC1BC,MAAM,EAAE,CACJ;UAAEC,KAAK,EAAE,OAAO;UAAEP,KAAK,EAAE,eAAe;UAAEQ,UAAU,EAAE,OAAO;UAAEC,KAAK,EAAEzC,OAAO,aAAPA,OAAO,wBAAAjC,WAAA,GAAPiC,OAAO,CAAG,GAAG,CAAC,cAAAjC,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBiC,OAAO,cAAAhC,mBAAA,uBAAvBA,mBAAA,CAAyB0E,KAAK,CAAC3H,QAAQ;QAAE,CAAC,EAChH;UAAEwH,KAAK,EAAE,OAAO;UAAEP,KAAK,EAAE,aAAa;UAAEQ,UAAU,EAAE,aAAa;UAAEC,KAAK,EAAEzC,OAAO,aAAPA,OAAO,wBAAA/B,WAAA,GAAP+B,OAAO,CAAG,GAAG,CAAC,cAAA/B,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgB+B,OAAO,cAAA9B,mBAAA,uBAAvBA,mBAAA,CAAyB0E,WAAW,CAAC7H,QAAQ;QAAE,CAAC,CAC5H;QACFiI,UAAU,EAAEhD,OAAO,aAAPA,OAAO,wBAAA7B,WAAA,GAAP6B,OAAO,CAAG,GAAG,CAAC,cAAA7B,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgB6B,OAAO,cAAA5B,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyB8H,aAAa,cAAA7H,qBAAA,uBAAtCA,qBAAA,CAAwC2C,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,MAAM;UAAEc,KAAK,EAAE,SAAS,IAAId,CAAC,GAAG,CAAC,CAAC;UAAEC,EAAE,EAAEF,CAAC,CAACG,KAAK;UAAEA,KAAK,EAAEH,CAAC,CAACG,KAAK;UAAE6B,GAAG,EAAEhC,CAAC,CAACgC;QAAI,CAAC,CAAC,CAAE;QAC7IE,OAAO,EAAE,eAAgB;QACzBpI,QAAQ,EAAEA,QAAS;QACnB6E,cAAc,EAAEI,OAAQ;QACxBoD,YAAY,EAAEnD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,GAAG,CAAE;QAC7BkG,eAAe,EAAE,IAAK;QACtB9C,UAAU,EAAEjE,SAAU;QACtBnE,YAAY,EAAEA;MAAa;QAAAgH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eAGFvH,OAAA,CAACR,cAAc;QACXW,WAAW,EAAEA,WAAY;QACzBqH,OAAO,EAAE,cAAe;QACxBC,MAAM,EAAE,CACJ;UAAEC,KAAK,EAAE,OAAO;UAAEP,KAAK,EAAE,eAAe;UAAEW,SAAS,EAAE,EAAE;UAAEH,UAAU,EAAE,OAAO;UAAEC,KAAK,EAAEzC,OAAO,aAAPA,OAAO,wBAAA1B,WAAA,GAAP0B,OAAO,CAAG,GAAG,CAAC,cAAA1B,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgB0B,OAAO,cAAAzB,mBAAA,uBAAvBA,mBAAA,CAAyBmE,KAAK,CAAC3H,QAAQ;QAAE,CAAC,CACjI;QACFoI,OAAO,EAAE,sBAAuB;QAChCpI,QAAQ,EAAEA,QAAS;QACnB6E,cAAc,EAAEI,OAAQ;QACxBoD,YAAY,EAAEnD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,GAAG,CAAE;QAC7BoD,UAAU,EAAEjE,SAAU;QACtBnE,YAAY,EAAEA;MAAa;QAAAgH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACFvH,OAAA,CAACP,WAAW;QACRU,WAAW,EAAEA,WAAY;QACzBmI,OAAO,EAAE,sBAAuB;QAChCpI,QAAQ,EAAEA,QAAS;QACnBiH,KAAK,EAAE,uBAAwB;QAC/BsB,OAAO,EAAE,sBAAuB;QAChCC,OAAO,EAAE,oBAAqB;QAC9BzC,OAAO,EAAEd,OAAO,aAAPA,OAAO,wBAAAxB,WAAA,GAAPwB,OAAO,CAAG,GAAG,CAAC,cAAAxB,WAAA,uBAAdA,WAAA,CAAgBgF,KAAM;QAC/BC,WAAW,EAAE/D,mBAAoB;QACjCgE,iBAAiB,EAAE;UAAEC,GAAG,EAAE,cAAc;UAAEZ,KAAK,EAAE;QAAE,CAAE;QACrDnD,cAAc,EAAEI,OAAQ;QACxBoD,YAAY,EAAEnD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,GAAG,CAAE;QAC7BmG,YAAY,EAAE,CAAE;QAChBC,GAAG,EAAE,CAAE;QACPpL,YAAY,EAAEA;MAAa;QAAAgH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eAIFvH,OAAA,CAACR,cAAc;QACXW,WAAW,EAAEA,WAAY;QACzBqH,OAAO,EAAE,aAAc;QACvBC,MAAM,EAAE,CACJ;UAAEC,KAAK,EAAE,OAAO;UAAEP,KAAK,EAAE,eAAe;UAAEW,SAAS,EAAE,EAAE;UAAEH,UAAU,EAAE,OAAO;UAAEC,KAAK,EAAEzC,OAAO,aAAPA,OAAO,wBAAAvB,WAAA,GAAPuB,OAAO,CAAG,GAAG,CAAC,cAAAvB,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBuB,OAAO,cAAAtB,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyBgE,KAAK,cAAA/D,qBAAA,uBAA9BA,qBAAA,CAAiC5D,QAAQ;QAAE,CAAC,EACjI;UAAEwH,KAAK,EAAE,UAAU;UAAEP,KAAK,EAAE,eAAe;UAAEQ,UAAU,EAAE,aAAa;UAAEC,KAAK,EAAEzC,OAAO,aAAPA,OAAO,wBAAApB,WAAA,GAAPoB,OAAO,CAAG,GAAG,CAAC,cAAApB,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBoB,OAAO,cAAAnB,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyB+D,WAAW,cAAA9D,qBAAA,uBAApCA,qBAAA,CAAuC/D,QAAQ;QAAE,CAAC,EACjI;UAAEwH,KAAK,EAAE,OAAO;UAAEP,KAAK,EAAE,aAAa;UAAEQ,UAAU,EAAE,QAAQ;UAAEC,KAAK,EAAEzC,OAAO,aAAPA,OAAO,wBAAAjB,WAAA,GAAPiB,OAAO,CAAG,GAAG,CAAC,cAAAjB,WAAA,wBAAAC,mBAAA,GAAdD,WAAA,CAAgBiB,OAAO,cAAAhB,mBAAA,wBAAAC,qBAAA,GAAvBD,mBAAA,CAAyB6D,MAAM,cAAA5D,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,wBAAAC,sBAAA,GAApCD,sBAAA,CAAsC4D,IAAI,cAAA3D,sBAAA,uBAA1CA,sBAAA,CAA6CpE,QAAQ,CAAC;UAAEgI,KAAK,EAAE;QAAE,CAAC,CACzI;QACFI,OAAO,EAAE,mBAAoB;QAC7BpI,QAAQ,EAAEA,QAAS;QACnB6E,cAAc,EAAEI,OAAQ;QACxBoD,YAAY,EAAEnD,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,GAAG,CAAE;QAC7BoD,UAAU,EAAEjE,SAAU;QACtBnE,YAAY,EAAEA;MAAa;QAAAgH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAED;EAAC;AAEd,CAAC;AAAAlH,EAAA,CAtRKJ,WAAW;EAAA,QAOUH,WAAW,EAEjBD,WAAW;AAAA;AAAA4L,EAAA,GAT1BxL,WAAW;AAwRjB,eAAeA,WAAW;AAAA,IAAAwL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}