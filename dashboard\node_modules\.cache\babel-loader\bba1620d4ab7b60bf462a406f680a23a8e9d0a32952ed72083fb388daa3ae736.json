{"ast": null, "code": "module.exports = Object.freeze({\n  MODAL_BODY_TYPES: {\n    USER_DETAIL: \"USER_DETAIL\",\n    LEAD_ADD_NEW: \"LEAD_ADD_NEW\",\n    CONFIRMATION: \"CONFIRMATION\",\n    RESET_PASS: \"RESET_PASS\",\n    DEFAULT: \"\"\n  },\n  RIGHT_DRAWER_TYPES: {\n    NOTIFICATION: \"NOTIFICATION\",\n    CALENDAR_EVENTS: \"CALENDAR_EVENTS\",\n    RESOURCE_DETAILS: \"RESOURCE_DETAILS\",\n    VERSION_DETAILS: \"VERSION_DETAILS\"\n  },\n  CONFIRMATION_MODAL_CLOSE_TYPES: {\n    LEAD_DELETE: \"LEAD_DELETE\"\n  }\n});", "map": {"version": 3, "names": ["module", "exports", "Object", "freeze", "MODAL_BODY_TYPES", "USER_DETAIL", "LEAD_ADD_NEW", "CONFIRMATION", "RESET_PASS", "DEFAULT", "RIGHT_DRAWER_TYPES", "NOTIFICATION", "CALENDAR_EVENTS", "RESOURCE_DETAILS", "VERSION_DETAILS", "CONFIRMATION_MODAL_CLOSE_TYPES", "LEAD_DELETE"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/shade_cms/dashboard/src/utils/globalConstantUtil.js"], "sourcesContent": ["module.exports = Object.freeze({\r\n  MODAL_BODY_TYPES: {\r\n    USER_DETAIL: \"USER_DETAIL\",\r\n    LEAD_ADD_NEW: \"LEAD_ADD_NEW\",\r\n    CONFIRMATION: \"CONFIRMATION\",\r\n    RESET_PASS: \"RESET_PASS\",\r\n    DEFAULT: \"\",\r\n  },\r\n\r\n  RIGHT_DRAWER_TYPES: {\r\n    NOTIFICATION: \"NOTIFICATION\",\r\n    CALENDAR_EVENTS: \"CALENDAR_EVENTS\",\r\n    RESOURCE_DETAILS: \"RESOURCE_DETAILS\",\r\n    VERSION_DETAILS: \"VERSION_DETAILS\"\r\n  },\r\n\r\n  CONFIRMATION_MODAL_CLOSE_TYPES: {\r\n    LEAD_DELETE: \"LEAD_DELETE\",\r\n  },\r\n});\r\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC;EAC7BC,gBAAgB,EAAE;IAChBC,WAAW,EAAE,aAAa;IAC1BC,YAAY,EAAE,cAAc;IAC5BC,YAAY,EAAE,cAAc;IAC5BC,UAAU,EAAE,YAAY;IACxBC,OAAO,EAAE;EACX,CAAC;EAEDC,kBAAkB,EAAE;IAClBC,YAAY,EAAE,cAAc;IAC5BC,eAAe,EAAE,iBAAiB;IAClCC,gBAAgB,EAAE,kBAAkB;IACpCC,eAAe,EAAE;EACnB,CAAC;EAEDC,8BAA8B,EAAE;IAC9BC,WAAW,EAAE;EACf;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}