{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\shade_cms\\\\dashboard\\\\src\\\\features\\\\Context\\\\Context.js\",\n  _s = $RefreshSig$();\n// ScrollContext.js\nimport { createContext, useRef } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ScrollContext = /*#__PURE__*/createContext();\nexport const ScrollProvider = ({\n  children\n}) => {\n  _s();\n  const scrollContainerRef = useRef();\n  return /*#__PURE__*/_jsxDEV(ScrollContext.Provider, {\n    value: scrollContainerRef,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 9\n  }, this);\n};\n_s(ScrollProvider, \"Gfm/oEKPzfcG/oikjUDPHSjaGoQ=\");\n_c = ScrollProvider;\nvar _c;\n$RefreshReg$(_c, \"ScrollProvider\");", "map": {"version": 3, "names": ["createContext", "useRef", "jsxDEV", "_jsxDEV", "ScrollContext", "ScrollProvider", "children", "_s", "scrollContainerRef", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/shade_cms/dashboard/src/features/Context/Context.js"], "sourcesContent": ["// ScrollContext.js\r\nimport { createContext, useRef } from \"react\";\r\n\r\nexport const ScrollContext = createContext();\r\n\r\nexport const ScrollProvider = ({ children }) => {\r\n    const scrollContainerRef = useRef();\r\n\r\n    return (\r\n        <ScrollContext.Provider value={scrollContainerRef}>\r\n            {children}\r\n        </ScrollContext.Provider>\r\n    );\r\n};\r\n"], "mappings": ";;AAAA;AACA,SAASA,aAAa,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9C,OAAO,MAAMC,aAAa,gBAAGJ,aAAa,CAAC,CAAC;AAE5C,OAAO,MAAMK,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAMC,kBAAkB,GAAGP,MAAM,CAAC,CAAC;EAEnC,oBACIE,OAAA,CAACC,aAAa,CAACK,QAAQ;IAACC,KAAK,EAAEF,kBAAmB;IAAAF,QAAA,EAC7CA;EAAQ;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAEjC,CAAC;AAACP,EAAA,CARWF,cAAc;AAAAU,EAAA,GAAdV,cAAc;AAAA,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}