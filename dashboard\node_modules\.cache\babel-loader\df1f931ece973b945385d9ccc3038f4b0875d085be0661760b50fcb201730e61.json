{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\shade_cms\\\\dashboard\\\\src\\\\features\\\\Resources\\\\components\\\\websiteComponent\\\\detailspages\\\\ServiceDetails.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Link } from \"react-router-dom\";\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { updateMainContent } from \"../../../../common/homeContentSlice\";\nimport { services, projectPageData } from \"../../../../../assets/index\";\nimport content from '../content.json';\nimport structureOfServiceDetails from \"../structures/structureOFServiceDetails.json\";\nimport { TruncateText } from \"../../../../../app/capitalizeword\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ServiceDetails = ({\n  serviceId,\n  content,\n  language,\n  screen\n}) => {\n  _s();\n  var _content$, _content$$content, _content$$content$ima, _content$$content$ima2, _content$2, _content$2$content, _content$2$content$ti, _content$3, _content$3$content, _content$3$content$de, _ref, _ref2;\n  const dispatch = useDispatch();\n  const isComputer = screen > 1100;\n  const isTablet = 1100 > screen && screen > 767;\n  const isPhone = screen < 767;\n  const isLeftAlign = language === 'en';\n  const ImageFromRedux = useSelector(state => state.homeContent.present.images);\n  // const contentFromRedux = useSelector(state => state.homeContent.present.serviceDetails)\n\n  // const currentContent = contentFromRedux?.filter(\n  //     (item) => item?.id == serviceId\n  // )[0];\n\n  // useEffect(() => {\n  //     if (!contentFromRedux?.[serviceId - 1]) {\n  //         dispatch(updateMainContent({ currentPath: \"serviceDetails\", payload: [...content?.serviceDetails, { ...structureOfServiceDetails, id: content.serviceDetails?.length + 1 }] }))\n  //     } else {\n  //         dispatch(updateMainContent({ currentPath: \"serviceDetails\", payload: content.serviceDetails }))\n  //     }\n  // console.log(content)\n  // }, [])\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    dir: isLeftAlign ? \"ltr\" : \"rtl\",\n    className: \"w-full\",\n    children: [/*#__PURE__*/_jsxDEV(\"section\", {\n      className: `py-[120px]  ${isPhone ? \"px-2\" : \"px-20\"} object-cover text-center flex flex-col items-center`,\n      style: {\n        backgroundImage: `linear-gradient(to bottom,#00000020 ,#fffffffb 100%), url(${content === null || content === void 0 ? void 0 : (_content$ = content['1']) === null || _content$ === void 0 ? void 0 : (_content$$content = _content$.content) === null || _content$$content === void 0 ? void 0 : (_content$$content$ima = _content$$content.images) === null || _content$$content$ima === void 0 ? void 0 : (_content$$content$ima2 = _content$$content$ima[0]) === null || _content$$content$ima2 === void 0 ? void 0 : _content$$content$ima2.url})`,\n        backgroundPosition: 'bottom'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: `text-[41px] text-[#292E3D] `,\n        children: content === null || content === void 0 ? void 0 : (_content$2 = content['1']) === null || _content$2 === void 0 ? void 0 : (_content$2$content = _content$2.content) === null || _content$2$content === void 0 ? void 0 : (_content$2$content$ti = _content$2$content.title) === null || _content$2$content$ti === void 0 ? void 0 : _content$2$content$ti[language]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: `text-[#0E172FB2] text-[10px] w-2/3`,\n        children: content === null || content === void 0 ? void 0 : (_content$3 = content['1']) === null || _content$3 === void 0 ? void 0 : (_content$3$content = _content$3.content) === null || _content$3$content === void 0 ? void 0 : (_content$3$content$de = _content$3$content.description) === null || _content$3$content$de === void 0 ? void 0 : _content$3$content$de[language]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      className: `grid ${isTablet || isPhone ? \"grid-cols-1 items-center justify-center px-[20px]\" : \"grid-cols-2 px-[76px]\"} gap-y-[27px] gap-x-[25px] py-[34px]`,\n      children: (_ref =\n      // currentContent?.subServices ||\n      []) === null || _ref === void 0 ? void 0 : _ref.map((subService, index) => {\n        return /*#__PURE__*/_jsxDEV(\"article\", {\n          className: `border-b flex gap-4 pb-[12px]`,\n          children: [/*#__PURE__*/_jsxDEV(\"article\", {\n            className: `min-w-[197px] py-2`,\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: subService.image || projectPageData.developmentOfHo,\n              alt: \"\",\n              className: `${isTablet || isTablet ? \"w-[50vw] aspect-[4/3]\" : \"w-[196px] h-[135px]\"} `\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 41\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 37\n          }, this), /*#__PURE__*/_jsxDEV(\"article\", {\n            className: \"\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-[17px]\",\n              children: subService.title[language]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-[11px]\",\n              children: \"We capitalize on our years of experience in the construction industry to clients by also maintaining their facilities and infrastructure.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 60,\n              columnNumber: 41\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"text-[#00B9F2] text-[11px]\",\n              children: [\"Veiw Details\", /*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"\",\n                alt: \"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 65,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 41\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 37\n          }, this)]\n        }, index + \"wqer\", true, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 33\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: `text-[#292E3D] font-[400] text-[22px] ${isPhone ? \"mx-5\" : \"mx-[76px]\"} py-[20px]`,\n        children: \"Other Services\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n        className: `${isComputer ? \"w-[988px]\" : screen} overflow-x-scroll rm-scroll py-5 pt-2`,\n        children: /*#__PURE__*/_jsxDEV(\"section\", {\n          dir: isLeftAlign ? 'ltr' : 'rtl',\n          className: `flex gap-7 ${isPhone ? \"px-[38px]\" : \"px-[76px]\"} pr-[38px] w-fit items-stretch`,\n          children: (_ref2 =\n          // currentContent?.otherServices ||\n          []) === null || _ref2 === void 0 ? void 0 : _ref2.map((service, idx) => {\n            var _service$title, _service$subtitle, _service$button;\n            return /*#__PURE__*/_jsxDEV(\"article\", {\n              className: \"flex flex-col bg-white overflow-hidden shadow w-[300px]\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: service.image,\n                alt: \"img\",\n                className: \"w-full object-cover h-[176px]\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"section\", {\n                className: \"bg-[#F8F8F8] py-[14px] px-[18px] flex flex-col justify-between flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  className: \"text-[#292E3D] text-[22px] font-[400]\",\n                  children: TruncateText(service === null || service === void 0 ? void 0 : (_service$title = service.title) === null || _service$title === void 0 ? void 0 : _service$title[language], isTablet ? 15 : 23)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-[#292E3D] text-[10px] mb-2\",\n                  children: service === null || service === void 0 ? void 0 : (_service$subtitle = service.subtitle) === null || _service$subtitle === void 0 ? void 0 : _service$subtitle[language]\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 95,\n                  columnNumber: 45\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: `text-[#00B9F2] flex gap-1 items-center mt-auto ${!isLeftAlign && \"flex-rows-reverse\"}`,\n                  children: [service === null || service === void 0 ? void 0 : (_service$button = service.button) === null || _service$button === void 0 ? void 0 : _service$button[language], /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"https://frequencyimage.s3.ap-south-1.amazonaws.com/61c0f0c2-6c90-42b2-a71e-27bc4c7446c2-mingcute_arrow-up-line.svg\",\n                    alt: \"\",\n                    className: `${isLeftAlign && \"rotate-[180deg]\"} w-[16px] h-[16px]`\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 100,\n                    columnNumber: 49\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 98,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 41\n              }, this)]\n            }, idx, true, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 37\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 33,\n    columnNumber: 9\n  }, this);\n};\n_s(ServiceDetails, \"X4ap/0/LUsk2aehm0ESb6UoHmEY=\", false, function () {\n  return [useDispatch, useSelector];\n});\n_c = ServiceDetails;\nexport default ServiceDetails;\nvar _c;\n$RefreshReg$(_c, \"ServiceDetails\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Link", "useSelector", "useDispatch", "update<PERSON>ain<PERSON><PERSON>nt", "services", "projectPageData", "content", "structureOfServiceDetails", "TruncateText", "jsxDEV", "_jsxDEV", "ServiceDetails", "serviceId", "language", "screen", "_s", "_content$", "_content$$content", "_content$$content$ima", "_content$$content$ima2", "_content$2", "_content$2$content", "_content$2$content$ti", "_content$3", "_content$3$content", "_content$3$content$de", "_ref", "_ref2", "dispatch", "isComputer", "isTablet", "isPhone", "isLeftAlign", "ImageFromRedux", "state", "homeContent", "present", "images", "dir", "className", "children", "style", "backgroundImage", "url", "backgroundPosition", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "map", "subService", "index", "src", "image", "developmentOfHo", "alt", "service", "idx", "_service$title", "_service$subtitle", "_service$button", "subtitle", "button", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/shade_cms/dashboard/src/features/Resources/components/websiteComponent/detailspages/ServiceDetails.jsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { useSelector, useDispatch } from \"react-redux\";\r\nimport { updateMainContent } from \"../../../../common/homeContentSlice\";\r\nimport { services, projectPageData } from \"../../../../../assets/index\";\r\nimport content from '../content.json'\r\nimport structureOfServiceDetails from \"../structures/structureOFServiceDetails.json\";\r\nimport { TruncateText } from \"../../../../../app/capitalizeword\";\r\n\r\nconst ServiceDetails = ({ serviceId, content, language, screen }) => {\r\n    const dispatch = useDispatch();\r\n    const isComputer = screen > 1100;\r\n    const isTablet = 1100 > screen && screen > 767;\r\n    const isPhone = screen < 767;\r\n    const isLeftAlign = language === 'en'\r\n    const ImageFromRedux = useSelector(state => state.homeContent.present.images)\r\n    // const contentFromRedux = useSelector(state => state.homeContent.present.serviceDetails)\r\n\r\n    // const currentContent = contentFromRedux?.filter(\r\n    //     (item) => item?.id == serviceId\r\n    // )[0];\r\n\r\n\r\n    // useEffect(() => {\r\n    //     if (!contentFromRedux?.[serviceId - 1]) {\r\n    //         dispatch(updateMainContent({ currentPath: \"serviceDetails\", payload: [...content?.serviceDetails, { ...structureOfServiceDetails, id: content.serviceDetails?.length + 1 }] }))\r\n    //     } else {\r\n    //         dispatch(updateMainContent({ currentPath: \"serviceDetails\", payload: content.serviceDetails }))\r\n    //     }\r\n    // console.log(content)\r\n    // }, [])\r\n    return (\r\n        <div dir={isLeftAlign ? \"ltr\" : \"rtl\"} className=\"w-full\">\r\n            {/* banner */}\r\n            <section className={`py-[120px]  ${isPhone ? \"px-2\" : \"px-20\"} object-cover text-center flex flex-col items-center`}\r\n                style={{ backgroundImage: `linear-gradient(to bottom,#00000020 ,#fffffffb 100%), url(${content?.['1']?.content?.images?.[0]?.url})`, backgroundPosition: 'bottom' }}\r\n            >\r\n                <h1 className={`text-[41px] text-[#292E3D] `}>\r\n                    {/* {currentContent?.banner?.title?.[language] || \"Project Details\"} */}\r\n                    {content?.['1']?.content?.title?.[language]}\r\n                </h1>\r\n                <p className={`text-[#0E172FB2] text-[10px] w-2/3`}>\r\n                    {content?.['1']?.content?.description?.[language]}\r\n                </p>\r\n            </section>\r\n\r\n            {/* Sub services */}\r\n            <section className={`grid ${isTablet || isPhone ? \"grid-cols-1 items-center justify-center px-[20px]\" : \"grid-cols-2 px-[76px]\"} gap-y-[27px] gap-x-[25px] py-[34px]`}>\r\n                {\r\n                    (\r\n                        // currentContent?.subServices ||\r\n                        [])?.map((subService, index) => {\r\n                            return (\r\n                                <article key={index + \"wqer\"} className={`border-b flex gap-4 pb-[12px]`}>\r\n                                    <article className={`min-w-[197px] py-2`}>\r\n                                        <img src={subService.image || projectPageData.developmentOfHo} alt=\"\" className={`${isTablet || isTablet ? \"w-[50vw] aspect-[4/3]\" : \"w-[196px] h-[135px]\"} `} />\r\n                                    </article>\r\n                                    <article className=\"\">\r\n                                        <h3 className=\"text-[17px]\">{subService.title[language]}</h3>\r\n                                        <p className=\"text-[11px]\">\r\n                                            We capitalize on our years of experience in the construction industry to clients by also maintaining their facilities and infrastructure.\r\n                                        </p>\r\n                                        <button className=\"text-[#00B9F2] text-[11px]\">\r\n                                            Veiw Details\r\n                                            <img src=\"\" alt=\"\" />\r\n                                        </button>\r\n                                    </article>\r\n                                </article>\r\n                            )\r\n                        })\r\n                }\r\n            </section>\r\n\r\n            {/* Other Services */}\r\n            <section>\r\n                <h3 className={`text-[#292E3D] font-[400] text-[22px] ${isPhone ? \"mx-5\" : \"mx-[76px]\"} py-[20px]`}>Other Services</h3>\r\n                <section className={`${isComputer ? \"w-[988px]\" : screen} overflow-x-scroll rm-scroll py-5 pt-2`}>\r\n                    <section dir={isLeftAlign ? 'ltr' : 'rtl'}\r\n                        className={`flex gap-7 ${isPhone ? \"px-[38px]\" : \"px-[76px]\"} pr-[38px] w-fit items-stretch`}>\r\n                        {\r\n                            (\r\n                                // currentContent?.otherServices ||\r\n                                []\r\n                            )?.map((service, idx) => {\r\n                                return (\r\n                                    <article\r\n                                        key={idx}\r\n                                        className=\"flex flex-col bg-white overflow-hidden shadow w-[300px]\"\r\n                                    >\r\n                                        <img src={service.image} alt=\"img\" className=\"w-full object-cover h-[176px]\" />\r\n                                        <section className=\"bg-[#F8F8F8] py-[14px] px-[18px] flex flex-col justify-between flex-1\">\r\n                                            <h1 className=\"text-[#292E3D] text-[22px] font-[400]\">\r\n                                                {TruncateText(service?.title?.[language], isTablet ? 15 : 23)}\r\n                                            </h1>\r\n                                            <p className=\"text-[#292E3D] text-[10px] mb-2\">\r\n                                                {service?.subtitle?.[language]}\r\n                                            </p>\r\n                                            <button className={`text-[#00B9F2] flex gap-1 items-center mt-auto ${!isLeftAlign && \"flex-rows-reverse\"}`}>\r\n                                                {service?.button?.[language]}\r\n                                                <img\r\n                                                    src=\"https://frequencyimage.s3.ap-south-1.amazonaws.com/61c0f0c2-6c90-42b2-a71e-27bc4c7446c2-mingcute_arrow-up-line.svg\"\r\n                                                    alt=\"\"\r\n                                                    className={`${isLeftAlign && \"rotate-[180deg]\"} w-[16px] h-[16px]`}\r\n                                                />\r\n                                            </button>\r\n                                        </section>\r\n                                    </article>\r\n                                )\r\n                            })}\r\n                    </section>\r\n                </section>\r\n            </section>\r\n        </div>\r\n    )\r\n}\r\n\r\nexport default ServiceDetails"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,iBAAiB,QAAQ,qCAAqC;AACvE,SAASC,QAAQ,EAAEC,eAAe,QAAQ,6BAA6B;AACvE,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,yBAAyB,MAAM,8CAA8C;AACpF,SAASC,YAAY,QAAQ,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjE,MAAMC,cAAc,GAAGA,CAAC;EAAEC,SAAS;EAAEN,OAAO;EAAEO,QAAQ;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,SAAA,EAAAC,iBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,UAAA,EAAAC,kBAAA,EAAAC,qBAAA,EAAAC,IAAA,EAAAC,KAAA;EACjE,MAAMC,QAAQ,GAAG1B,WAAW,CAAC,CAAC;EAC9B,MAAM2B,UAAU,GAAGf,MAAM,GAAG,IAAI;EAChC,MAAMgB,QAAQ,GAAG,IAAI,GAAGhB,MAAM,IAAIA,MAAM,GAAG,GAAG;EAC9C,MAAMiB,OAAO,GAAGjB,MAAM,GAAG,GAAG;EAC5B,MAAMkB,WAAW,GAAGnB,QAAQ,KAAK,IAAI;EACrC,MAAMoB,cAAc,GAAGhC,WAAW,CAACiC,KAAK,IAAIA,KAAK,CAACC,WAAW,CAACC,OAAO,CAACC,MAAM,CAAC;EAC7E;;EAEA;EACA;EACA;;EAGA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,oBACI3B,OAAA;IAAK4B,GAAG,EAAEN,WAAW,GAAG,KAAK,GAAG,KAAM;IAACO,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBAErD9B,OAAA;MAAS6B,SAAS,EAAE,eAAeR,OAAO,GAAG,MAAM,GAAG,OAAO,sDAAuD;MAChHU,KAAK,EAAE;QAAEC,eAAe,EAAE,6DAA6DpC,OAAO,aAAPA,OAAO,wBAAAU,SAAA,GAAPV,OAAO,CAAG,GAAG,CAAC,cAAAU,SAAA,wBAAAC,iBAAA,GAAdD,SAAA,CAAgBV,OAAO,cAAAW,iBAAA,wBAAAC,qBAAA,GAAvBD,iBAAA,CAAyBoB,MAAM,cAAAnB,qBAAA,wBAAAC,sBAAA,GAA/BD,qBAAA,CAAkC,CAAC,CAAC,cAAAC,sBAAA,uBAApCA,sBAAA,CAAsCwB,GAAG,GAAG;QAAEC,kBAAkB,EAAE;MAAS,CAAE;MAAAJ,QAAA,gBAEpK9B,OAAA;QAAI6B,SAAS,EAAE,6BAA8B;QAAAC,QAAA,EAExClC,OAAO,aAAPA,OAAO,wBAAAc,UAAA,GAAPd,OAAO,CAAG,GAAG,CAAC,cAAAc,UAAA,wBAAAC,kBAAA,GAAdD,UAAA,CAAgBd,OAAO,cAAAe,kBAAA,wBAAAC,qBAAA,GAAvBD,kBAAA,CAAyBwB,KAAK,cAAAvB,qBAAA,uBAA9BA,qBAAA,CAAiCT,QAAQ;MAAC;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CAAC,eACLvC,OAAA;QAAG6B,SAAS,EAAE,oCAAqC;QAAAC,QAAA,EAC9ClC,OAAO,aAAPA,OAAO,wBAAAiB,UAAA,GAAPjB,OAAO,CAAG,GAAG,CAAC,cAAAiB,UAAA,wBAAAC,kBAAA,GAAdD,UAAA,CAAgBjB,OAAO,cAAAkB,kBAAA,wBAAAC,qBAAA,GAAvBD,kBAAA,CAAyB0B,WAAW,cAAAzB,qBAAA,uBAApCA,qBAAA,CAAuCZ,QAAQ;MAAC;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGVvC,OAAA;MAAS6B,SAAS,EAAE,QAAQT,QAAQ,IAAIC,OAAO,GAAG,mDAAmD,GAAG,uBAAuB,sCAAuC;MAAAS,QAAA,GAAAd,IAAA;MAG1J;MACA,EAAE,cAAAA,IAAA,uBAFNA,IAAA,CAESyB,GAAG,CAAC,CAACC,UAAU,EAAEC,KAAK,KAAK;QAC5B,oBACI3C,OAAA;UAA8B6B,SAAS,EAAE,+BAAgC;UAAAC,QAAA,gBACrE9B,OAAA;YAAS6B,SAAS,EAAE,oBAAqB;YAAAC,QAAA,eACrC9B,OAAA;cAAK4C,GAAG,EAAEF,UAAU,CAACG,KAAK,IAAIlD,eAAe,CAACmD,eAAgB;cAACC,GAAG,EAAC,EAAE;cAAClB,SAAS,EAAE,GAAGT,QAAQ,IAAIA,QAAQ,GAAG,uBAAuB,GAAG,qBAAqB;YAAI;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5J,CAAC,eACVvC,OAAA;YAAS6B,SAAS,EAAC,EAAE;YAAAC,QAAA,gBACjB9B,OAAA;cAAI6B,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEY,UAAU,CAACP,KAAK,CAAChC,QAAQ;YAAC;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC7DvC,OAAA;cAAG6B,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAE3B;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJvC,OAAA;cAAQ6B,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,cAE3C,eAAA9B,OAAA;gBAAK4C,GAAG,EAAC,EAAE;gBAACG,GAAG,EAAC;cAAE;gBAAAX,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA,GAbAI,KAAK,GAAG,MAAM;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAcnB,CAAC;MAElB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEL,CAAC,eAGVvC,OAAA;MAAA8B,QAAA,gBACI9B,OAAA;QAAI6B,SAAS,EAAE,yCAAyCR,OAAO,GAAG,MAAM,GAAG,WAAW,YAAa;QAAAS,QAAA,EAAC;MAAc;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvHvC,OAAA;QAAS6B,SAAS,EAAE,GAAGV,UAAU,GAAG,WAAW,GAAGf,MAAM,wCAAyC;QAAA0B,QAAA,eAC7F9B,OAAA;UAAS4B,GAAG,EAAEN,WAAW,GAAG,KAAK,GAAG,KAAM;UACtCO,SAAS,EAAE,cAAcR,OAAO,GAAG,WAAW,GAAG,WAAW,gCAAiC;UAAAS,QAAA,GAAAb,KAAA;UAGrF;UACA,EAAE,cAAAA,KAAA,uBAFNA,KAAA,CAGGwB,GAAG,CAAC,CAACO,OAAO,EAAEC,GAAG,KAAK;YAAA,IAAAC,cAAA,EAAAC,iBAAA,EAAAC,eAAA;YACrB,oBACIpD,OAAA;cAEI6B,SAAS,EAAC,yDAAyD;cAAAC,QAAA,gBAEnE9B,OAAA;gBAAK4C,GAAG,EAAEI,OAAO,CAACH,KAAM;gBAACE,GAAG,EAAC,KAAK;gBAAClB,SAAS,EAAC;cAA+B;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC/EvC,OAAA;gBAAS6B,SAAS,EAAC,uEAAuE;gBAAAC,QAAA,gBACtF9B,OAAA;kBAAI6B,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAChDhC,YAAY,CAACkD,OAAO,aAAPA,OAAO,wBAAAE,cAAA,GAAPF,OAAO,CAAEb,KAAK,cAAAe,cAAA,uBAAdA,cAAA,CAAiB/C,QAAQ,CAAC,EAAEiB,QAAQ,GAAG,EAAE,GAAG,EAAE;gBAAC;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7D,CAAC,eACLvC,OAAA;kBAAG6B,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EACzCkB,OAAO,aAAPA,OAAO,wBAAAG,iBAAA,GAAPH,OAAO,CAAEK,QAAQ,cAAAF,iBAAA,uBAAjBA,iBAAA,CAAoBhD,QAAQ;gBAAC;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACJvC,OAAA;kBAAQ6B,SAAS,EAAE,kDAAkD,CAACP,WAAW,IAAI,mBAAmB,EAAG;kBAAAQ,QAAA,GACtGkB,OAAO,aAAPA,OAAO,wBAAAI,eAAA,GAAPJ,OAAO,CAAEM,MAAM,cAAAF,eAAA,uBAAfA,eAAA,CAAkBjD,QAAQ,CAAC,eAC5BH,OAAA;oBACI4C,GAAG,EAAC,oHAAoH;oBACxHG,GAAG,EAAC,EAAE;oBACNlB,SAAS,EAAE,GAAGP,WAAW,IAAI,iBAAiB;kBAAqB;oBAAAc,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA,GAnBLU,GAAG;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAoBH,CAAC;UAElB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEd,CAAC;AAAAlC,EAAA,CAzGKJ,cAAc;EAAA,QACCT,WAAW,EAKLD,WAAW;AAAA;AAAAgE,EAAA,GANhCtD,cAAc;AA2GpB,eAAeA,cAAc;AAAA,IAAAsD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}