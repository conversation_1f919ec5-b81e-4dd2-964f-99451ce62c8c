{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\shade_cms\\\\dashboard\\\\src\\\\containers\\\\Navbar.jsx\",\n  _s = $RefreshSig$();\nimport { useSelector, useDispatch } from \"react-redux\";\nimport { updateName, updateTag, updateType } from \"../features/common/navbarSlice\";\nimport capitalizeWords from \"../app/capitalizeword\";\nimport { useEffect, useState } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navbar = ({\n  setCurrentResource\n}) => {\n  _s();\n  // const userPermissions = useSelector(state => state.user.user?.permissions);\n  // const currentNav = useSelector(state => state.navBar.resourceTag)\n  const currentName = useSelector(state => state.navBar.name);\n  const dispatch = useDispatch();\n  // const [currentName, setCurrentName] = useState(\"Pages\")\n  // const permissionsSet = new Set(userPermissions);\n\n  const navs = [{\n    name: \"Pages\",\n    resourceType: \"MAIN_PAGE\",\n    resourceTag: \"MAIN\",\n    permission: \"PAGE_MANAGEMENT\"\n  }, {\n    name: \"Services\",\n    resourceType: \"SUB_PAGE\",\n    resourceTag: \"SERVICE\",\n    permission: \"SERVICE_MANAGEMENT\"\n  }, {\n    name: \"Sub Services\",\n    resourceType: \"SUB_PAGE_ITEM\",\n    resourceTag: \"SERVICE\",\n    permission: \"SERVICE_MANAGEMENT\"\n  }, {\n    name: \"Market\",\n    resourceType: \"SUB_PAGE\",\n    resourceTag: \"MARKET\",\n    permission: \"MARKET_MANAGEMENT\"\n  }, {\n    name: \"Project\",\n    resourceType: \"SUB_PAGE\",\n    resourceTag: \"PROJECT\",\n    permission: \"PROJECT_MANAGEMENT\"\n  }, {\n    name: \"Testimonials\",\n    resourceType: \"SUB_PAGE\",\n    resourceTag: \"TESTIMONIAL\",\n    permission: \"TESTIMONIAL_MANAGEMENT\"\n  }, {\n    name: \"Career Page\",\n    resourceType: \"SUB_PAGE\",\n    resourceTag: \"CAREER\",\n    permission: \"CAREER_MANAGEMENT\"\n  }, {\n    name: \"Blogs & News\",\n    resourceType: \"SUB_PAGE\",\n    resourceTag: \"NEWS\",\n    permission: \"NEWS_BLOGS_MANAGEMENT\"\n  }, {\n    name: \"Header\",\n    resourceType: \"HEADER\",\n    resourceTag: \"HEADER\",\n    permission: [\"HEADER_MANAGEMENT\", \"FOOTER_MANAGEMENT\"]\n  }, {\n    name: \"Footer\",\n    resourceType: \"FOOTER\",\n    resourceTag: \"FOOTER\",\n    permission: [\"HEADER_MANAGEMENT\", \"FOOTER_MANAGEMENT\"]\n  }, {\n    name: \"S & R\",\n    resourceType: \"SUB_PAGE\",\n    resourceTag: \"SAFETY_RESPONSIBILITY\",\n    permission: \"PROJECT_MANAGEMENT\"\n  }];\n\n  // const hasPermission = (required) => {\n  //     if (Array.isArray(required)) {\n  //         return required.some(p => permissionsSet.has(p));\n  //     }\n  //     return permissionsSet.has(required);\n  // };\n\n  const settingResources = (resource, tag, name) => {\n    dispatch(updateType(resource));\n    dispatch(updateTag(tag));\n    dispatch(updateName(name));\n    localStorage.setItem(\"resourceType\", resource);\n    localStorage.setItem(\"resourceTag\", tag);\n    localStorage.setItem(\"navName\", name);\n  };\n  useEffect(() => {\n    let currentName = localStorage.getItem(\"navName\");\n    if (currentName) {\n      // setCurrentName(currentName)\n      dispatch(updateName(currentName));\n    }\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"sticky top-0 z-20\",\n    children: /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"bg-[#29469C] xl:text-[.9rem] sm:text-[.8rem] w-full rounded-lg sm:overflow-x-scroll xl:overflow-x-visible customscroller h-[61px]\",\n      children: /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"flex md:flex-nowrap lg:flex-nowrap md:w-full text-white py-2 whitespace-nowrap\",\n        children: navs.map((nav, index) => {\n          return /*#__PURE__*/_jsxDEV(\"li\", {\n            className: \"max-w-[150px] w-full text-center px-2 relative flex items-center justify-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => settingResources(nav.resourceType, nav.resourceTag, nav.name),\n              className: `block w-full rounded-lg py-1 ${currentName === nav.name ? \"bg-base-200 text-stone-700 dark:text-stone-50\" : \"hover:bg-base-200\"} hover:text-stone-700 dark:hover:text-stone-50 transition flex flex-col`,\n              children: [nav.name, /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-[300] text-[9px]\",\n                children: [\"(\", capitalizeWords(nav.resourceType), \")\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 37\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 33\n            }, this), index !== navs.length - 1 && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"absolute right-[-1px] top-1/2 -translate-y-1/2 h-1/2 w-[1px] bg-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 37\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 29\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 9\n  }, this);\n};\n_s(Navbar, \"IhhBRdg0gjqSY0QnhBOpeWVdfG0=\", false, function () {\n  return [useSelector, useDispatch];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["useSelector", "useDispatch", "updateName", "updateTag", "updateType", "capitalizeWords", "useEffect", "useState", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "setCurrentResource", "_s", "currentName", "state", "navBar", "name", "dispatch", "navs", "resourceType", "resourceTag", "permission", "settingResources", "resource", "tag", "localStorage", "setItem", "getItem", "className", "children", "map", "nav", "index", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "length", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/shade_cms/dashboard/src/containers/Navbar.jsx"], "sourcesContent": ["import { useSelector, useDispatch } from \"react-redux\";\r\nimport { updateName, updateTag, updateType } from \"../features/common/navbarSlice\";\r\nimport capitalizeWords from \"../app/capitalizeword\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\nconst Navbar = ({ setCurrentResource }) => {\r\n    // const userPermissions = useSelector(state => state.user.user?.permissions);\r\n    // const currentNav = useSelector(state => state.navBar.resourceTag)\r\n    const currentName = useSelector(state => state.navBar.name)\r\n    const dispatch = useDispatch();\r\n    // const [currentName, setCurrentName] = useState(\"Pages\")\r\n    // const permissionsSet = new Set(userPermissions);\r\n\r\n\r\n    const navs = [\r\n        { name: \"Pages\", resourceType: \"MAIN_PAGE\", resourceTag: \"MAIN\", permission: \"PAGE_MANAGEMENT\" },\r\n        { name: \"Services\", resourceType: \"SUB_PAGE\", resourceTag: \"SERVICE\", permission: \"SERVICE_MANAGEMENT\" },\r\n        { name: \"Sub Services\", resourceType: \"SUB_PAGE_ITEM\", resourceTag: \"SERVICE\", permission: \"SERVICE_MANAGEMENT\" },\r\n        { name: \"Market\", resourceType: \"SUB_PAGE\", resourceTag: \"MARKET\", permission: \"MARKET_MANAGEMENT\" },\r\n        { name: \"Project\", resourceType: \"SUB_PAGE\", resourceTag: \"PROJECT\", permission: \"PROJECT_MANAGEMENT\" },\r\n        { name: \"Testimonials\", resourceType: \"SUB_PAGE\", resourceTag: \"TESTIMONIAL\", permission: \"TESTIMONIAL_MANAGEMENT\" },\r\n        { name: \"Career Page\", resourceType: \"SUB_PAGE\", resourceTag: \"CAREER\", permission: \"CAREER_MANAGEMENT\" },\r\n        { name: \"Blogs & News\", resourceType: \"SUB_PAGE\", resourceTag: \"NEWS\", permission: \"NEWS_BLOGS_MANAGEMENT\" },\r\n        { name: \"Header\", resourceType: \"HEADER\", resourceTag: \"HEADER\", permission: [\"HEADER_MANAGEMENT\", \"FOOTER_MANAGEMENT\"] },\r\n        { name: \"Footer\", resourceType: \"FOOTER\", resourceTag: \"FOOTER\", permission: [\"HEADER_MANAGEMENT\", \"FOOTER_MANAGEMENT\"] },\r\n        { name: \"S & R\", resourceType: \"SUB_PAGE\", resourceTag: \"SAFETY_RESPONSIBILITY\", permission: \"PROJECT_MANAGEMENT\" },\r\n    ];\r\n\r\n    // const hasPermission = (required) => {\r\n    //     if (Array.isArray(required)) {\r\n    //         return required.some(p => permissionsSet.has(p));\r\n    //     }\r\n    //     return permissionsSet.has(required);\r\n    // };\r\n\r\n    const settingResources = (resource, tag, name) => {\r\n        dispatch(updateType(resource));\r\n        dispatch(updateTag(tag));\r\n        dispatch(updateName(name));\r\n        localStorage.setItem(\"resourceType\", resource);\r\n        localStorage.setItem(\"resourceTag\", tag);\r\n        localStorage.setItem(\"navName\", name)\r\n    };\r\n\r\n    useEffect(() => {\r\n        let currentName = localStorage.getItem(\"navName\")\r\n        if(currentName) {\r\n            // setCurrentName(currentName)\r\n            dispatch(updateName(currentName))\r\n        }\r\n    }, [])\r\n\r\n    return (\r\n        <div className=\"sticky top-0 z-20\">\r\n            <nav className=\"bg-[#29469C] xl:text-[.9rem] sm:text-[.8rem] w-full rounded-lg sm:overflow-x-scroll xl:overflow-x-visible customscroller h-[61px]\">\r\n                <ul className=\"flex md:flex-nowrap lg:flex-nowrap md:w-full text-white py-2 whitespace-nowrap\">\r\n                    {navs.map((nav, index) => {\r\n\r\n                        return (\r\n                            <li\r\n                                key={index}\r\n                                className=\"max-w-[150px] w-full text-center px-2 relative flex items-center justify-center\"\r\n                            >\r\n                                <button\r\n                                    onClick={() => settingResources(nav.resourceType, nav.resourceTag, nav.name)}\r\n                                    className={`block w-full rounded-lg py-1 ${currentName === nav.name\r\n                                        ? \"bg-base-200 text-stone-700 dark:text-stone-50\"\r\n                                        : \"hover:bg-base-200\"\r\n                                        } hover:text-stone-700 dark:hover:text-stone-50 transition flex flex-col`}\r\n                                >\r\n                                    {nav.name}\r\n                                    <span className=\"text-[300] text-[9px]\">{\"(\"}{capitalizeWords(nav.resourceType)}{\")\"}</span>\r\n                                </button>\r\n                                {index !== navs.length - 1 && (\r\n                                    <span className=\"absolute right-[-1px] top-1/2 -translate-y-1/2 h-1/2 w-[1px] bg-white\"></span>\r\n                                )}\r\n                            </li>\r\n                        );\r\n                    })}\r\n                </ul>\r\n            </nav>\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default Navbar;\r\n"], "mappings": ";;AAAA,SAASA,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,UAAU,EAAEC,SAAS,EAAEC,UAAU,QAAQ,gCAAgC;AAClF,OAAOC,eAAe,MAAM,uBAAuB;AACnD,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5C,MAAMC,MAAM,GAAGA,CAAC;EAAEC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EACvC;EACA;EACA,MAAMC,WAAW,GAAGb,WAAW,CAACc,KAAK,IAAIA,KAAK,CAACC,MAAM,CAACC,IAAI,CAAC;EAC3D,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B;EACA;;EAGA,MAAMiB,IAAI,GAAG,CACT;IAAEF,IAAI,EAAE,OAAO;IAAEG,YAAY,EAAE,WAAW;IAAEC,WAAW,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAkB,CAAC,EAChG;IAAEL,IAAI,EAAE,UAAU;IAAEG,YAAY,EAAE,UAAU;IAAEC,WAAW,EAAE,SAAS;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACxG;IAAEL,IAAI,EAAE,cAAc;IAAEG,YAAY,EAAE,eAAe;IAAEC,WAAW,EAAE,SAAS;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACjH;IAAEL,IAAI,EAAE,QAAQ;IAAEG,YAAY,EAAE,UAAU;IAAEC,WAAW,EAAE,QAAQ;IAAEC,UAAU,EAAE;EAAoB,CAAC,EACpG;IAAEL,IAAI,EAAE,SAAS;IAAEG,YAAY,EAAE,UAAU;IAAEC,WAAW,EAAE,SAAS;IAAEC,UAAU,EAAE;EAAqB,CAAC,EACvG;IAAEL,IAAI,EAAE,cAAc;IAAEG,YAAY,EAAE,UAAU;IAAEC,WAAW,EAAE,aAAa;IAAEC,UAAU,EAAE;EAAyB,CAAC,EACpH;IAAEL,IAAI,EAAE,aAAa;IAAEG,YAAY,EAAE,UAAU;IAAEC,WAAW,EAAE,QAAQ;IAAEC,UAAU,EAAE;EAAoB,CAAC,EACzG;IAAEL,IAAI,EAAE,cAAc;IAAEG,YAAY,EAAE,UAAU;IAAEC,WAAW,EAAE,MAAM;IAAEC,UAAU,EAAE;EAAwB,CAAC,EAC5G;IAAEL,IAAI,EAAE,QAAQ;IAAEG,YAAY,EAAE,QAAQ;IAAEC,WAAW,EAAE,QAAQ;IAAEC,UAAU,EAAE,CAAC,mBAAmB,EAAE,mBAAmB;EAAE,CAAC,EACzH;IAAEL,IAAI,EAAE,QAAQ;IAAEG,YAAY,EAAE,QAAQ;IAAEC,WAAW,EAAE,QAAQ;IAAEC,UAAU,EAAE,CAAC,mBAAmB,EAAE,mBAAmB;EAAE,CAAC,EACzH;IAAEL,IAAI,EAAE,OAAO;IAAEG,YAAY,EAAE,UAAU;IAAEC,WAAW,EAAE,uBAAuB;IAAEC,UAAU,EAAE;EAAqB,CAAC,CACtH;;EAED;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAMC,gBAAgB,GAAGA,CAACC,QAAQ,EAAEC,GAAG,EAAER,IAAI,KAAK;IAC9CC,QAAQ,CAACb,UAAU,CAACmB,QAAQ,CAAC,CAAC;IAC9BN,QAAQ,CAACd,SAAS,CAACqB,GAAG,CAAC,CAAC;IACxBP,QAAQ,CAACf,UAAU,CAACc,IAAI,CAAC,CAAC;IAC1BS,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEH,QAAQ,CAAC;IAC9CE,YAAY,CAACC,OAAO,CAAC,aAAa,EAAEF,GAAG,CAAC;IACxCC,YAAY,CAACC,OAAO,CAAC,SAAS,EAAEV,IAAI,CAAC;EACzC,CAAC;EAEDV,SAAS,CAAC,MAAM;IACZ,IAAIO,WAAW,GAAGY,YAAY,CAACE,OAAO,CAAC,SAAS,CAAC;IACjD,IAAGd,WAAW,EAAE;MACZ;MACAI,QAAQ,CAACf,UAAU,CAACW,WAAW,CAAC,CAAC;IACrC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,oBACIJ,OAAA;IAAKmB,SAAS,EAAC,mBAAmB;IAAAC,QAAA,eAC9BpB,OAAA;MAAKmB,SAAS,EAAC,mIAAmI;MAAAC,QAAA,eAC9IpB,OAAA;QAAImB,SAAS,EAAC,gFAAgF;QAAAC,QAAA,EACzFX,IAAI,CAACY,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,KAAK;UAEtB,oBACIvB,OAAA;YAEImB,SAAS,EAAC,iFAAiF;YAAAC,QAAA,gBAE3FpB,OAAA;cACIwB,OAAO,EAAEA,CAAA,KAAMX,gBAAgB,CAACS,GAAG,CAACZ,YAAY,EAAEY,GAAG,CAACX,WAAW,EAAEW,GAAG,CAACf,IAAI,CAAE;cAC7EY,SAAS,EAAE,gCAAgCf,WAAW,KAAKkB,GAAG,CAACf,IAAI,GAC7D,+CAA+C,GAC/C,mBAAmB,yEACqD;cAAAa,QAAA,GAE7EE,GAAG,CAACf,IAAI,eACTP,OAAA;gBAAMmB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,GAAE,GAAG,EAAExB,eAAe,CAAC0B,GAAG,CAACZ,YAAY,CAAC,EAAE,GAAG;cAAA;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CAAC,EACRL,KAAK,KAAKd,IAAI,CAACoB,MAAM,GAAG,CAAC,iBACtB7B,OAAA;cAAMmB,SAAS,EAAC;YAAuE;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CACjG;UAAA,GAfIL,KAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBV,CAAC;QAEb,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAACzB,EAAA,CA9EIF,MAAM;EAAA,QAGYV,WAAW,EACdC,WAAW;AAAA;AAAAsC,EAAA,GAJ1B7B,MAAM;AAgFZ,eAAeA,MAAM;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}