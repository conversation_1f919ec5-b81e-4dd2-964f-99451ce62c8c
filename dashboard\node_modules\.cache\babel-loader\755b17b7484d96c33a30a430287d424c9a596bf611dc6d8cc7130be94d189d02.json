{"ast": null, "code": "export default function dynamicSize(size, width) {\n  return `${(width / 1532 * size).toFixed(0)}px`;\n}\nexport const differentText = {\n  highlight1: \"border-green-600 border\",\n  checkDifference: function (arg1, arg2, highlight) {\n    if (Array.isArray(arg1) && Array.isArray(arg2)) {\n      return arg1.forEach((e, i) => e.id === arg2[i].id);\n    }\n    if (arg1 !== arg2) {\n      var _this$highlight;\n      return (_this$highlight = this === null || this === void 0 ? void 0 : this[highlight]) !== null && _this$highlight !== void 0 ? _this$highlight : this === null || this === void 0 ? void 0 : this.highlight1;\n    } else return \"\";\n  }\n};\nexport function defineDevice(screen) {\n  if (screen > 900) {\n    return \"computer\";\n  } else if (screen < 900 && screen > 550) {\n    return 'tablet';\n  } else {\n    return 'phone';\n  }\n}\n\n// const fontKeys = [\n//     \"mainHeading\", \"mainPara\", \"mainButton\",\n//     \"markDownHead\", \"markDownPara\", \"markDownButton\",\n//     \"serviceHeading\", \"services\",\n//     \"experienceCount\", \"experienceTitle\",\n//     \"experienceHeading\", \"experiencePara\", \"experienceButton\",\n//     \"subProjectTopButton\", \"subProjectHeadings\", \"subProjectParas\",\n//     \"subProjectBoxHeading\", \"subProjectBoxPara\",\n//     \"subProjectButtons\",\n//     \"clientSection\",\n//     \"testimonialsHead\", \"testimonialsHeading\", \"testimonialsPosition\",\n//     \"testimonialsQuote\", \"testimonialsCompany\", 'aboutMainPara'\n// ];\n\nconst fontSizes = {\n  computer: {\n    // home -- some of them are used in other pages due similarity\n    mainHeading: 70,\n    mainPara: 16,\n    mainButton: 18,\n    markDownHead: 36,\n    markDownPara: 15,\n    markDownButton: 70,\n    serviceHeading: 36,\n    services: 20,\n    experienceCount: 40,\n    experienceTitle: 12,\n    experienceHeading: 60,\n    experiencePara: 16,\n    experienceButton: 18,\n    subProjectTopButton: 16,\n    subProjectHeadings: 32,\n    subProjectParas: 16,\n    subProjectBoxHeading: 20,\n    subProjectBoxPara: 16,\n    subProjectButtons: 18,\n    clientSection: 36,\n    testimonialsHead: 36,\n    testimonialsHeading: 20,\n    testimonialsPosition: 12,\n    testimonialsQuote: 14,\n    testimonialsCompany: 16,\n    // about\n    aboutMainPara: 26,\n    aboutPaddingX: 150,\n    aboutVideoW: 639,\n    aboutVideoH: 457,\n    // video size but not applied to the video\n    aboutCardPaddingY: 40,\n    aboutCardPaddingX: 10\n  },\n  tablet: {\n    // home -- some of them are used in other pages due similarity\n    mainHeading: 70,\n    mainPara: 16,\n    mainButton: 18,\n    markDownHead: 36,\n    markDownPara: 15,\n    markDownButton: 70,\n    serviceHeading: 36,\n    services: 20,\n    experienceCount: 40,\n    experienceTitle: 12,\n    experienceHeading: 60,\n    experiencePara: 16,\n    experienceButton: 18,\n    subProjectTopButton: 16,\n    subProjectHeadings: 32,\n    subProjectParas: 16,\n    subProjectBoxHeading: 20,\n    subProjectBoxPara: 16,\n    subProjectButtons: 18,\n    clientSection: 36,\n    testimonialsHead: 36,\n    testimonialsHeading: 20,\n    testimonialsPosition: 12,\n    testimonialsQuote: 14,\n    testimonialsCompany: 16,\n    // about\n    aboutMainPara: 26,\n    aboutPaddingX: 50,\n    aboutVideoW: 639,\n    aboutVideoH: 457,\n    // video size but not applied to the video\n    aboutCardPaddingY: 40,\n    aboutCardPaddingX: 30\n  },\n  phone: {\n    // home -- some of them are used in other pages due similarity\n    mainHeading: 150,\n    mainPara: 48,\n    mainButton: 50,\n    markDownHead: 36,\n    markDownPara: 15,\n    markDownButton: 70,\n    serviceHeading: 36,\n    services: 20,\n    experienceCount: 40,\n    experienceTitle: 12,\n    experienceHeading: 60,\n    experiencePara: 50,\n    experienceButton: 18,\n    subProjectTopButton: 16,\n    subProjectHeadings: 32,\n    subProjectParas: 16,\n    subProjectBoxHeading: 20,\n    subProjectBoxPara: 16,\n    subProjectButtons: 18,\n    clientSection: 90,\n    testimonialsHead: 36,\n    testimonialsHeading: 20,\n    testimonialsPosition: 12,\n    testimonialsQuote: 14,\n    testimonialsCompany: 16,\n    // about\n    aboutMainPara: 70,\n    aboutPaddingX: 20,\n    aboutVideoW: 639,\n    aboutVideoH: 457,\n    // video size but not applied to the video\n    aboutCardPaddingY: 100,\n    aboutCardPaddingX: 60\n  }\n};\nexport const generatefontSize = (condition, fn, w) => {\n  const result = {};\n  for (const key in fontSizes.computer) {\n    result[key] = fn(fontSizes[condition][key], w);\n  }\n  return result;\n};", "map": {"version": 3, "names": ["dynamicSize", "size", "width", "toFixed", "differentText", "highlight1", "checkDifference", "arg1", "arg2", "highlight", "Array", "isArray", "for<PERSON>ach", "e", "i", "id", "_this$highlight", "defineDevice", "screen", "fontSizes", "computer", "mainHeading", "mainPara", "mainButton", "markDownHead", "markDownPara", "markDownButton", "serviceHeading", "services", "experienceCount", "experienceTitle", "experienceHeading", "experiencePara", "experienceButton", "subProjectTopButton", "subProjectHeadings", "subProjectParas", "subProjectBoxHeading", "subProjectBoxPara", "subProjectButtons", "clientSection", "testimonialsHead", "testimonialsHeading", "testimonialsPosition", "testimonialsQuote", "testimonialsCompany", "about<PERSON>ain<PERSON>ara", "aboutPaddingX", "aboutVideoW", "aboutVideoH", "aboutCardPaddingY", "aboutCardPaddingX", "tablet", "phone", "generatefontSize", "condition", "fn", "w", "result", "key"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/shade_cms/dashboard/src/app/fontSizes.js"], "sourcesContent": ["export default function dynamicSize(size, width) {\r\n    return `${(width / 1532 * size).toFixed(0)}px`\r\n}\r\n\r\nexport const differentText = {\r\n    highlight1: \"border-green-600 border\",\r\n\r\n    checkDifference: function (arg1, arg2, highlight) {\r\n        if (Array.isArray(arg1) && Array.isArray(arg2)) {\r\n            return arg1.forEach((e, i) => e.id === arg2[i].id)\r\n        }\r\n\r\n        if (arg1 !== arg2) {\r\n            return this?.[highlight] ?? this?.highlight1\r\n        } else return \"\"\r\n    }\r\n}\r\n\r\nexport function defineDevice(screen) {\r\n    if (screen > 900) {\r\n        return \"computer\"\r\n    } else if (screen < 900 && screen > 550) {\r\n        return 'tablet'\r\n    } else {\r\n        return 'phone'\r\n    }\r\n}\r\n\r\n// const fontKeys = [\r\n//     \"mainHeading\", \"mainPara\", \"mainButton\",\r\n//     \"markDownHead\", \"markDownPara\", \"markDownButton\",\r\n//     \"serviceHeading\", \"services\",\r\n//     \"experienceCount\", \"experienceTitle\",\r\n//     \"experienceHeading\", \"experiencePara\", \"experienceButton\",\r\n//     \"subProjectTopButton\", \"subProjectHeadings\", \"subProjectParas\",\r\n//     \"subProjectBoxHeading\", \"subProjectBoxPara\",\r\n//     \"subProjectButtons\",\r\n//     \"clientSection\",\r\n//     \"testimonialsHead\", \"testimonialsHeading\", \"testimonialsPosition\",\r\n//     \"testimonialsQuote\", \"testimonialsCompany\", 'aboutMainPara'\r\n// ];\r\n\r\nconst fontSizes = {\r\n    computer: {\r\n        // home -- some of them are used in other pages due similarity\r\n        mainHeading: 70, mainPara: 16, mainButton: 18, markDownHead: 36, markDownPara: 15, markDownButton: 70,\r\n        serviceHeading: 36, services: 20, experienceCount: 40, experienceTitle: 12, experienceHeading: 60,\r\n        experiencePara: 16, experienceButton: 18, subProjectTopButton: 16, subProjectHeadings: 32,\r\n        subProjectParas: 16, subProjectBoxHeading: 20, subProjectBoxPara: 16, subProjectButtons: 18,\r\n        clientSection: 36, testimonialsHead: 36, testimonialsHeading: 20, testimonialsPosition: 12,\r\n        testimonialsQuote: 14, testimonialsCompany: 16,\r\n        // about\r\n        aboutMainPara: 26, aboutPaddingX: 150,\r\n        aboutVideoW: 639, aboutVideoH: 457, // video size but not applied to the video\r\n        aboutCardPaddingY: 40, aboutCardPaddingX: 10\r\n    },\r\n    tablet: {\r\n        // home -- some of them are used in other pages due similarity\r\n        mainHeading: 70, mainPara: 16, mainButton: 18, markDownHead: 36, markDownPara: 15, markDownButton: 70,\r\n        serviceHeading: 36, services: 20, experienceCount: 40, experienceTitle: 12, experienceHeading: 60,\r\n        experiencePara: 16, experienceButton: 18, subProjectTopButton: 16, subProjectHeadings: 32,\r\n        subProjectParas: 16, subProjectBoxHeading: 20, subProjectBoxPara: 16, subProjectButtons: 18,\r\n        clientSection: 36, testimonialsHead: 36, testimonialsHeading: 20, testimonialsPosition: 12,\r\n        testimonialsQuote: 14, testimonialsCompany: 16,\r\n        // about\r\n        aboutMainPara: 26, aboutPaddingX: 50,\r\n        aboutVideoW: 639, aboutVideoH: 457, // video size but not applied to the video\r\n        aboutCardPaddingY: 40, aboutCardPaddingX: 30\r\n    },\r\n    phone: {\r\n        // home -- some of them are used in other pages due similarity\r\n        mainHeading: 150, mainPara: 48, mainButton: 50, markDownHead: 36, markDownPara: 15, markDownButton: 70,\r\n        serviceHeading: 36, services: 20, experienceCount: 40, experienceTitle: 12, experienceHeading: 60,\r\n        experiencePara: 50, experienceButton: 18, subProjectTopButton: 16, subProjectHeadings: 32,\r\n        subProjectParas: 16, subProjectBoxHeading: 20, subProjectBoxPara: 16, subProjectButtons: 18,\r\n        clientSection: 90, testimonialsHead: 36, testimonialsHeading: 20, testimonialsPosition: 12,\r\n        testimonialsQuote: 14, testimonialsCompany: 16,\r\n        // about\r\n        aboutMainPara: 70, aboutPaddingX: 20,\r\n        aboutVideoW: 639, aboutVideoH: 457, // video size but not applied to the video\r\n        aboutCardPaddingY: 100, aboutCardPaddingX: 60\r\n    }\r\n};\r\n\r\nexport const generatefontSize = (condition, fn, w) => {\r\n    const result = {};\r\n    for (const key in fontSizes.computer) {\r\n        result[key] = fn(fontSizes[condition][key], w);\r\n    }\r\n    return result;\r\n};"], "mappings": "AAAA,eAAe,SAASA,WAAWA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC7C,OAAO,GAAG,CAACA,KAAK,GAAG,IAAI,GAAGD,IAAI,EAAEE,OAAO,CAAC,CAAC,CAAC,IAAI;AAClD;AAEA,OAAO,MAAMC,aAAa,GAAG;EACzBC,UAAU,EAAE,yBAAyB;EAErCC,eAAe,EAAE,SAAAA,CAAUC,IAAI,EAAEC,IAAI,EAAEC,SAAS,EAAE;IAC9C,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,IAAIG,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;MAC5C,OAAOD,IAAI,CAACK,OAAO,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,EAAE,KAAKP,IAAI,CAACM,CAAC,CAAC,CAACC,EAAE,CAAC;IACtD;IAEA,IAAIR,IAAI,KAAKC,IAAI,EAAE;MAAA,IAAAQ,eAAA;MACf,QAAAA,eAAA,GAAO,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAGP,SAAS,CAAC,cAAAO,eAAA,cAAAA,eAAA,GAAI,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAEX,UAAU;IAChD,CAAC,MAAM,OAAO,EAAE;EACpB;AACJ,CAAC;AAED,OAAO,SAASY,YAAYA,CAACC,MAAM,EAAE;EACjC,IAAIA,MAAM,GAAG,GAAG,EAAE;IACd,OAAO,UAAU;EACrB,CAAC,MAAM,IAAIA,MAAM,GAAG,GAAG,IAAIA,MAAM,GAAG,GAAG,EAAE;IACrC,OAAO,QAAQ;EACnB,CAAC,MAAM;IACH,OAAO,OAAO;EAClB;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,SAAS,GAAG;EACdC,QAAQ,EAAE;IACN;IACAC,WAAW,EAAE,EAAE;IAAEC,QAAQ,EAAE,EAAE;IAAEC,UAAU,EAAE,EAAE;IAAEC,YAAY,EAAE,EAAE;IAAEC,YAAY,EAAE,EAAE;IAAEC,cAAc,EAAE,EAAE;IACrGC,cAAc,EAAE,EAAE;IAAEC,QAAQ,EAAE,EAAE;IAAEC,eAAe,EAAE,EAAE;IAAEC,eAAe,EAAE,EAAE;IAAEC,iBAAiB,EAAE,EAAE;IACjGC,cAAc,EAAE,EAAE;IAAEC,gBAAgB,EAAE,EAAE;IAAEC,mBAAmB,EAAE,EAAE;IAAEC,kBAAkB,EAAE,EAAE;IACzFC,eAAe,EAAE,EAAE;IAAEC,oBAAoB,EAAE,EAAE;IAAEC,iBAAiB,EAAE,EAAE;IAAEC,iBAAiB,EAAE,EAAE;IAC3FC,aAAa,EAAE,EAAE;IAAEC,gBAAgB,EAAE,EAAE;IAAEC,mBAAmB,EAAE,EAAE;IAAEC,oBAAoB,EAAE,EAAE;IAC1FC,iBAAiB,EAAE,EAAE;IAAEC,mBAAmB,EAAE,EAAE;IAC9C;IACAC,aAAa,EAAE,EAAE;IAAEC,aAAa,EAAE,GAAG;IACrCC,WAAW,EAAE,GAAG;IAAEC,WAAW,EAAE,GAAG;IAAE;IACpCC,iBAAiB,EAAE,EAAE;IAAEC,iBAAiB,EAAE;EAC9C,CAAC;EACDC,MAAM,EAAE;IACJ;IACA/B,WAAW,EAAE,EAAE;IAAEC,QAAQ,EAAE,EAAE;IAAEC,UAAU,EAAE,EAAE;IAAEC,YAAY,EAAE,EAAE;IAAEC,YAAY,EAAE,EAAE;IAAEC,cAAc,EAAE,EAAE;IACrGC,cAAc,EAAE,EAAE;IAAEC,QAAQ,EAAE,EAAE;IAAEC,eAAe,EAAE,EAAE;IAAEC,eAAe,EAAE,EAAE;IAAEC,iBAAiB,EAAE,EAAE;IACjGC,cAAc,EAAE,EAAE;IAAEC,gBAAgB,EAAE,EAAE;IAAEC,mBAAmB,EAAE,EAAE;IAAEC,kBAAkB,EAAE,EAAE;IACzFC,eAAe,EAAE,EAAE;IAAEC,oBAAoB,EAAE,EAAE;IAAEC,iBAAiB,EAAE,EAAE;IAAEC,iBAAiB,EAAE,EAAE;IAC3FC,aAAa,EAAE,EAAE;IAAEC,gBAAgB,EAAE,EAAE;IAAEC,mBAAmB,EAAE,EAAE;IAAEC,oBAAoB,EAAE,EAAE;IAC1FC,iBAAiB,EAAE,EAAE;IAAEC,mBAAmB,EAAE,EAAE;IAC9C;IACAC,aAAa,EAAE,EAAE;IAAEC,aAAa,EAAE,EAAE;IACpCC,WAAW,EAAE,GAAG;IAAEC,WAAW,EAAE,GAAG;IAAE;IACpCC,iBAAiB,EAAE,EAAE;IAAEC,iBAAiB,EAAE;EAC9C,CAAC;EACDE,KAAK,EAAE;IACH;IACAhC,WAAW,EAAE,GAAG;IAAEC,QAAQ,EAAE,EAAE;IAAEC,UAAU,EAAE,EAAE;IAAEC,YAAY,EAAE,EAAE;IAAEC,YAAY,EAAE,EAAE;IAAEC,cAAc,EAAE,EAAE;IACtGC,cAAc,EAAE,EAAE;IAAEC,QAAQ,EAAE,EAAE;IAAEC,eAAe,EAAE,EAAE;IAAEC,eAAe,EAAE,EAAE;IAAEC,iBAAiB,EAAE,EAAE;IACjGC,cAAc,EAAE,EAAE;IAAEC,gBAAgB,EAAE,EAAE;IAAEC,mBAAmB,EAAE,EAAE;IAAEC,kBAAkB,EAAE,EAAE;IACzFC,eAAe,EAAE,EAAE;IAAEC,oBAAoB,EAAE,EAAE;IAAEC,iBAAiB,EAAE,EAAE;IAAEC,iBAAiB,EAAE,EAAE;IAC3FC,aAAa,EAAE,EAAE;IAAEC,gBAAgB,EAAE,EAAE;IAAEC,mBAAmB,EAAE,EAAE;IAAEC,oBAAoB,EAAE,EAAE;IAC1FC,iBAAiB,EAAE,EAAE;IAAEC,mBAAmB,EAAE,EAAE;IAC9C;IACAC,aAAa,EAAE,EAAE;IAAEC,aAAa,EAAE,EAAE;IACpCC,WAAW,EAAE,GAAG;IAAEC,WAAW,EAAE,GAAG;IAAE;IACpCC,iBAAiB,EAAE,GAAG;IAAEC,iBAAiB,EAAE;EAC/C;AACJ,CAAC;AAED,OAAO,MAAMG,gBAAgB,GAAGA,CAACC,SAAS,EAAEC,EAAE,EAAEC,CAAC,KAAK;EAClD,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,KAAK,MAAMC,GAAG,IAAIxC,SAAS,CAACC,QAAQ,EAAE;IAClCsC,MAAM,CAACC,GAAG,CAAC,GAAGH,EAAE,CAACrC,SAAS,CAACoC,SAAS,CAAC,CAACI,GAAG,CAAC,EAAEF,CAAC,CAAC;EAClD;EACA,OAAOC,MAAM;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}