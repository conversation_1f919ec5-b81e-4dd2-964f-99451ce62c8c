{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\Akshay\\\\shade_cms\\\\dashboard\\\\src\\\\index.js\";\nimport React, { Suspense } from \"react\";\nimport ReactDOM from \"react-dom/client\";\nimport \"./index.css\";\nimport App from \"./App\";\nimport reportWebVitals from \"./reportWebVitals\";\nimport store from \"./app/store\";\nimport { Provider } from \"react-redux\";\nimport SuspenseContent from \"./containers/SuspenseContent\";\nimport { ScrollProvider } from \"./features/Context/Context\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst root = ReactDOM.createRoot(document.getElementById(\"root\"));\nroot.render(/*#__PURE__*/_jsxDEV(Suspense, {\n  fallback: /*#__PURE__*/_jsxDEV(SuspenseContent, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 23\n  }, this),\n  children: /*#__PURE__*/_jsxDEV(Provider, {\n    store: store,\n    children: /*#__PURE__*/_jsxDEV(ScrollProvider, {\n      children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 14,\n  columnNumber: 3\n}, this));\nreportWebVitals();", "map": {"version": 3, "names": ["React", "Suspense", "ReactDOM", "App", "reportWebVitals", "store", "Provider", "SuspenseContent", "ScrollProvider", "jsxDEV", "_jsxDEV", "root", "createRoot", "document", "getElementById", "render", "fallback", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/Akshay/shade_cms/dashboard/src/index.js"], "sourcesContent": ["import React, { Suspense } from \"react\";\r\nimport ReactDOM from \"react-dom/client\";\r\nimport \"./index.css\";\r\nimport App from \"./App\";\r\nimport reportWebVitals from \"./reportWebVitals\";\r\nimport store from \"./app/store\";\r\nimport { Provider } from \"react-redux\";\r\nimport SuspenseContent from \"./containers/SuspenseContent\";\r\nimport { ScrollProvider } from \"./features/Context/Context\";\r\n\r\nconst root = ReactDOM.createRoot(document.getElementById(\"root\"));\r\nroot.render(\r\n\r\n  <Suspense fallback={<SuspenseContent />}>\r\n    <Provider store={store}>\r\n      <ScrollProvider>\r\n        <App />\r\n      </ScrollProvider>\r\n    </Provider>\r\n  </Suspense>\r\n);\r\n\r\nreportWebVitals();\r\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,OAAO;AACvB,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,KAAK,MAAM,aAAa;AAC/B,SAASC,QAAQ,QAAQ,aAAa;AACtC,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,SAASC,cAAc,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,IAAI,GAAGT,QAAQ,CAACU,UAAU,CAACC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAAC,CAAC;AACjEH,IAAI,CAACI,MAAM,cAETL,OAAA,CAACT,QAAQ;EAACe,QAAQ,eAAEN,OAAA,CAACH,eAAe;IAAAU,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAE;EAAAC,QAAA,eACtCX,OAAA,CAACJ,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAgB,QAAA,eACrBX,OAAA,CAACF,cAAc;MAAAa,QAAA,eACbX,OAAA,CAACP,GAAG;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACZ,CAAC;AAEDhB,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}